# 🚀 RiftStays Complete Integrations Guide

## 📋 **Overview**

RiftStays now includes comprehensive integrations for a complete booking and payment platform supporting:

- **Tours & Experiences** 🎯
- **Houses** 🏠  
- **Hotels** 🏨
- **Airbnbs** 🏡
- **M-Pesa Payments** 💳
- **Booking Management** 📅
- **Email Notifications** 📧

---

## 💳 **1. M-Pesa Payment Integration**

### **Setup Instructions:**

1. **Get Safaricom Daraja API Credentials:**
   - Visit [Safaricom Developer Portal](https://developer.safaricom.co.ke/)
   - Create an app and get Consumer Key & Secret
   - Get your Business Short Code and Passkey

2. **Environment Variables:**
```env
MPESA_CONSUMER_KEY=your_consumer_key
MPESA_CONSUMER_SECRET=your_consumer_secret
MPESA_ENVIRONMENT=sandbox  # or 'production'
MPESA_SHORT_CODE=174379    # Your business short code
MPESA_PASSKEY=your_passkey
MPESA_CALLBACK_URL=https://yourdomain.com/api/payments/mpesa/callback
```

3. **Features:**
   - ✅ STK Push payments
   - ✅ Payment status tracking
   - ✅ Automatic booking confirmation
   - ✅ Transaction receipts
   - ✅ Callback handling

### **Usage:**
```typescript
import { mpesaService } from '@/lib/mpesa'

// Initiate payment
const payment = await mpesaService.initiateSTKPush({
  phoneNumber: '+************',
  amount: 12000,
  accountReference: 'RS20241201ABCD',
  transactionDesc: 'Tour booking payment'
})
```

---

## 📅 **2. Booking System**

### **Supported Booking Types:**

#### **Tours & Experiences**
- Wildlife safaris
- Cultural tours  
- City tours
- Adventure activities
- Nature walks
- Beach activities

#### **Accommodations**
- Private houses
- Hotels
- Airbnb properties
- Vacation rentals

### **Booking Features:**
- ✅ Multi-step booking form
- ✅ Guest management (adults, children, infants)
- ✅ Date/time selection
- ✅ Pricing calculation
- ✅ Additional services (airport transfer, car rental, insurance)
- ✅ Special requests
- ✅ Availability checking
- ✅ Booking validation

### **API Endpoints:**

```typescript
// Create booking
POST /api/bookings
{
  "type": "tour",
  "tourId": "tour_id",
  "tourDate": "2024-12-15",
  "tourTime": "06:00",
  "guests": { "adults": 2, "children": 0, "infants": 0 },
  "contact": { ... },
  "additionalServices": { ... }
}

// Initiate payment
POST /api/payments/mpesa
{
  "bookingId": "booking_id",
  "phoneNumber": "+************"
}
```

---

## 🎯 **3. Tours Management**

### **Tour Schema Features:**
- Tour details (title, description, images)
- Pricing and duration
- Difficulty levels (easy, moderate, challenging)
- Categories (wildlife, cultural, adventure, etc.)
- Guest capacity (min/max)
- Detailed itinerary
- Includes/excludes lists
- Meeting point and cancellation policy
- Availability settings (days, times, blackout dates)

### **Tour Pages:**
- `/tours` - Browse all tours
- `/tours/[slug]` - Tour details with booking
- Filtering by category, difficulty, duration
- Interactive booking form
- Real-time availability checking

---

## 🏠 **4. Property Management**

### **Enhanced Property Features:**
- Multiple property types (house, hotel, airbnb)
- Advanced booking calendar
- Room management
- Pricing rules
- Availability tracking
- Guest capacity management

### **Property Pages:**
- `/properties` - Browse all properties
- `/properties/[slug]` - Property details with booking
- Advanced filtering and search
- Interactive booking calendar

---

## 📧 **5. Email Notifications**

### **Automated Emails:**
- Booking confirmation
- Payment receipts
- Booking reminders
- Cancellation notifications
- Customer support

### **Email Templates:**
Located in `/src/lib/email-templates/`
- Booking confirmation
- Payment receipt
- Tour reminder
- Cancellation notice

---

## 🗄️ **6. Database Schema (Sanity)**

### **New Content Types:**

#### **Tour Schema:**
```typescript
{
  title: string
  slug: slug
  description: text
  images: array[image]
  price: number
  duration: number
  maxGuests: number
  difficulty: 'easy' | 'moderate' | 'challenging'
  category: string
  location: string
  itinerary: array[object]
  availability: object
  // ... more fields
}
```

#### **Booking Schema:**
```typescript
{
  bookingNumber: string
  type: 'tour' | 'house' | 'hotel' | 'airbnb'
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed'
  contact: object
  guests: object
  pricing: object
  payment: object
  // ... more fields
}
```

---

## 🔧 **7. Setup Instructions**

### **1. Install Dependencies:**
```bash
npm install
```

### **2. Environment Setup:**
```bash
cp .env.example .env.local
# Fill in your credentials
```

### **3. Sanity Setup:**
```bash
# Add new schemas to Sanity Studio
# Deploy schemas: npm run sanity:deploy
```

### **4. M-Pesa Setup:**
1. Register with Safaricom Daraja API
2. Get sandbox credentials for testing
3. Configure callback URL
4. Test with sandbox environment

### **5. Production Deployment:**
1. Update environment variables
2. Configure production M-Pesa credentials
3. Set up SSL certificate for callbacks
4. Configure email service

---

## 📱 **8. User Journey**

### **Tour Booking Flow:**
1. Browse tours (`/tours`)
2. Select tour (`/tours/[slug]`)
3. Fill booking form (dates, guests, contact)
4. Add additional services
5. Create booking (`/api/bookings`)
6. Payment page (`/booking/[id]/payment`)
7. M-Pesa payment process
8. Confirmation page (`/booking/[id]/confirmation`)
9. Email confirmation

### **Property Booking Flow:**
1. Browse properties (`/properties`)
2. Select property (`/properties/[slug]`)
3. Choose dates and guests
4. Fill contact information
5. Add services and special requests
6. Create booking and payment
7. Confirmation and email

---

## 🔐 **9. Security Features**

- ✅ Input validation and sanitization
- ✅ CSRF protection
- ✅ Secure payment processing
- ✅ Environment variable protection
- ✅ API rate limiting ready
- ✅ Error handling and logging

---

## 📊 **10. Analytics & Monitoring**

### **Performance Tracking:**
- Booking conversion rates
- Payment success rates
- Popular tours and properties
- User behavior analytics

### **Business Metrics:**
- Revenue tracking
- Booking trends
- Customer satisfaction
- Operational efficiency

---

## 🚀 **11. Next Steps**

### **Immediate:**
1. Set up M-Pesa sandbox account
2. Configure environment variables
3. Test booking flow
4. Set up email service

### **Production Ready:**
1. Get production M-Pesa credentials
2. Configure domain and SSL
3. Set up monitoring and alerts
4. Launch marketing campaigns

---

## 📞 **12. Support**

### **M-Pesa Support:**
- Safaricom Developer Portal
- API Documentation
- Sandbox testing environment

### **Technical Support:**
- Comprehensive error logging
- Payment status tracking
- Booking management dashboard
- Customer support tools

---

## ✅ **Integration Checklist**

- [x] M-Pesa payment integration
- [x] Tour booking system
- [x] Property booking system
- [x] Multi-step booking forms
- [x] Payment processing
- [x] Booking confirmation
- [x] Email notifications (framework)
- [x] Database schemas
- [x] API endpoints
- [x] Security measures
- [x] Error handling
- [x] Performance optimization

**🎉 RiftStays is now a complete booking platform ready for tours, accommodations, and payments!**
