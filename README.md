# 🏠 RiftStays - Performance-Optimized Accommodation Platform

A high-performance Next.js 15 application for discovering and booking luxury accommodations. Built with modern web technologies and optimized for lightning-fast loading speeds.

## ⚡ Performance Features

### Core Web Vitals Optimization
- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1
- **FCP (First Contentful Paint)**: < 1.8s
- **TTFB (Time to First Byte)**: < 800ms

### Advanced Optimizations
- 🚀 **Next.js 15** with Turbopack for faster builds
- 🖼️ **Advanced Image Optimization** with AVIF/WebP support
- 📦 **Bundle Splitting** and code optimization
- 🔄 **Service Worker** for offline functionality
- 📱 **PWA Support** with app-like experience
- ⚡ **Lazy Loading** with Intersection Observer
- 🎯 **Resource Preloading** for critical assets
- 📊 **Performance Monitoring** with Web Vitals

## 🛠️ Tech Stack

- **Framework**: Next.js 15.3.3 with App Router
- **Language**: TypeScript 5.8.3
- **Styling**: Tailwind CSS 4.1.8
- **CMS**: Sanity.io with optimized queries
- **Performance**: Web Vitals, Bundle Analyzer
- **PWA**: Service Worker, Web App Manifest

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm/yarn/pnpm

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd riftstays

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Add your Sanity project credentials

# Run development server with Turbopack
npm run dev
```

### Development Scripts

```bash
# Development with Turbopack (faster)
npm run dev

# Production build
npm run build

# Start production server
npm run start

# Type checking
npm run type-check

# Linting
npm run lint

# Bundle analysis
npm run analyze

# Performance audit
npm run audit:performance

# Security audit
npm run audit:security
```

## 📊 Performance Monitoring

### Built-in Monitoring
- Real-time Web Vitals tracking
- Long task detection
- Layout shift monitoring
- Resource loading analysis

### Performance Scripts
```bash
# Generate Lighthouse report
npm run audit:performance

# Analyze bundle size
npm run analyze

# Production build with optimizations
npm run build:production
```

## 🎯 Optimization Features

### Image Optimization
- **Responsive Images**: Multiple sizes for different viewports
- **Modern Formats**: AVIF and WebP with fallbacks
- **Lazy Loading**: Intersection Observer-based loading
- **Blur Placeholders**: Smooth loading transitions
- **Sanity CDN**: Optimized image delivery

### Code Splitting
- **Route-based**: Automatic page-level splitting
- **Component-based**: Dynamic imports for heavy components
- **Vendor Splitting**: Separate chunks for third-party libraries
- **Framework Splitting**: React/Next.js in separate bundles

### Caching Strategy
- **Static Assets**: 1 year cache with immutable headers
- **API Responses**: 1 hour cache with stale-while-revalidate
- **Pages**: 24 hour cache for static content
- **Service Worker**: Offline-first caching

### PWA Features
- **Offline Support**: Cached pages and assets
- **App-like Experience**: Standalone display mode
- **Fast Installation**: Add to home screen
- **Background Sync**: Offline form submissions

## 🔧 Configuration

### Performance Config
See `performance.config.js` for detailed performance settings:
- Web Vitals thresholds
- Image optimization settings
- Bundle size limits
- Caching strategies

### Next.js Config
Optimized `next.config.ts` includes:
- Turbopack configuration
- Image optimization
- Bundle splitting
- Security headers
- Compression settings

## 📱 PWA Setup

The app includes full PWA support:
- Web App Manifest (`/manifest.json`)
- Service Worker (`/sw.js`)
- Offline page (`/offline`)
- App icons and splash screens

## 🔍 SEO & Accessibility

- **Semantic HTML**: Proper heading structure and landmarks
- **Meta Tags**: Dynamic Open Graph and Twitter cards
- **Structured Data**: JSON-LD for rich snippets
- **Accessibility**: WCAG 2.1 AA compliance
- **Core Web Vitals**: Optimized for search ranking

## 📈 Analytics Integration

Ready for analytics integration:
- Google Analytics 4 events
- Performance metrics tracking
- User interaction monitoring
- Custom event tracking

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Deploy to Vercel
vercel --prod
```

### Other Platforms
The app is optimized for deployment on:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

## 🔧 Environment Variables

```env
# Sanity Configuration
NEXT_PUBLIC_SANITY_PROJECT_ID=your_project_id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_READ_TOKEN=your_read_token

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://riftstays.com

# Analytics (Optional)
NEXT_PUBLIC_GA_ID=your_ga_id
```

## 📊 Performance Benchmarks

### Lighthouse Scores (Target)
- **Performance**: 95+
- **Accessibility**: 100
- **Best Practices**: 100
- **SEO**: 100

### Bundle Sizes (Gzipped)
- **Initial Bundle**: < 250KB
- **Total JavaScript**: < 500KB
- **First Load JS**: < 300KB

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Run performance tests
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
