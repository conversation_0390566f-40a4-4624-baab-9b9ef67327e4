# RiftStays Schema Documentation

## Overview

This document provides comprehensive documentation for the RiftStays schema system, including all content types, their fields, relationships, and usage patterns.

## Schema Architecture

The RiftStays platform uses Sanity CMS as the content management system with the following main content types:

- **Properties** - Accommodation listings (houses, apartments, villas, cabins)
- **Tours** - Tour and activity offerings
- **Bookings** - Booking records for properties and tours
- **Users** - User accounts and profiles
- **Settings** - Site configuration and settings
- **Blog Posts** - Content marketing and blog articles
- **Authors** - Blog post authors
- **Categories** - Content categorization

## Content Types

### 1. Property Schema

**Type:** `property`

**Core Fields:**
- `title` (string, required) - Property name
- `slug` (slug, required) - URL-friendly identifier
- `description` (text, required) - Detailed property description
- `price` (number, required) - Price per night in KES
- `location` (string, required) - General location
- `address` (text, optional) - Full address
- `coordinates` (object, optional) - GPS coordinates
  - `lat` (number) - Latitude
  - `lng` (number) - Longitude

**Property Details:**
- `bedrooms` (number, required) - Number of bedrooms
- `bathrooms` (number, required) - Number of bathrooms
- `maxGuests` (number, required) - Maximum occupancy
- `category` (string, required) - beach | mountain | city | countryside
- `propertyType` (string, required) - house | apartment | villa | cabin

**Features:**
- `amenities` (array of strings) - Available amenities
- `rules` (array of strings) - House rules
- `images` (array of images, required) - Property photos with alt text
- `availability` (array of objects) - Available date ranges

**Management:**
- `featured` (boolean) - Featured property flag
- `status` (string) - available | booked | maintenance | inactive
- `verified` (boolean) - Verification status
- `owner` (object) - Owner information
- `contact` (object) - Contact and check-in details
- `pricing` (object) - Pricing details and fees
- `seo` (object) - SEO metadata
- `analytics` (object) - Performance metrics

### 2. Tour Schema

**Type:** `tour`

**Core Fields:**
- `title` (string, required) - Tour name
- `slug` (slug, required) - URL-friendly identifier
- `description` (text, required) - Detailed tour description
- `shortDescription` (text, optional) - Brief description for listings
- `price` (number, required) - Tour price in KES
- `duration` (number, required) - Duration in hours
- `location` (string, required) - Tour location

**Tour Details:**
- `maxGuests` (number, required) - Maximum participants
- `minGuests` (number, required) - Minimum participants
- `difficulty` (string, required) - easy | moderate | challenging | expert
- `category` (string, required) - Tour category
- `coordinates` (object, optional) - GPS coordinates

**Content:**
- `images` (array of images, required) - Tour photos
- `includes` (array of strings) - What's included
- `excludes` (array of strings) - What's not included
- `itinerary` (array of objects) - Detailed schedule
- `meetingPoint` (text, required) - Meeting location

**Operations:**
- `availability` (object) - Available days, times, blackout dates
- `guide` (object) - Guide information
- `safety` (object) - Safety requirements and equipment
- `booking` (object) - Booking settings and discounts
- `cancellationPolicy` (text) - Cancellation terms

**Management:**
- `featured` (boolean) - Featured tour flag
- `active` (boolean) - Active status
- `seo` (object) - SEO metadata

### 3. Booking Schema

**Type:** `booking`

**Core Fields:**
- `bookingNumber` (string, required) - Unique booking identifier
- `type` (string, required) - tour | house | hotel | airbnb
- `status` (string, required) - pending | confirmed | cancelled | completed | refunded

**Customer Information:**
- `contact` (object, required) - Customer contact details
- `guests` (object, required) - Guest count breakdown

**Booking Details:**
- `tourBooking` (object, conditional) - Tour-specific details
- `accommodationBooking` (object, conditional) - Property-specific details

**Financial:**
- `pricing` (object, required) - Price breakdown
- `payment` (object, required) - Payment details and status
- `additionalServices` (object, optional) - Extra services

**Management:**
- `customerNotes` (text, optional) - Customer requests
- `internalNotes` (text, optional) - Staff notes
- `createdAt` (datetime, required) - Creation timestamp
- `updatedAt` (datetime, optional) - Last update
- `confirmedAt` (datetime, optional) - Confirmation timestamp
- `cancelledAt` (datetime, optional) - Cancellation timestamp

### 4. User Schema

**Type:** `user`

**Core Fields:**
- `email` (string, required) - User email (unique)
- `firstName` (string, required) - First name
- `lastName` (string, required) - Last name
- `role` (string, required) - admin | employee | customer
- `status` (string, required) - active | inactive | suspended

**Profile:**
- `avatar` (image, optional) - Profile photo
- `phone` (string, optional) - Phone number
- `department` (string, optional) - For staff users
- `profile` (object, optional) - Extended profile information
- `preferences` (object, optional) - User preferences
- `verification` (object, optional) - Verification status

**Access Control:**
- `permissions` (array of objects) - Role-based permissions

### 5. Settings Schema

**Type:** `settings`

**Site Configuration:**
- `title` (string, required) - Site title
- `description` (text, required) - Site description
- `logo` (image, optional) - Site logo
- `twitterHandle` (string, optional) - Twitter handle

**SEO:**
- `seo` (object) - Default SEO settings

**Contact:**
- `contactInfo` (object) - Contact information

**Payment:**
- `paymentSettings` (object) - Payment gateway configuration

**Booking:**
- `bookingSettings` (object) - Booking system configuration

## Relationships

### Property → User
- `owner.userId` references User document
- Establishes property ownership

### Booking → Property/Tour
- `accommodationBooking.propertyId` references Property
- `tourBooking.tourId` references Tour
- Links bookings to their respective items

### Blog Post → Author
- `author` references Author document
- Links posts to their authors

### Blog Post → Category
- `categories` references Category documents
- Categorizes blog content

## Validation Rules

### Property Validation
- Title: Required, minimum 1 character
- Price: Required, must be positive
- Bedrooms/Bathrooms: Required, minimum 0
- Max Guests: Required, minimum 1
- Images: Required, minimum 1 image
- Owner email: Must be valid email format

### Tour Validation
- Title: Required, minimum 1 character
- Price: Required, must be positive
- Duration: Required, minimum 1 hour
- Min/Max Guests: Required, minimum 1
- Images: Required, minimum 1 image
- Meeting Point: Required

### Booking Validation
- Contact email: Must be valid email format
- Guest count: At least 1 adult required
- Dates: Check-out must be after check-in
- Tour bookings: Must have date and time
- Property bookings: Must have check-in/out dates

### User Validation
- Email: Required, must be valid email format
- Names: Required, minimum 1 character
- Role: Must be valid role value
- Phone: Must match Kenyan phone format (when provided)

## API Queries

### Get Properties
```groq
*[_type == "property" && status == "available"] | order(featured desc, createdAt desc) {
  _id, title, slug, description, price, location, bedrooms, bathrooms,
  maxGuests, category, propertyType, images, featured, status, verified
}
```

### Get Tours
```groq
*[_type == "tour" && active == true] | order(featured desc, createdAt desc) {
  _id, title, slug, description, shortDescription, price, duration,
  maxGuests, difficulty, category, location, images, featured
}
```

### Get Bookings
```groq
*[_type == "booking"] | order(createdAt desc) {
  _id, bookingNumber, type, status, contact, pricing, createdAt
}
```

## Performance Considerations

1. **Indexing**: Key fields like `slug`, `status`, `featured` are indexed
2. **Image Optimization**: All images use Sanity's built-in optimization
3. **Query Optimization**: Queries include only necessary fields
4. **Caching**: API responses are cached where appropriate

## Security

1. **Access Control**: Role-based permissions system
2. **Data Validation**: Server-side validation using Zod schemas
3. **Sensitive Data**: Payment details and passwords are handled securely
4. **API Authentication**: Protected endpoints require valid tokens

## Migration and Updates

When updating schemas:
1. Test changes in development environment
2. Run data migration scripts if needed
3. Update TypeScript types
4. Update validation schemas
5. Update API queries
6. Deploy changes incrementally

## Monitoring

Key metrics to monitor:
- Schema validation errors
- Query performance
- Data consistency
- User activity patterns
- Booking conversion rates

## Support

For schema-related issues:
1. Check validation errors in Sanity Studio
2. Review TypeScript type definitions
3. Verify API query syntax
4. Check data relationships
5. Contact development team for complex issues
