/** @type {import('next').NextConfig} */
const nextConfig = {
  // Minimal stable configuration
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.sanity.io',
        port: '',
        pathname: '/images/**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
  },

  // Enable static optimization
  output: 'standalone',

  // Disable ESLint during build to focus on runtime issues
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Basic webpack configuration
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
    }
    return config
  },
}

module.exports = nextConfig
