{"name": "riftstays", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "build:fast": "SKIP_ENV_VALIDATION=true next build", "start": "next start", "lint": "next lint", "analyze": "ANALYZE=true npm run build", "build:analyze": "cross-env ANALYZE=true npm run build", "type-check": "tsc --noEmit", "build:production": "npm run type-check && npm run lint && npm run build", "preview": "npm run build && npm run start", "clean": "rm -rf .next out dist", "audit:security": "npm audit --audit-level moderate", "audit:performance": "npm run build && npx lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@portabletext/react": "^3.2.1", "@portabletext/types": "^2.0.13", "@sanity/client": "^7.4.1", "@sanity/image-url": "^1.1.0", "@sanity/ui": "^2.15.18", "@sanity/vision": "^3.91.0", "@sendgrid/mail": "^8.1.5", "@types/bcryptjs": "^2.4.6", "@types/formidable": "^3.4.5", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.13", "@types/nodemailer": "^6.4.17", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "formidable": "^3.5.4", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "next": "15.3.3", "next-sanity": "^9.12.0", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.6.0", "stripe": "^18.2.1", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "web-vitals": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.3", "lighthouse": "^12.6.1", "tailwindcss": "^4", "typescript": "^5"}}