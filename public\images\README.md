# RiftStays Local Images

This directory contains locally stored images for the RiftStays website.

## Image Files:

### Hero Images
- `kenya-safari-landscape.jpg` - Main hero background (Kenya safari landscape with acacia trees)

### Property Images  
- `luxury-safari-lodge.jpg` - Safari lodge accommodation
- `coastal-beach-resort.jpg` - Beach resort property
- `luxury-city-hotel.jpg` - Urban hotel accommodation

### Tour Images
- `kenya-wildlife-safari.jpg` - Wildlife safari tour
- `kenyan-culture.jpg` - Cultural experience tour  
- `kenyan-coast-beach.jpg` - Beach and coastal tour

### Blog Images
- Same as tour images, reused for blog content

## Usage:
All images are referenced using local paths like `/images/filename.jpg` in the Next.js Image components.

## Image Sources:
All images were originally sourced from Unsplash.com (free stock photos) and downloaded for local use to improve performance and reduce external dependencies.

## Recommended Sizes:
- Hero images: 2072x1380px (3:2 aspect ratio)
- Property/Tour cards: 1000x667px (3:2 aspect ratio)
- Blog images: 1000x667px (3:2 aspect ratio)

## Optimization:
Images should be optimized for web use with appropriate compression while maintaining quality.
