const fs = require('fs');
const path = require('path');

// Create a simple SVG placeholder function
function createSVGPlaceholder(width, height, color, text, filename) {
  const svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="${color}"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" dy=".3em" fill="white">${text}</text>
</svg>`;
  
  const filePath = path.join(__dirname, '..', 'public', 'images', filename);
  fs.writeFileSync(filePath, svg);
  console.log(`✅ Created ${filename}`);
}

// Create placeholder images
console.log('🎨 Creating placeholder images for RiftStays...\n');

const images = [
  {
    filename: 'kenya-safari-landscape.jpg',
    width: 2072,
    height: 1380,
    color: '#f37a0b',
    text: 'Kenya Safari Landscape'
  },
  {
    filename: 'luxury-safari-lodge.jpg',
    width: 1000,
    height: 667,
    color: '#0ea5e9',
    text: 'Luxury Safari Lodge'
  },
  {
    filename: 'coastal-beach-resort.jpg',
    width: 1000,
    height: 667,
    color: '#d946ef',
    text: 'Coastal Beach Resort'
  },
  {
    filename: 'luxury-city-hotel.jpg',
    width: 1000,
    height: 667,
    color: '#22c55e',
    text: 'Luxury City Hotel'
  },
  {
    filename: 'kenya-wildlife-safari.jpg',
    width: 1000,
    height: 667,
    color: '#f59e0b',
    text: 'Kenya Wildlife Safari'
  },
  {
    filename: 'kenyan-culture.jpg',
    width: 1000,
    height: 667,
    color: '#ef4444',
    text: 'Kenyan Culture'
  },
  {
    filename: 'kenyan-coast-beach.jpg',
    width: 1000,
    height: 667,
    color: '#8b5cf6',
    text: 'Kenyan Coast Beach'
  }
];

// Create images directory if it doesn't exist
const imagesDir = path.join(__dirname, '..', 'public', 'images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Create all placeholder images
images.forEach(img => {
  // Change extension to .svg for now since we're creating SVG placeholders
  const svgFilename = img.filename.replace('.jpg', '.svg');
  createSVGPlaceholder(img.width, img.height, img.color, img.text, svgFilename);
});

console.log('\n🎉 All placeholder images created successfully!');
console.log('\n📋 Created Images:');
images.forEach(img => {
  const svgFilename = img.filename.replace('.jpg', '.svg');
  console.log(`   • ${svgFilename} - ${img.text}`);
});

console.log('\n📁 Images saved to: public/images/');
console.log('\n🔧 Note: These are SVG placeholders with vibrant colors.');
console.log('   Replace with actual photos when available.');
console.log('   The components are already configured to use local paths.');

console.log('\n🚀 Next steps:');
console.log('   1. Update image extensions from .jpg to .svg in components (or replace with actual JPG files)');
console.log('   2. Restart your development server');
console.log('   3. Replace placeholders with actual photos when ready');
