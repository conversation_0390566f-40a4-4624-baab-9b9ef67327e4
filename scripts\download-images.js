const https = require('https');
const fs = require('fs');
const path = require('path');

// Create public/images directory if it doesn't exist
const imagesDir = path.join(__dirname, '..', 'public', 'images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Image URLs and their local filenames
const images = [
  {
    url: 'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
    filename: 'kenya-safari-landscape.jpg',
    description: 'Kenya Safari Landscape with Acacia Trees - Hero Image'
  },
  {
    url: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    filename: 'luxury-safari-lodge.jpg',
    description: 'Luxury Safari Lodge - Featured Property'
  },
  {
    url: 'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    filename: 'coastal-beach-resort.jpg',
    description: 'Coastal Beach Resort - Beach Property'
  },
  {
    url: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    filename: 'luxury-city-hotel.jpg',
    description: 'Luxury City Hotel - Urban Accommodation'
  },
  {
    url: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    filename: 'kenya-wildlife-safari.jpg',
    description: 'Kenya Wildlife Safari - Tour Image'
  },
  {
    url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    filename: 'kenyan-culture.jpg',
    description: 'Kenyan Culture and People - Cultural Tour'
  },
  {
    url: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    filename: 'kenyan-coast-beach.jpg',
    description: 'Kenyan Coast Beach - Beach Tour'
  }
];

// Function to download an image
function downloadImage(imageUrl, filename) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(imagesDir, filename);
    
    // Check if file already exists
    if (fs.existsSync(filePath)) {
      console.log(`✓ ${filename} already exists, skipping...`);
      resolve();
      return;
    }

    console.log(`📥 Downloading ${filename}...`);
    
    const file = fs.createWriteStream(filePath);
    
    https.get(imageUrl, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${filename}: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`✅ Downloaded ${filename}`);
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(filePath, () => {}); // Delete the file on error
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// Download all images
async function downloadAllImages() {
  console.log('🎨 Starting image download for RiftStays...\n');
  
  try {
    for (const image of images) {
      await downloadImage(image.url, image.filename);
    }
    
    console.log('\n🎉 All images downloaded successfully!');
    console.log('\n📋 Downloaded Images:');
    images.forEach(img => {
      console.log(`   • ${img.filename} - ${img.description}`);
    });
    
    console.log('\n📁 Images saved to: public/images/');
    console.log('\n🔧 Next steps:');
    console.log('   1. Update your components to use local image paths');
    console.log('   2. Remove Unsplash domain from next.config.ts if desired');
    console.log('   3. Restart your development server');
    
  } catch (error) {
    console.error('❌ Error downloading images:', error.message);
    process.exit(1);
  }
}

// Run the download
downloadAllImages();
