const { createClient } = require('@sanity/client')
require('dotenv').config({ path: '.env.local' })

const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  useCdn: false,
  apiVersion: '2023-05-03'
})

// Sample data for seeding
const sampleSettings = {
  _type: 'settings',
  _id: 'settings',
  title: 'RiftStays - Premium Accommodations & Tours in Kenya',
  description: 'Discover exceptional accommodations and unforgettable tours across Kenya. From luxury villas to adventure safaris, RiftStays offers curated experiences for every traveler.',
  twitterHandle: '@riftstays',
  seo: {
    defaultTitle: 'RiftStays - Premium Accommodations & Tours in Kenya',
    defaultDescription: 'Book luxury accommodations and exciting tours in Kenya. Experience the best of East Africa with RiftStays.',
    keywords: ['Kenya accommodation', 'Kenya tours', 'safari', 'luxury travel', 'vacation rental']
  },
  contactInfo: {
    email: '<EMAIL>',
    phone: '+254 700 123 456',
    address: 'Nairobi, Kenya',
    businessHours: 'Monday - Friday: 8:00 AM - 6:00 PM, Saturday: 9:00 AM - 4:00 PM',
    emergencyContact: '+254 700 999 888'
  },
  paymentSettings: {
    mpesaEnabled: true,
    mpesaShortcode: '174379',
    stripeEnabled: false,
    paypalEnabled: false,
    bankTransferEnabled: true,
    bankDetails: {
      bankName: 'Kenya Commercial Bank',
      accountName: 'RiftStays Limited',
      accountNumber: '**********',
      swiftCode: 'KCBLKENX'
    }
  },
  bookingSettings: {
    defaultCancellationPolicy: 'Free cancellation up to 24 hours before check-in. 50% refund for cancellations within 24 hours.',
    advanceBookingHours: 24,
    maxBookingDays: 365,
    requireDeposit: true,
    depositPercentage: 30,
    autoConfirmBookings: false,
    sendConfirmationEmails: true,
    sendReminderEmails: true
  }
}

const sampleProperties = [
  {
    _type: 'property',
    title: 'Luxury Safari Villa - Maasai Mara',
    slug: { _type: 'slug', current: 'luxury-safari-villa-maasai-mara' },
    description: 'Experience the ultimate safari luxury in this stunning villa overlooking the Maasai Mara. Perfect for families and groups seeking an unforgettable African adventure.',
    price: 25000,
    location: 'Maasai Mara National Reserve',
    address: 'Maasai Mara National Reserve, Narok County, Kenya',
    coordinates: { lat: -1.4061, lng: 35.0222 },
    bedrooms: 4,
    bathrooms: 3,
    maxGuests: 8,
    category: 'countryside',
    propertyType: 'villa',
    amenities: ['wifi', 'pool', 'kitchen', 'parking', 'ac', 'tv', 'garden', 'security', 'mountain_view'],
    rules: ['No smoking indoors', 'No pets allowed', 'Quiet hours: 10 PM - 7 AM'],
    featured: true,
    status: 'available',
    verified: true,
    owner: {
      name: 'Safari Luxury Lodges',
      email: '<EMAIL>',
      phone: '+254 722 123 456'
    },
    contact: {
      checkInTime: '2:00 PM',
      checkOutTime: '11:00 AM',
      emergencyContact: '+254 722 999 888',
      localContact: 'Mara Camp Manager: +254 722 555 777'
    },
    pricing: {
      weeklyDiscount: 10,
      monthlyDiscount: 20,
      cleaningFee: 2500,
      securityDeposit: 10000,
      extraGuestFee: 2000
    },
    seo: {
      metaTitle: 'Luxury Safari Villa Maasai Mara - RiftStays',
      metaDescription: 'Book this stunning safari villa in Maasai Mara. 4 bedrooms, private pool, game viewing deck. Perfect for safari adventures.',
      keywords: ['Maasai Mara villa', 'safari accommodation', 'luxury lodge', 'Kenya safari']
    }
  },
  {
    _type: 'property',
    title: 'Beachfront Apartment - Diani Beach',
    slug: { _type: 'slug', current: 'beachfront-apartment-diani-beach' },
    description: 'Wake up to stunning ocean views in this modern beachfront apartment. Located on the pristine Diani Beach with direct beach access.',
    price: 8500,
    location: 'Diani Beach, Kwale',
    address: 'Diani Beach Road, Kwale County, Kenya',
    coordinates: { lat: -4.3297, lng: 39.5771 },
    bedrooms: 2,
    bathrooms: 2,
    maxGuests: 4,
    category: 'beach',
    propertyType: 'apartment',
    amenities: ['wifi', 'pool', 'kitchen', 'parking', 'ac', 'tv', 'beach_access', 'ocean_view'],
    rules: ['No smoking', 'No parties', 'Beach equipment available'],
    featured: true,
    status: 'available',
    verified: true,
    owner: {
      name: 'Coastal Properties Kenya',
      email: '<EMAIL>',
      phone: '+254 733 456 789'
    },
    contact: {
      checkInTime: '3:00 PM',
      checkOutTime: '10:00 AM',
      emergencyContact: '+254 733 999 888'
    },
    pricing: {
      weeklyDiscount: 15,
      cleaningFee: 1500,
      securityDeposit: 5000
    }
  }
]

const sampleTours = [
  {
    _type: 'tour',
    title: 'Maasai Mara Big Five Safari',
    slug: { _type: 'slug', current: 'maasai-mara-big-five-safari' },
    description: 'Experience the ultimate African safari adventure in the world-famous Maasai Mara. Witness the Big Five and the Great Migration in their natural habitat.',
    shortDescription: 'Full-day safari adventure in Maasai Mara National Reserve',
    price: 12500,
    duration: 8,
    maxGuests: 6,
    minGuests: 2,
    difficulty: 'easy',
    category: 'wildlife',
    location: 'Maasai Mara National Reserve',
    coordinates: { lat: -1.4061, lng: 35.0222 },
    includes: ['Professional safari guide', 'Game drive vehicle', 'Park entrance fees', 'Lunch', 'Bottled water'],
    excludes: ['Accommodation', 'Personal expenses', 'Tips', 'Travel insurance'],
    itinerary: [
      { time: '06:00', activity: 'Hotel pickup', description: 'Pickup from your Nairobi hotel' },
      { time: '09:30', activity: 'Arrive Maasai Mara', description: 'Enter the reserve and begin game drive' },
      { time: '12:30', activity: 'Lunch break', description: 'Lunch at a scenic spot in the reserve' },
      { time: '14:00', activity: 'Afternoon game drive', description: 'Continue wildlife viewing' },
      { time: '17:00', activity: 'Return journey', description: 'Depart Maasai Mara for Nairobi' }
    ],
    meetingPoint: 'Hotel lobby or designated pickup point in Nairobi',
    cancellationPolicy: 'Free cancellation up to 48 hours before tour date',
    availability: {
      daysOfWeek: ['1', '2', '3', '4', '5', '6', '0'],
      times: ['06:00'],
      blackoutDates: []
    },
    guide: {
      name: 'Samuel Kipkoech',
      bio: 'Experienced safari guide with 15 years in the industry. Expert in wildlife behavior and Maasai culture.',
      languages: ['english', 'swahili'],
      experience: 15
    },
    safety: {
      requirements: ['Comfortable walking shoes', 'Sun hat', 'Sunscreen'],
      equipment: ['First aid kit', 'Emergency communication device'],
      restrictions: 'Not suitable for children under 5 years',
      insurance: 'Comprehensive travel insurance included'
    },
    booking: {
      advanceBooking: 48,
      instantBooking: true,
      groupDiscounts: [
        { minGuests: 4, discount: 10 },
        { minGuests: 6, discount: 15 }
      ]
    },
    featured: true,
    active: true
  },
  {
    _type: 'tour',
    title: 'Mount Kenya Hiking Adventure',
    slug: { _type: 'slug', current: 'mount-kenya-hiking-adventure' },
    description: 'Challenge yourself with a guided hike on Mount Kenya, Africa\'s second-highest peak. Experience diverse ecosystems and breathtaking mountain views.',
    shortDescription: 'Guided hiking adventure on Mount Kenya',
    price: 8500,
    duration: 6,
    maxGuests: 8,
    minGuests: 3,
    difficulty: 'challenging',
    category: 'adventure',
    location: 'Mount Kenya National Park',
    coordinates: { lat: -0.1521, lng: 37.3084 },
    includes: ['Professional mountain guide', 'Hiking equipment', 'Park fees', 'Lunch', 'Transportation'],
    excludes: ['Accommodation', 'Personal gear', 'Tips'],
    meetingPoint: 'Nanyuki town center',
    featured: false,
    active: true
  }
]

const sampleUsers = [
  {
    _type: 'user',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    status: 'active',
    department: 'operations',
    permissions: [
      { resource: 'bookings', actions: ['view', 'create', 'update', 'delete', 'manage'] },
      { resource: 'properties', actions: ['view', 'create', 'update', 'delete', 'approve'] },
      { resource: 'tours', actions: ['view', 'create', 'update', 'delete'] },
      { resource: 'users', actions: ['view', 'create', 'update', 'delete', 'manage'] },
      { resource: 'analytics', actions: ['view', 'export'] },
      { resource: 'settings', actions: ['view', 'update'] }
    ],
    preferences: {
      language: 'en',
      currency: 'KES',
      notifications: {
        email: true,
        sms: false,
        marketing: false
      }
    },
    verification: {
      emailVerified: true,
      phoneVerified: true,
      identityVerified: true,
      verificationDocuments: []
    }
  }
]

async function seedData() {
  try {
    console.log('Starting data seeding...')

    // Create settings
    console.log('Creating settings...')
    await client.createOrReplace(sampleSettings)

    // Create properties
    console.log('Creating properties...')
    for (const property of sampleProperties) {
      await client.create(property)
    }

    // Create tours
    console.log('Creating tours...')
    for (const tour of sampleTours) {
      await client.create(tour)
    }

    // Create users
    console.log('Creating users...')
    for (const user of sampleUsers) {
      await client.create(user)
    }

    console.log('Data seeding completed successfully!')
  } catch (error) {
    console.error('Error seeding data:', error)
  }
}

// Run the seeding
if (require.main === module) {
  seedData()
}

module.exports = { seedData }
