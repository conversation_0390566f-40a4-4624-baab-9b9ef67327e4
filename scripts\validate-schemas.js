const fs = require('fs')
const path = require('path')

// Schema validation script for RiftStays
console.log('🔍 Validating RiftStays Schema System...\n')

// Check if all required schema files exist
const requiredSchemaFiles = [
  'src/sanity/schemas/index.ts',
  'src/sanity/schemas/property.ts',
  'src/sanity/schemas/tour.ts',
  'src/sanity/schemas/booking.ts',
  'src/sanity/schemas/user.ts',
  'src/sanity/schemas/settings.ts',
  'src/sanity/schemas/blogPost.ts',
  'src/sanity/schemas/author.ts',
  'src/sanity/schemas/category.ts',
  'src/sanity/schemas/blockContent.ts',
  'src/sanity/schemas/pendingProperty.ts'
]

const requiredTypeFiles = [
  'src/types/property.ts',
  'src/types/tour.ts',
  'src/types/booking.ts',
  'src/types/auth.ts',
  'src/types/settings.ts',
  'src/types/blog.ts'
]

const requiredConfigFiles = [
  'src/sanity/sanity.config.ts',
  'src/sanity/schema.ts',
  'src/sanity/lib/queries.ts',
  'src/lib/validation.ts'
]

let allFilesExist = true
let validationErrors = []

// Check schema files
console.log('📁 Checking Schema Files:')
requiredSchemaFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file} - MISSING`)
    allFilesExist = false
  }
})

// Check type files
console.log('\n📁 Checking Type Definition Files:')
requiredTypeFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file} - MISSING`)
    allFilesExist = false
  }
})

// Check config files
console.log('\n📁 Checking Configuration Files:')
requiredConfigFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file} - MISSING`)
    allFilesExist = false
  }
})

// Validate schema index exports
console.log('\n🔗 Validating Schema Exports:')
try {
  const schemaIndexContent = fs.readFileSync('src/sanity/schemas/index.ts', 'utf8')
  const expectedSchemas = [
    'property', 'settings', 'pendingProperty', 'blogPost', 
    'blockContent', 'author', 'category', 'tour', 'booking', 'user'
  ]
  
  expectedSchemas.forEach(schema => {
    if (schemaIndexContent.includes(`import ${schema} from './${schema}'`)) {
      console.log(`✅ ${schema} schema imported`)
    } else {
      console.log(`❌ ${schema} schema import - MISSING`)
      validationErrors.push(`Missing import for ${schema} schema`)
    }
    
    if (schemaIndexContent.includes(schema) && schemaIndexContent.includes('schemaTypes')) {
      console.log(`✅ ${schema} schema exported`)
    } else {
      console.log(`❌ ${schema} schema export - MISSING`)
      validationErrors.push(`Missing export for ${schema} schema`)
    }
  })
} catch (error) {
  console.log(`❌ Error reading schema index: ${error.message}`)
  validationErrors.push('Cannot read schema index file')
}

// Validate Sanity config
console.log('\n⚙️ Validating Sanity Configuration:')
try {
  const configContent = fs.readFileSync('src/sanity/sanity.config.ts', 'utf8')
  
  if (configContent.includes('schemaTypes') && configContent.includes('./schemas')) {
    console.log('✅ Schema types imported correctly')
  } else {
    console.log('❌ Schema types import - INCORRECT')
    validationErrors.push('Sanity config not importing schemas correctly')
  }
  
  if (configContent.includes('deskTool') && configContent.includes('visionTool')) {
    console.log('✅ Required plugins configured')
  } else {
    console.log('❌ Required plugins - MISSING')
    validationErrors.push('Missing required Sanity plugins')
  }
} catch (error) {
  console.log(`❌ Error reading Sanity config: ${error.message}`)
  validationErrors.push('Cannot read Sanity config file')
}

// Check for required fields in schemas
console.log('\n🏗️ Validating Schema Structure:')

const schemaValidations = [
  {
    file: 'src/sanity/schemas/property.ts',
    requiredFields: ['title', 'slug', 'description', 'price', 'location', 'bedrooms', 'bathrooms', 'maxGuests', 'category', 'propertyType', 'images']
  },
  {
    file: 'src/sanity/schemas/tour.ts',
    requiredFields: ['title', 'slug', 'description', 'price', 'duration', 'maxGuests', 'minGuests', 'difficulty', 'category', 'location']
  },
  {
    file: 'src/sanity/schemas/booking.ts',
    requiredFields: ['bookingNumber', 'type', 'status', 'contact', 'guests', 'pricing', 'payment']
  },
  {
    file: 'src/sanity/schemas/user.ts',
    requiredFields: ['email', 'firstName', 'lastName', 'role', 'status']
  }
]

schemaValidations.forEach(({ file, requiredFields }) => {
  try {
    const content = fs.readFileSync(file, 'utf8')
    const schemaName = path.basename(file, '.ts')
    
    requiredFields.forEach(field => {
      if (content.includes(`name: '${field}'`)) {
        console.log(`✅ ${schemaName}.${field}`)
      } else {
        console.log(`❌ ${schemaName}.${field} - MISSING`)
        validationErrors.push(`Missing field ${field} in ${schemaName} schema`)
      }
    })
  } catch (error) {
    console.log(`❌ Error reading ${file}: ${error.message}`)
    validationErrors.push(`Cannot read ${file}`)
  }
})

// Check TypeScript types alignment
console.log('\n🔤 Validating TypeScript Types:')
try {
  const propertyTypes = fs.readFileSync('src/types/property.ts', 'utf8')
  const tourTypes = fs.readFileSync('src/types/tour.ts', 'utf8')
  const bookingTypes = fs.readFileSync('src/types/booking.ts', 'utf8')
  
  if (propertyTypes.includes('export interface Property') && propertyTypes.includes('PropertyCategory')) {
    console.log('✅ Property types defined')
  } else {
    console.log('❌ Property types - INCOMPLETE')
    validationErrors.push('Property types incomplete')
  }
  
  if (tourTypes.includes('export interface Tour') && tourTypes.includes('TourDifficulty')) {
    console.log('✅ Tour types defined')
  } else {
    console.log('❌ Tour types - INCOMPLETE')
    validationErrors.push('Tour types incomplete')
  }
  
  if (bookingTypes.includes('export interface Booking') && bookingTypes.includes('BookingType')) {
    console.log('✅ Booking types defined')
  } else {
    console.log('❌ Booking types - INCOMPLETE')
    validationErrors.push('Booking types incomplete')
  }
} catch (error) {
  console.log(`❌ Error reading type files: ${error.message}`)
  validationErrors.push('Cannot read type definition files')
}

// Summary
console.log('\n📊 Validation Summary:')
console.log('='.repeat(50))

if (allFilesExist && validationErrors.length === 0) {
  console.log('🎉 All schema validations passed!')
  console.log('✅ Schema system is properly configured')
  console.log('✅ All required files exist')
  console.log('✅ Schema exports are correct')
  console.log('✅ TypeScript types are aligned')
  process.exit(0)
} else {
  console.log('⚠️ Schema validation issues found:')
  
  if (!allFilesExist) {
    console.log('❌ Some required files are missing')
  }
  
  if (validationErrors.length > 0) {
    console.log('❌ Validation errors:')
    validationErrors.forEach(error => {
      console.log(`   • ${error}`)
    })
  }
  
  console.log('\n🔧 Please fix the issues above before proceeding.')
  process.exit(1)
}
