import { Metadata } from 'next'
import { Typography } from '@/components/ui/Typography'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'
import Image from 'next/image'

export const metadata: Metadata = {
  title: 'About Us | RiftStays',
  description: 'Learn about RiftStays mission to connect travelers with unique accommodations across Kenya.',
}

export default function AboutPage() {
  const values = [
    {
      title: "Trust & Safety",
      description: "We prioritize the safety and security of our users through verified listings and secure payment processing."
    },
    {
      title: "Local Expertise",
      description: "Our deep understanding of Kenya's diverse regions helps travelers find authentic experiences."
    },
    {
      title: "Quality Service",
      description: "We're committed to providing exceptional customer service and support throughout your journey."
    },
    {
      title: "Community Impact",
      description: "We support local communities by connecting travelers with local property owners and businesses."
    }
  ]

  const stats = [
    { number: "10,000+", label: "Properties Listed" },
    { number: "50,000+", label: "Happy Guests" },
    { number: "47", label: "Counties Covered" },
    { number: "24/7", label: "Customer Support" }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <Typography variant="h1" className="text-white mb-6">
              About RiftStays
            </Typography>
            <Typography variant="lead" className="text-white/90 max-w-3xl mx-auto">
              Kenya's leading platform for discovering and booking unique accommodations, 
              connecting travelers with unforgettable experiences across the country.
            </Typography>
          </div>
        </div>
      </div>

      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
        {/* Mission Section */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          <div>
            <Typography variant="h2" className="mb-6">
              Our Mission
            </Typography>
            <Typography variant="body" className="text-gray-600 mb-6 text-lg leading-relaxed">
              At RiftStays, we believe that every journey should be extraordinary. Our mission is to 
              connect travelers with unique, authentic accommodations while empowering local property 
              owners to share their spaces and stories.
            </Typography>
            <Typography variant="body" className="text-gray-600 mb-6 text-lg leading-relaxed">
              From the bustling streets of Nairobi to the pristine beaches of the coast, from the 
              wildlife-rich savannas to the scenic highlands, we help you discover Kenya's diverse 
              beauty through carefully curated accommodations.
            </Typography>
          </div>
          <div className="relative h-96 rounded-2xl overflow-hidden shadow-xl">
            <Image
              src="/images/kenya-safari-landscape.jpg"
              alt="Kenya landscape"
              fill
              className="object-cover"
            />
          </div>
        </div>

        {/* Stats Section */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-20">
          <Typography variant="h2" className="text-center mb-12">
            RiftStays by the Numbers
          </Typography>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <Typography variant="h1" className="text-primary-600 mb-2">
                  {stat.number}
                </Typography>
                <Typography variant="body" className="text-gray-600">
                  {stat.label}
                </Typography>
              </div>
            ))}
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-20">
          <Typography variant="h2" className="text-center mb-12">
            Our Values
          </Typography>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg p-6 text-center">
                <Typography variant="h3" className="text-primary-600 mb-4">
                  {value.title}
                </Typography>
                <Typography variant="body" className="text-gray-600">
                  {value.description}
                </Typography>
              </div>
            ))}
          </div>
        </div>

        {/* Story Section */}
        <div className="bg-white rounded-2xl shadow-xl p-8 lg:p-12 mb-20">
          <Typography variant="h2" className="text-center mb-8">
            Our Story
          </Typography>
          <div className="prose prose-lg max-w-none">
            <Typography variant="body" className="text-gray-600 mb-6 text-lg leading-relaxed">
              Founded in 2024, RiftStays was born from a simple idea: to make it easier for travelers 
              to discover authentic accommodations while helping property owners reach a wider audience. 
              Our founders, passionate about Kenya's tourism potential, recognized the need for a 
              platform that truly understands the local market.
            </Typography>
            <Typography variant="body" className="text-gray-600 mb-6 text-lg leading-relaxed">
              Starting with a handful of properties in Nairobi, we've grown to become Kenya's most 
              trusted accommodation platform. Our success is built on the relationships we've formed 
              with property owners, guests, and local communities across the country.
            </Typography>
            <Typography variant="body" className="text-gray-600 text-lg leading-relaxed">
              Today, RiftStays continues to innovate, introducing new features and services that 
              enhance the travel experience while supporting Kenya's growing tourism industry.
            </Typography>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 text-center text-white">
          <Typography variant="h2" className="text-white mb-4">
            Join the RiftStays Community
          </Typography>
          <Typography variant="lead" className="text-white/90 mb-8 max-w-2xl mx-auto">
            Whether you're looking for your next adventure or want to share your property with travelers, 
            we're here to help you every step of the way.
          </Typography>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/search">
              <Button 
                variant="secondary"
                className="bg-white text-primary-600 hover:bg-gray-50 font-semibold px-8 py-3 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                Find Accommodation
              </Button>
            </Link>
            <Link href="/list-your-property">
              <Button 
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-primary-600 font-semibold px-8 py-3 rounded-full transition-all duration-300"
              >
                List Your Property
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
