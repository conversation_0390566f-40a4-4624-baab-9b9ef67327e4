'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/admin/DashboardLayout'
import { AdvancedAnalytics } from '@/components/admin/AdvancedAnalytics'
import { 
  DocumentChartBarIcon,
  CurrencyDollarIcon,
  CalendarDaysIcon,
  UserGroupIcon,
  HomeIcon,
  ArrowDownTrayIcon,
  PrinterIcon,
  ShareIcon
} from '@heroicons/react/24/outline'

interface ReportData {
  summary: {
    totalRevenue: number
    totalBookings: number
    totalProperties: number
    totalUsers: number
    averageBookingValue: number
    occupancyRate: number
  }
  trends: {
    revenueGrowth: number
    bookingGrowth: number
    userGrowth: number
  }
  topMetrics: {
    topProperties: Array<{
      id: string
      title: string
      revenue: number
      bookings: number
    }>
    topLocations: Array<{
      location: string
      properties: number
      revenue: number
    }>
    topCustomers: Array<{
      id: string
      name: string
      totalSpent: number
      bookings: number
    }>
  }
}

export default function AdminReportsPage() {
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState({
    startDate: '2024-01-01',
    endDate: '2024-12-31'
  })
  const [reportType, setReportType] = useState<'overview' | 'revenue' | 'bookings' | 'properties' | 'users'>('overview')

  useEffect(() => {
    fetchReportData()
  }, [dateRange, reportType])

  const fetchReportData = async () => {
    try {
      // Mock report data - replace with actual API call
      const mockData: ReportData = {
        summary: {
          totalRevenue: 15750000,
          totalBookings: 1456,
          totalProperties: 189,
          totalUsers: 4231,
          averageBookingValue: 108200,
          occupancyRate: 78.5
        },
        trends: {
          revenueGrowth: 23.4,
          bookingGrowth: 18.7,
          userGrowth: 31.2
        },
        topMetrics: {
          topProperties: [
            { id: '1', title: 'Luxury Villa - Karen', revenue: 850000, bookings: 45 },
            { id: '2', title: 'Beachfront Resort - Diani', revenue: 720000, bookings: 38 },
            { id: '3', title: 'Safari Lodge - Maasai Mara', revenue: 650000, bookings: 32 }
          ],
          topLocations: [
            { location: 'Nairobi', properties: 67, revenue: 5200000 },
            { location: 'Mombasa', properties: 45, revenue: 4100000 },
            { location: 'Nakuru', properties: 23, revenue: 2800000 }
          ],
          topCustomers: [
            { id: '1', name: 'John Smith', totalSpent: 450000, bookings: 8 },
            { id: '2', name: 'Sarah Johnson', totalSpent: 380000, bookings: 6 },
            { id: '3', name: 'Michael Brown', totalSpent: 320000, bookings: 5 }
          ]
        }
      }
      setReportData(mockData)
    } catch (error) {
      console.error('Failed to fetch report data:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateReport = async (format: 'pdf' | 'excel' | 'csv') => {
    try {
      // Generate report - replace with actual API call
      console.log(`Generating ${format.toUpperCase()} report for ${reportType}`)
      alert(`${format.toUpperCase()} report generated successfully!`)
    } catch (error) {
      console.error('Failed to generate report:', error)
      alert('Failed to generate report')
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-KE').format(num)
  }

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
            <p className="text-gray-600">Comprehensive business insights and performance metrics</p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => generateReport('pdf')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <DocumentChartBarIcon className="h-4 w-4 mr-2" />
              PDF
            </button>
            
            <button
              onClick={() => generateReport('excel')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              Excel
            </button>
            
            <button
              onClick={() => generateReport('csv')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ShareIcon className="h-4 w-4 mr-2" />
              CSV
            </button>
          </div>
        </div>

        {/* Date Range and Report Type Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Date
              </label>
              <input
                type="date"
                value={dateRange.startDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                End Date
              </label>
              <input
                type="date"
                value={dateRange.endDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Report Type
              </label>
              <select
                value={reportType}
                onChange={(e) => setReportType(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="overview">Overview</option>
                <option value="revenue">Revenue Analysis</option>
                <option value="bookings">Booking Analytics</option>
                <option value="properties">Property Performance</option>
                <option value="users">User Analytics</option>
              </select>
            </div>
          </div>
        </div>

        {/* Key Metrics Summary */}
        {reportData && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(reportData.summary.totalRevenue)}
                  </p>
                  <p className="text-sm text-green-600 mt-1">
                    +{formatPercentage(reportData.trends.revenueGrowth)} from last period
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatNumber(reportData.summary.totalBookings)}
                  </p>
                  <p className="text-sm text-green-600 mt-1">
                    +{formatPercentage(reportData.trends.bookingGrowth)} from last period
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <CalendarDaysIcon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average Booking Value</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(reportData.summary.averageBookingValue)}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    Per booking transaction
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <CurrencyDollarIcon className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Properties</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatNumber(reportData.summary.totalProperties)}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    Active listings
                  </p>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <HomeIcon className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatNumber(reportData.summary.totalUsers)}
                  </p>
                  <p className="text-sm text-green-600 mt-1">
                    +{formatPercentage(reportData.trends.userGrowth)} from last period
                  </p>
                </div>
                <div className="p-3 bg-indigo-100 rounded-full">
                  <UserGroupIcon className="h-6 w-6 text-indigo-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Occupancy Rate</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercentage(reportData.summary.occupancyRate)}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    Average across all properties
                  </p>
                </div>
                <div className="p-3 bg-teal-100 rounded-full">
                  <CalendarDaysIcon className="h-6 w-6 text-teal-600" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Advanced Analytics Component */}
        <AdvancedAnalytics />

        {/* Top Performers */}
        {reportData && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Top Properties */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Properties</h3>
              <div className="space-y-3">
                {reportData.topMetrics.topProperties.map((property, index) => (
                  <div key={property.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium text-primary-700">#{index + 1}</span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{property.title}</p>
                        <p className="text-xs text-gray-500">{property.bookings} bookings</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">{formatCurrency(property.revenue)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Locations */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Locations</h3>
              <div className="space-y-3">
                {reportData.topMetrics.topLocations.map((location, index) => (
                  <div key={location.location} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-secondary-100 rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium text-secondary-700">#{index + 1}</span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{location.location}</p>
                        <p className="text-xs text-gray-500">{location.properties} properties</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">{formatCurrency(location.revenue)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Customers */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Customers</h3>
              <div className="space-y-3">
                {reportData.topMetrics.topCustomers.map((customer, index) => (
                  <div key={customer.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-accent-100 rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium text-accent-700">#{index + 1}</span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{customer.name}</p>
                        <p className="text-xs text-gray-500">{customer.bookings} bookings</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">{formatCurrency(customer.totalSpent)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
