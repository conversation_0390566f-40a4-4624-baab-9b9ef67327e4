'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/admin/DashboardLayout'
import { Tour } from '@/types/booking'
import Image from 'next/image'

export default function ToursManagement() {
  const [tours, setTours] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    fetchTours()
  }, [])

  const fetchTours = async () => {
    try {
      // Mock data for demo
      const mockTours: any[] = [
        {
          _id: '1',
          title: 'Maasai Mara Wildlife Safari',
          slug: { _type: 'slug', current: 'maasai-mara-wildlife-safari' },
          description: 'Experience the breathtaking wildlife of Maasai Mara National Reserve',
          shortDescription: 'Full-day wildlife safari with game drives and lunch',
          images: [
            {
              _type: 'image',
              asset: { _ref: 'image-placeholder-tour-1', _type: 'reference' }
            }
          ],
          price: 12000,
          duration: 8,
          maxGuests: 8,
          minGuests: 2,
          difficulty: 'easy',
          category: 'wildlife',
          location: 'Maasai Mara, Kenya',
          includes: ['Transportation', 'Game drives', 'Lunch', 'Professional guide'],
          excludes: ['Accommodation', 'Dinner', 'Personal expenses'],
          itinerary: [],
          meetingPoint: 'Hotel lobby in Nairobi',
          cancellationPolicy: 'Free cancellation up to 24 hours',
          availability: {
            daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
            times: ['06:00'],
            blackoutDates: []
          },
          featured: true,
          active: true,
          createdAt: '2024-11-01T00:00:00Z',
          updatedAt: '2024-11-01T00:00:00Z'
        },
        {
          _id: '2',
          title: 'Nairobi City Walking Tour',
          slug: { _type: 'slug', current: 'nairobi-city-walking-tour' },
          description: 'Discover the vibrant culture and history of Nairobi',
          shortDescription: 'Half-day walking tour exploring Nairobi culture',
          images: [
            {
              _type: 'image',
              asset: { _ref: 'image-placeholder-tour-2', _type: 'reference' }
            }
          ],
          price: 3500,
          duration: 4,
          maxGuests: 12,
          minGuests: 1,
          difficulty: 'easy',
          category: 'city',
          location: 'Nairobi, Kenya',
          includes: ['Professional guide', 'Walking tour', 'Market visit'],
          excludes: ['Meals', 'Transportation', 'Personal expenses'],
          itinerary: [],
          meetingPoint: 'Kenyatta International Conference Centre',
          cancellationPolicy: 'Free cancellation up to 2 hours',
          availability: {
            daysOfWeek: [1, 2, 3, 4, 5, 6],
            times: ['09:00', '14:00'],
            blackoutDates: []
          },
          featured: false,
          active: true,
          createdAt: '2024-11-15T00:00:00Z',
          updatedAt: '2024-11-15T00:00:00Z'
        }
      ]
      
      setTours(mockTours)
    } catch (error) {
      console.error('Failed to fetch tours:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'moderate': return 'bg-yellow-100 text-yellow-800'
      case 'challenging': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredTours = tours.filter(tour => {
    const matchesFilter = filter === 'all' || 
                         (filter === 'active' && tour.active) ||
                         (filter === 'inactive' && !tour.active) ||
                         tour.category === filter
    const matchesSearch = tour.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tour.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tour.category.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesFilter && matchesSearch
  })

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Tours Management
              </h2>
            </div>
            <div className="mt-4 flex md:mt-0 md:ml-4">
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                Export
              </button>
              <button className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                Add Tour
              </button>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          {/* Filters */}
          <div className="mt-8 flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search tours..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
            </div>
            <div>
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                <option value="all">All Tours</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="wildlife">Wildlife</option>
                <option value="cultural">Cultural</option>
                <option value="adventure">Adventure</option>
                <option value="city">City</option>
                <option value="nature">Nature</option>
                <option value="beach">Beach</option>
              </select>
            </div>
          </div>

          {/* Tours Grid */}
          <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {filteredTours.map((tour) => (
              <div key={tour._id} className="bg-white overflow-hidden shadow rounded-lg">
                <div className="relative h-48">
                  <Image
                    src="/api/placeholder/400/300"
                    alt={tour.images[0]?.alt || tour.title}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                  {tour.featured && (
                    <div className="absolute top-2 left-2 bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
                      Featured
                    </div>
                  )}
                  <div className={`absolute top-2 right-2 px-2 py-1 rounded text-xs font-medium ${getDifficultyColor(tour.difficulty)}`}>
                    {tour.difficulty}
                  </div>
                  {!tour.active && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                      <span className="text-white font-medium">Inactive</span>
                    </div>
                  )}
                </div>
                
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-blue-600 font-medium capitalize">{tour.category}</span>
                    <span className="text-lg font-bold text-gray-900">{formatCurrency(tour.price)}</span>
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                    {tour.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {tour.shortDescription}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span>{tour.duration} hours</span>
                    <span>{tour.minGuests}-{tour.maxGuests} guests</span>
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-500 mb-4">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {tour.location}
                  </div>
                  
                  <div className="flex justify-between">
                    <button className="text-blue-600 hover:text-blue-900 text-sm font-medium">
                      Edit
                    </button>
                    <button className="text-green-600 hover:text-green-900 text-sm font-medium">
                      Schedule
                    </button>
                    <button className={`text-sm font-medium ${tour.active ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}>
                      {tour.active ? 'Deactivate' : 'Activate'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredTours.length === 0 && (
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No tours found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || filter !== 'all' ? 'Try adjusting your search or filter.' : 'Get started by creating a new tour.'}
              </p>
              <div className="mt-6">
                <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                  Create Tour
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
