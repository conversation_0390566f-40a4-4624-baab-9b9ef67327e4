import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'

// GET /api/admin/analytics - Get comprehensive analytics data
export async function GET(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { searchParams } = new URL(request.url)
      const period = searchParams.get('period') || '30d' // 7d, 30d, 90d, 1y
      const startDate = searchParams.get('startDate')
      const endDate = searchParams.get('endDate')
      
      // Calculate date range
      let dateFilter = ''
      if (startDate && endDate) {
        dateFilter = `&& createdAt >= "${startDate}" && createdAt <= "${endDate}"`
      } else {
        const now = new Date()
        let daysBack = 30
        switch (period) {
          case '7d': daysBack = 7; break
          case '30d': daysBack = 30; break
          case '90d': daysBack = 90; break
          case '1y': daysBack = 365; break
        }
        const startDateCalc = new Date(now.getTime() - daysBack * 24 * 60 * 60 * 1000)
        dateFilter = `&& createdAt >= "${startDateCalc.toISOString()}"`
      }
      
      // Get overview statistics
      const overview = await client.fetch(
        groq`{
          "totalUsers": count(*[_type == "user"]),
          "activeUsers": count(*[_type == "user" && status == "active"]),
          "newUsers": count(*[_type == "user" ${dateFilter}]),
          "totalProperties": count(*[_type == "property"]),
          "activeProperties": count(*[_type == "property" && status == "active"]),
          "pendingProperties": count(*[_type == "property" && status == "pending"]),
          "newProperties": count(*[_type == "property" ${dateFilter}]),
          "totalBookings": count(*[_type == "booking"]),
          "confirmedBookings": count(*[_type == "booking" && status == "confirmed"]),
          "newBookings": count(*[_type == "booking" ${dateFilter}]),
          "totalRevenue": sum(*[_type == "booking" && status in ["confirmed", "completed"] && paymentStatus == "paid"].totalAmount),
          "revenueThisPeriod": sum(*[_type == "booking" && status in ["confirmed", "completed"] && paymentStatus == "paid" ${dateFilter}].totalAmount)
        }`
      )
      
      // Get user growth data (last 30 days)
      const userGrowth = await client.fetch(
        groq`*[_type == "user" && createdAt >= "${new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()}"] {
          "date": createdAt,
          role
        } | order(createdAt asc)`
      )
      
      // Get property statistics by type
      const propertyStats = await client.fetch(
        groq`{
          "byType": *[_type == "property" && status == "active"] | {
            "type": propertyType,
            "count": count(*)
          } | group(type),
          "byLocation": *[_type == "property" && status == "active"] | {
            "city": location.city,
            "count": count(*)
          } | group(city)[0...10],
          "byPriceRange": {
            "under50k": count(*[_type == "property" && status == "active" && price.amount < 50000]),
            "50k-100k": count(*[_type == "property" && status == "active" && price.amount >= 50000 && price.amount < 100000]),
            "100k-200k": count(*[_type == "property" && status == "active" && price.amount >= 100000 && price.amount < 200000]),
            "200k-500k": count(*[_type == "property" && status == "active" && price.amount >= 200000 && price.amount < 500000]),
            "over500k": count(*[_type == "property" && status == "active" && price.amount >= 500000])
          }
        }`
      )
      
      // Get booking statistics
      const bookingStats = await client.fetch(
        groq`{
          "byStatus": {
            "pending": count(*[_type == "booking" && status == "pending"]),
            "confirmed": count(*[_type == "booking" && status == "confirmed"]),
            "cancelled": count(*[_type == "booking" && status == "cancelled"]),
            "completed": count(*[_type == "booking" && status == "completed"])
          },
          "revenueByMonth": *[_type == "booking" && status in ["confirmed", "completed"] && paymentStatus == "paid" && createdAt >= "${new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString()}"] {
            "month": dateTime(createdAt).month,
            "year": dateTime(createdAt).year,
            "amount": totalAmount
          } | group([year, month]) | {
            "period": key,
            "revenue": sum(amount)
          } | order(period desc)[0...12]
        }`
      )
      
      // Get top performing properties
      const topProperties = await client.fetch(
        groq`*[_type == "property" && status == "active"] {
          _id,
          title,
          location,
          price,
          "bookingCount": count(*[_type == "booking" && property._ref == ^._id]),
          "revenue": sum(*[_type == "booking" && property._ref == ^._id && status in ["confirmed", "completed"] && paymentStatus == "paid"].totalAmount),
          "rating": avg(*[_type == "review" && property._ref == ^._id].rating)
        } | order(bookingCount desc)[0...10]`
      )
      
      // Get recent activity
      const recentActivity = await client.fetch(
        groq`{
          "recentUsers": *[_type == "user"] | order(createdAt desc)[0...5] {
            _id,
            firstName,
            lastName,
            email,
            role,
            createdAt
          },
          "recentProperties": *[_type == "property"] | order(createdAt desc)[0...5] {
            _id,
            title,
            propertyType,
            status,
            owner->{firstName, lastName},
            createdAt
          },
          "recentBookings": *[_type == "booking"] | order(createdAt desc)[0...5] {
            _id,
            status,
            totalAmount,
            property->{title},
            guest->{firstName, lastName},
            createdAt
          }
        }`
      )
      
      // Process user growth data for charts
      const userGrowthChart = processGrowthData(userGrowth)
      
      return NextResponse.json({
        success: true,
        data: {
          overview,
          userGrowth: userGrowthChart,
          propertyStats,
          bookingStats,
          topProperties,
          recentActivity,
          period,
          generatedAt: new Date().toISOString()
        }
      })
    } catch (error) {
      console.error('Error fetching analytics:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch analytics' },
        { status: 500 }
      )
    }
  }, ['analytics:read'])(request, NextResponse)
}

// Helper function to process growth data for charts
function processGrowthData(data: any[]) {
  const dailyGrowth: { [key: string]: { customers: number, property_owners: number, total: number } } = {}
  
  data.forEach(item => {
    if (!item?.date) return
    try {
      const date = new Date(item.date as string).toISOString().split('T')[0]
      if (!date) return

      if (!dailyGrowth[date]) {
        dailyGrowth[date] = { customers: 0, property_owners: 0, total: 0 }
      }

      if (item.role === 'customer') {
        dailyGrowth[date].customers++
      } else if (item.role === 'property_owner') {
        dailyGrowth[date].property_owners++
      }
      dailyGrowth[date].total++
    } catch (error) {
      // Skip invalid dates
      return
    }
  })
  
  return Object.entries(dailyGrowth).map(([date, counts]) => ({
    date,
    ...counts
  })).sort((a, b) => a.date.localeCompare(b.date))
}
