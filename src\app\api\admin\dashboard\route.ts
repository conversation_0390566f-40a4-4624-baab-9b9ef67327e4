import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth'
import { PERMISSIONS } from '@/types/auth'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'

export const GET = withAuth(async (request: NextRequest) => {
  try {
    // Get dashboard statistics
    const [
      totalBookings,
      totalRevenue,
      totalProperties,
      totalTours,
      totalUsers,
      pendingBookings,
      todayBookings,
      recentBookings,
      topTours,
      topProperties,
      monthlyRevenue
    ] = await Promise.all([
      // Total bookings
      client.fetch(groq`count(*[_type == "booking"])`),
      
      // Total revenue
      client.fetch(groq`sum(*[_type == "booking" && payment.status == "completed"].pricing.totalPrice)`),
      
      // Total properties
      client.fetch(groq`count(*[_type == "property"])`),
      
      // Total tours
      client.fetch(groq`count(*[_type == "tour"])`),
      
      // Total users
      client.fetch(groq`count(*[_type == "user"])`),
      
      // Pending bookings
      client.fetch(groq`count(*[_type == "booking" && status == "pending"])`),
      
      // Today's bookings
      client.fetch(groq`count(*[_type == "booking" && createdAt >= $today])`, {
        today: new Date().toISOString().split('T')[0]
      }),
      
      // Recent bookings
      client.fetch(groq`
        *[_type == "booking"] | order(createdAt desc)[0...10] {
          _id,
          bookingNumber,
          type,
          status,
          contact,
          pricing,
          createdAt,
          tourBooking->{title},
          accommodationBooking->{title}
        }
      `),
      
      // Top performing tours
      client.fetch(groq`
        *[_type == "tour"] {
          _id,
          title,
          price,
          "bookingCount": count(*[_type == "booking" && tourBooking.tourId == ^._id]),
          "revenue": sum(*[_type == "booking" && tourBooking.tourId == ^._id && payment.status == "completed"].pricing.totalPrice)
        } | order(bookingCount desc)[0...5]
      `),
      
      // Top performing properties
      client.fetch(groq`
        *[_type == "property"] {
          _id,
          title,
          price,
          "bookingCount": count(*[_type == "booking" && accommodationBooking.propertyId == ^._id]),
          "revenue": sum(*[_type == "booking" && accommodationBooking.propertyId == ^._id && payment.status == "completed"].pricing.totalPrice)
        } | order(bookingCount desc)[0...5]
      `),
      
      // Monthly revenue for the last 12 months
      client.fetch(groq`
        *[_type == "booking" && payment.status == "completed" && createdAt >= $startDate] {
          "month": dateTime(createdAt).month,
          "year": dateTime(createdAt).year,
          "amount": pricing.totalPrice
        }
      `, {
        startDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString()
      })
    ])

    // Calculate additional metrics
    const averageBookingValue = totalBookings > 0 ? totalRevenue / totalBookings : 0
    const conversionRate = 0.15 // This would be calculated based on actual visitor data

    // Process monthly revenue data
    const monthlyRevenueData = monthlyRevenue.reduce((acc: any, booking: any) => {
      const key = `${booking.year}-${booking.month.toString().padStart(2, '0')}`
      if (!acc[key]) {
        acc[key] = { month: key, amount: 0, bookings: 0 }
      }
      acc[key].amount += booking.amount
      acc[key].bookings += 1
      return acc
    }, {})

    const revenueData = Object.values(monthlyRevenueData).slice(-12)

    return NextResponse.json({
      success: true,
      data: {
        stats: {
          totalBookings,
          totalRevenue: totalRevenue || 0,
          totalProperties,
          totalTours,
          totalUsers,
          pendingBookings,
          todayBookings,
          monthlyRevenue: totalRevenue || 0,
          conversionRate,
          averageBookingValue
        },
        recentBookings,
        topTours,
        topProperties,
        revenueData,
        bookingTrends: [
          { month: 'Jan', tours: 45, properties: 32, total: 77 },
          { month: 'Feb', tours: 52, properties: 28, total: 80 },
          { month: 'Mar', tours: 48, properties: 35, total: 83 },
          { month: 'Apr', tours: 61, properties: 42, total: 103 },
          { month: 'May', tours: 55, properties: 38, total: 93 },
          { month: 'Jun', tours: 67, properties: 45, total: 112 }
        ]
      }
    })
  } catch (error) {
    console.error('Dashboard data error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch dashboard data',
        success: false 
      },
      { status: 500 }
    )
  }
}, [PERMISSIONS.ANALYTICS_VIEW])
