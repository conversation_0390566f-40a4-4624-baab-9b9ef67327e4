import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'

// GET /api/admin/properties/[id] - Get property by ID with full details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(async (req: any) => {
    try {
      const property = await client.fetch(
        groq`*[_type == "property" && _id == $id][0] {
          _id,
          title,
          description,
          propertyType,
          listingType,
          price,
          location,
          images,
          amenities,
          specifications,
          status,
          featured,
          verified,
          owner->{
            _id,
            firstName,
            lastName,
            email,
            phone
          },
          createdAt,
          updatedAt,
          publishedAt,
          views,
          favorites,
          bookings,
          rejectionReason,
          moderationNotes
        }`,
        { id: params.id }
      )
      
      if (!property) {
        return NextResponse.json(
          { success: false, error: 'Property not found' },
          { status: 404 }
        )
      }
      
      // Get related bookings
      const bookings = await client.fetch(
        groq`*[_type == "booking" && property._ref == $id] | order(createdAt desc) [0...10] {
          _id,
          status,
          checkIn,
          checkOut,
          totalAmount,
          guest->{
            _id,
            firstName,
            lastName,
            email
          },
          createdAt
        }`,
        { id: params.id }
      )
      
      return NextResponse.json({ 
        success: true, 
        data: { ...property, recentBookings: bookings } 
      })
    } catch (error) {
      console.error('Error fetching property:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch property' },
        { status: 500 }
      )
    }
  }, ['properties:read'])(request, NextResponse)
}

// PUT /api/admin/properties/[id] - Update property
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(async (req: any) => {
    try {
      const updates = await request.json()
      
      // Remove system fields that shouldn't be updated directly
      const { _id, _type, createdAt, ...safeUpdates } = updates
      
      const updatedProperty = await client.patch(params.id).set({
        ...safeUpdates,
        updatedAt: new Date().toISOString()
      }).commit()
      
      return NextResponse.json({ 
        success: true, 
        data: updatedProperty 
      })
    } catch (error) {
      console.error('Error updating property:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to update property' },
        { status: 500 }
      )
    }
  }, ['properties:update'])(request, NextResponse)
}

// DELETE /api/admin/properties/[id] - Delete property
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(async (req: any) => {
    try {
      // Check if property exists
      const property = await client.fetch(
        groq`*[_type == "property" && _id == $id][0]`,
        { id: params.id }
      )
      
      if (!property) {
        return NextResponse.json(
          { success: false, error: 'Property not found' },
          { status: 404 }
        )
      }
      
      // Check for active bookings
      const activeBookings = await client.fetch(
        groq`count(*[_type == "booking" && property._ref == $id && status in ["confirmed", "checked_in"]])`,
        { id: params.id }
      )
      
      if (activeBookings > 0) {
        return NextResponse.json(
          { success: false, error: 'Cannot delete property with active bookings' },
          { status: 409 }
        )
      }
      
      // Soft delete by updating status
      await client.patch(params.id).set({
        status: 'deleted',
        deletedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }).commit()
      
      return NextResponse.json({ 
        success: true, 
        message: 'Property deleted successfully' 
      })
    } catch (error) {
      console.error('Error deleting property:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to delete property' },
        { status: 500 }
      )
    }
  }, ['properties:delete'])(request, NextResponse)
}

// PATCH /api/admin/properties/[id] - Property moderation actions
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(async (req: any) => {
    try {
      const { action, ...data } = await request.json()
      
      let updates: any = {
        updatedAt: new Date().toISOString()
      }
      
      switch (action) {
        case 'approve':
          updates.status = 'active'
          updates.verified = true
          updates.publishedAt = new Date().toISOString()
          updates.moderationNotes = data.notes || ''
          break
          
        case 'reject':
          updates.status = 'rejected'
          updates.rejectedAt = new Date().toISOString()
          updates.rejectionReason = data.reason || 'No reason provided'
          updates.moderationNotes = data.notes || ''
          break
          
        case 'feature':
          updates.featured = true
          updates.featuredAt = new Date().toISOString()
          updates.featuredUntil = data.featuredUntil || null
          break
          
        case 'unfeature':
          updates.featured = false
          updates.featuredAt = null
          updates.featuredUntil = null
          break
          
        case 'verify':
          updates.verified = true
          updates.verifiedAt = new Date().toISOString()
          break
          
        case 'unverify':
          updates.verified = false
          updates.verifiedAt = null
          break
          
        case 'suspend':
          updates.status = 'suspended'
          updates.suspendedAt = new Date().toISOString()
          updates.suspensionReason = data.reason || 'No reason provided'
          break
          
        case 'unsuspend':
          updates.status = 'active'
          updates.suspendedAt = null
          updates.suspensionReason = null
          break
          
        default:
          return NextResponse.json(
            { success: false, error: 'Invalid action' },
            { status: 400 }
          )
      }
      
      const updatedProperty = await client.patch(params.id).set(updates).commit()
      
      return NextResponse.json({ 
        success: true, 
        data: updatedProperty 
      })
    } catch (error) {
      console.error('Error updating property:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to update property' },
        { status: 500 }
      )
    }
  }, ['properties:update'])(request, NextResponse)
}
