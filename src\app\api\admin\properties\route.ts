import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'

// GET /api/admin/properties - Get all properties with admin filters
export async function GET(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { searchParams } = new URL(request.url)
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '10')
      const search = searchParams.get('search') || ''
      const status = searchParams.get('status') || ''
      const propertyType = searchParams.get('propertyType') || ''
      const location = searchParams.get('location') || ''
      const sortBy = searchParams.get('sortBy') || 'createdAt'
      const sortOrder = searchParams.get('sortOrder') || 'desc'
      
      const offset = (page - 1) * limit
      
      // Build query filters
      let filters = ['_type == "property"']
      if (search) {
        filters.push(`(title match "${search}*" || description match "${search}*" || location.city match "${search}*")`)
      }
      if (status) {
        filters.push(`status == "${status}"`)
      }
      if (propertyType) {
        filters.push(`propertyType == "${propertyType}"`)
      }
      if (location) {
        filters.push(`location.city match "${location}*"`)
      }
      
      const filterQuery = filters.join(' && ')
      const orderQuery = `${sortBy} ${sortOrder}`
      
      // Get properties with pagination
      const properties = await client.fetch(
        groq`*[${filterQuery}] | order(${orderQuery}) [${offset}...${offset + limit}] {
          _id,
          title,
          description,
          propertyType,
          listingType,
          price,
          location,
          images,
          amenities,
          specifications,
          status,
          featured,
          verified,
          owner->{
            _id,
            firstName,
            lastName,
            email
          },
          createdAt,
          updatedAt,
          publishedAt,
          views,
          favorites,
          bookings
        }`
      )
      
      // Get total count
      const total = await client.fetch(
        groq`count(*[${filterQuery}])`
      )
      
      // Get status counts for dashboard
      const statusCounts = await client.fetch(
        groq`{
          "total": count(*[_type == "property"]),
          "active": count(*[_type == "property" && status == "active"]),
          "pending": count(*[_type == "property" && status == "pending"]),
          "rejected": count(*[_type == "property" && status == "rejected"]),
          "draft": count(*[_type == "property" && status == "draft"])
        }`
      )
      
      return NextResponse.json({
        success: true,
        data: {
          properties,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          },
          statusCounts
        }
      })
    } catch (error) {
      console.error('Error fetching properties:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch properties' },
        { status: 500 }
      )
    }
  }, ['properties:read'])(request, NextResponse)
}

// POST /api/admin/properties - Create new property (admin)
export async function POST(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const propertyData = await request.json()
      
      // Validate required fields
      if (!propertyData.title || !propertyData.propertyType || !propertyData.price) {
        return NextResponse.json(
          { success: false, error: 'Missing required fields' },
          { status: 400 }
        )
      }
      
      // Create property with admin privileges
      const property = await client.create({
        _type: 'property',
        ...propertyData,
        status: propertyData.status || 'active',
        verified: true, // Admin-created properties are auto-verified
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        publishedAt: new Date().toISOString()
      })
      
      return NextResponse.json({ success: true, data: property })
    } catch (error) {
      console.error('Error creating property:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to create property' },
        { status: 500 }
      )
    }
  }, ['properties:create'])(request, NextResponse)
}

// PATCH /api/admin/properties - Bulk operations
export async function PATCH(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { action, propertyIds, data } = await request.json()
      
      if (!action || !propertyIds || !Array.isArray(propertyIds)) {
        return NextResponse.json(
          { success: false, error: 'Invalid bulk operation data' },
          { status: 400 }
        )
      }
      
      let updates: any = {
        updatedAt: new Date().toISOString()
      }
      
      switch (action) {
        case 'approve':
          updates.status = 'active'
          updates.verified = true
          updates.publishedAt = new Date().toISOString()
          break
          
        case 'reject':
          updates.status = 'rejected'
          updates.rejectedAt = new Date().toISOString()
          updates.rejectionReason = data?.reason || 'No reason provided'
          break
          
        case 'feature':
          updates.featured = true
          updates.featuredAt = new Date().toISOString()
          break
          
        case 'unfeature':
          updates.featured = false
          updates.featuredAt = null
          break
          
        case 'delete':
          updates.status = 'deleted'
          updates.deletedAt = new Date().toISOString()
          break
          
        default:
          return NextResponse.json(
            { success: false, error: 'Invalid action' },
            { status: 400 }
          )
      }
      
      // Update all properties
      const updatePromises = propertyIds.map(id =>
        client.patch(id).set(updates).commit()
      )
      
      await Promise.all(updatePromises)
      
      return NextResponse.json({
        success: true,
        message: `${propertyIds.length} properties updated successfully`
      })
    } catch (error) {
      console.error('Error in bulk operation:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to perform bulk operation' },
        { status: 500 }
      )
    }
  }, ['properties:update'])(request, NextResponse)
}
