import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'
import { DEFAULT_PERMISSIONS } from '@/types/auth'

export async function POST(request: NextRequest) {
  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Setup only available in development' },
        { status: 403 }
      )
    }

    // Create admin user
    const adminUser = await authService.createUser({
      email: '<EMAIL>',
      password: 'admin123',
      firstName: 'Admin',
      lastName: 'User',
      role: 'admin',
      department: 'it',
      permissions: DEFAULT_PERMISSIONS.admin.map(permission => {
        const [resource, action] = permission.split(':')
        return { resource: resource || '', actions: [action || ''] }
      })
    })

    // Create employee user
    const employeeUser = await authService.createUser({
      email: '<EMAIL>',
      password: 'employee123',
      firstName: 'Employee',
      lastName: 'User',
      role: 'employee',
      department: 'operations',
      permissions: DEFAULT_PERMISSIONS.employee.map(permission => {
        const [resource, action] = permission.split(':')
        return { resource: resource || '', actions: [action || ''] }
      })
    })

    return NextResponse.json({
      success: true,
      message: 'Demo accounts created successfully',
      accounts: [
        {
          email: '<EMAIL>',
          password: 'admin123',
          role: 'admin'
        },
        {
          email: '<EMAIL>',
          password: 'employee123',
          role: 'employee'
        }
      ]
    })
  } catch (error) {
    console.error('Setup error:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Setup failed',
        success: false 
      },
      { status: 500 }
    )
  }
}
