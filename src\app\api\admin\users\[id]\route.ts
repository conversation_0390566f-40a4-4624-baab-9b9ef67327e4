import { NextRequest, NextResponse } from 'next/server'
import { authService, withAuth } from '@/lib/auth'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'

// GET /api/admin/users/[id] - Get user by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(async (req: any) => {
    try {
      const user = await client.fetch(
        groq`*[_type == "user" && _id == $id][0] {
          _id,
          email,
          firstName,
          lastName,
          role,
          status,
          phone,
          avatar,
          verified,
          createdAt,
          lastLogin,
          preferences,
          profile,
          permissions
        }`,
        { id: params.id }
      )
      
      if (!user) {
        return NextResponse.json(
          { success: false, error: 'User not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json({ success: true, data: user })
    } catch (error) {
      console.error('Error fetching user:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch user' },
        { status: 500 }
      )
    }
  }, ['users:read'])(request, NextResponse)
}

// PUT /api/admin/users/[id] - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(async (req: any) => {
    try {
      const updates = await request.json()
      
      // Remove sensitive fields that shouldn't be updated directly
      const { password, _id, _type, ...safeUpdates } = updates
      
      const updatedUser = await client.patch(params.id).set({
        ...safeUpdates,
        updatedAt: new Date().toISOString()
      }).commit()
      
      // Remove password from response
      const { password: _, ...userWithoutPassword } = updatedUser
      
      return NextResponse.json({ 
        success: true, 
        data: userWithoutPassword 
      })
    } catch (error) {
      console.error('Error updating user:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to update user' },
        { status: 500 }
      )
    }
  }, ['users:update'])(request, NextResponse)
}

// DELETE /api/admin/users/[id] - Delete user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(async (req: any) => {
    try {
      // Check if user exists
      const user = await client.fetch(
        groq`*[_type == "user" && _id == $id][0]`,
        { id: params.id }
      )
      
      if (!user) {
        return NextResponse.json(
          { success: false, error: 'User not found' },
          { status: 404 }
        )
      }
      
      // Prevent deleting admin users
      if (user.role === 'admin') {
        return NextResponse.json(
          { success: false, error: 'Cannot delete admin users' },
          { status: 403 }
        )
      }
      
      // Soft delete by updating status
      await client.patch(params.id).set({
        status: 'deleted',
        deletedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }).commit()
      
      return NextResponse.json({ 
        success: true, 
        message: 'User deleted successfully' 
      })
    } catch (error) {
      console.error('Error deleting user:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to delete user' },
        { status: 500 }
      )
    }
  }, ['users:delete'])(request, NextResponse)
}

// PATCH /api/admin/users/[id] - Partial update (e.g., ban/unban, verify)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(async (req: any) => {
    try {
      const { action, ...data } = await request.json()
      
      let updates: any = {
        updatedAt: new Date().toISOString()
      }
      
      switch (action) {
        case 'ban':
          updates.status = 'banned'
          updates.bannedAt = new Date().toISOString()
          updates.banReason = data.reason || 'No reason provided'
          break
          
        case 'unban':
          updates.status = 'active'
          updates.bannedAt = null
          updates.banReason = null
          break
          
        case 'verify':
          updates.verified = true
          updates.verifiedAt = new Date().toISOString()
          break
          
        case 'unverify':
          updates.verified = false
          updates.verifiedAt = null
          break
          
        case 'change_role':
          if (!data.role) {
            return NextResponse.json(
              { success: false, error: 'Role is required' },
              { status: 400 }
            )
          }
          updates.role = data.role
          break
          
        default:
          return NextResponse.json(
            { success: false, error: 'Invalid action' },
            { status: 400 }
          )
      }
      
      const updatedUser = await client.patch(params.id).set(updates).commit()
      
      // Remove password from response
      const { password, ...userWithoutPassword } = updatedUser
      
      return NextResponse.json({ 
        success: true, 
        data: userWithoutPassword 
      })
    } catch (error) {
      console.error('Error updating user:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to update user' },
        { status: 500 }
      )
    }
  }, ['users:update'])(request, NextResponse)
}
