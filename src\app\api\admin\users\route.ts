import { NextRequest, NextResponse } from 'next/server'
import { authService, withAuth } from '@/lib/auth'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'

// GET /api/admin/users - Get all users with pagination and filtering
export async function GET(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { searchParams } = new URL(request.url)
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '10')
      const search = searchParams.get('search') || ''
      const role = searchParams.get('role') || ''
      const status = searchParams.get('status') || ''
      
      const offset = (page - 1) * limit
      
      // Build query filters
      let filters = ['_type == "user"']
      if (search) {
        filters.push(`(firstName match "${search}*" || lastName match "${search}*" || email match "${search}*")`)
      }
      if (role) {
        filters.push(`role == "${role}"`)
      }
      if (status) {
        filters.push(`status == "${status}"`)
      }
      
      const filterQuery = filters.join(' && ')
      
      // Get users with pagination
      const users = await client.fetch(
        groq`*[${filterQuery}] | order(createdAt desc) [${offset}...${offset + limit}] {
          _id,
          email,
          firstName,
          lastName,
          role,
          status,
          phone,
          avatar,
          verified,
          createdAt,
          lastLogin,
          preferences,
          profile
        }`
      )
      
      // Get total count
      const total = await client.fetch(
        groq`count(*[${filterQuery}])`
      )
      
      return NextResponse.json({
        success: true,
        data: {
          users,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      })
    } catch (error) {
      console.error('Error fetching users:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch users' },
        { status: 500 }
      )
    }
  }, ['users:read'])(request, NextResponse)
}

// POST /api/admin/users - Create new user
export async function POST(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const userData = await request.json()
      
      // Validate required fields
      if (!userData.email || !userData.password || !userData.firstName || !userData.lastName) {
        return NextResponse.json(
          { success: false, error: 'Missing required fields' },
          { status: 400 }
        )
      }
      
      // Check if user already exists
      const existingUser = await client.fetch(
        groq`*[_type == "user" && email == $email][0]`,
        { email: userData.email }
      )
      
      if (existingUser) {
        return NextResponse.json(
          { success: false, error: 'User with this email already exists' },
          { status: 409 }
        )
      }
      
      const user = await authService.createUser(userData)
      return NextResponse.json({ success: true, data: user })
    } catch (error) {
      console.error('Error creating user:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to create user' },
        { status: 500 }
      )
    }
  }, ['users:create'])(request, NextResponse)
}
