import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'
import { LoginCredentials } from '@/types/auth'

export async function POST(request: NextRequest) {
  try {
    const credentials: LoginCredentials = await request.json()

    if (!credentials.email || !credentials.password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    const session = await authService.login(credentials)

    return NextResponse.json({
      success: true,
      session,
      message: 'Login successful'
    })
  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Login failed',
        success: false 
      },
      { status: 401 }
    )
  }
}
