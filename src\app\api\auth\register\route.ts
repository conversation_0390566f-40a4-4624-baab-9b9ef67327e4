import { NextRequest, NextResponse } from 'next/server'
import { authService, withAuth } from '@/lib/auth'
import { CreateUserRequest } from '@/types/auth'
import { PERMISSIONS } from '@/types/auth'

export const POST = withAuth(async (request: NextRequest) => {
  try {
    const userData: CreateUserRequest = await request.json()

    // Validate required fields
    if (!userData.email || !userData.password || !userData.firstName || !userData.lastName || !userData.role) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(userData.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate password strength
    if (userData.password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      )
    }

    const user = await authService.createUser(userData)

    return NextResponse.json({
      success: true,
      user,
      message: 'User created successfully'
    })
  } catch (error) {
    console.error('User creation error:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to create user',
        success: false 
      },
      { status: 500 }
    )
  }
}, [PERMISSIONS.USERS_CREATE])
