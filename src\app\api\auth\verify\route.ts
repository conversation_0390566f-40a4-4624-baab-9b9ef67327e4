import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json()

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      )
    }

    const user = await authService.verifyToken(token)

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    return NextResponse.json({
      success: true,
      user,
      message: 'Token is valid'
    })
  } catch (error) {
    console.error('Token verification error:', error)
    return NextResponse.json(
      { 
        error: 'Token verification failed',
        success: false 
      },
      { status: 500 }
    )
  }
}
