import { NextRequest, NextResponse } from 'next/server'
import { bookingService } from '@/lib/booking'
import { BookingRequest } from '@/types/booking'

export async function POST(request: NextRequest) {
  try {
    const body: BookingRequest = await request.json()

    // Validate required fields
    if (!body.type || !body.contact || !body.guests) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Create booking
    const booking = await bookingService.createBooking(body)

    return NextResponse.json({
      success: true,
      booking: {
        id: booking._id,
        bookingNumber: booking.bookingNumber,
        type: booking.type,
        status: booking.status,
        pricing: booking.pricing,
        createdAt: booking.createdAt
      }
    })
  } catch (error) {
    console.error('Booking creation error:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to create booking',
        success: false 
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const bookingNumber = searchParams.get('bookingNumber')
    const email = searchParams.get('email')

    if (!bookingNumber && !email) {
      return NextResponse.json(
        { error: 'Booking number or email is required' },
        { status: 400 }
      )
    }

    // In a real implementation, you would fetch from Sanity
    // For now, return a placeholder response
    return NextResponse.json({
      success: true,
      message: 'Booking lookup endpoint ready'
    })
  } catch (error) {
    console.error('Booking lookup error:', error)
    return NextResponse.json(
      { error: 'Failed to lookup booking' },
      { status: 500 }
    )
  }
}
