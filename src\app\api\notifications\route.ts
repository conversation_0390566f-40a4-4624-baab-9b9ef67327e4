import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'
import { sendTemplatedEmail } from '@/lib/email'

// GET /api/notifications - Get user notifications
export async function GET(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { searchParams } = new URL(request.url)
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '20')
      const unreadOnly = searchParams.get('unreadOnly') === 'true'
      const type = searchParams.get('type')
      
      const offset = (page - 1) * limit
      
      // Build query filters
      let filters = [`user._ref == "${req.user.userId}"`]
      if (unreadOnly) {
        filters.push('read == false')
      }
      if (type) {
        filters.push(`type == "${type}"`)
      }
      
      const filterQuery = filters.join(' && ')
      
      // Get notifications with pagination
      const notifications = await client.fetch(
        groq`*[_type == "notification" && ${filterQuery}] | order(createdAt desc) [${offset}...${offset + limit}] {
          _id,
          title,
          message,
          type,
          read,
          data,
          createdAt,
          readAt
        }`
      )
      
      // Get total count
      const total = await client.fetch(
        groq`count(*[_type == "notification" && ${filterQuery}])`
      )
      
      // Get unread count
      const unreadCount = await client.fetch(
        groq`count(*[_type == "notification" && user._ref == "${req.user.userId}" && read == false])`
      )
      
      return NextResponse.json({
        success: true,
        data: {
          notifications,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          },
          unreadCount
        }
      })
    } catch (error) {
      console.error('Get notifications error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch notifications' },
        { status: 500 }
      )
    }
  }, ['notifications:read'])(request, NextResponse)
}

// POST /api/notifications - Create notification (admin only)
export async function POST(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { userId, title, message, type, data, sendEmail } = await request.json()
      
      // Validate required fields
      if (!userId || !title || !message || !type) {
        return NextResponse.json(
          { success: false, error: 'Missing required fields' },
          { status: 400 }
        )
      }
      
      // Create notification
      const notification = await client.create({
        _type: 'notification',
        user: { _type: 'reference', _ref: userId },
        title,
        message,
        type,
        data: data || {},
        read: false,
        createdAt: new Date().toISOString()
      })
      
      // Send email notification if requested
      if (sendEmail) {
        try {
          const user = await client.fetch(
            groq`*[_type == "user" && _id == $userId][0] {
              firstName,
              email,
              preferences
            }`,
            { userId }
          )
          
          if (user?.email && user?.preferences?.notifications?.email !== false) {
            await sendTemplatedEmail(
              'notification',
              user.email,
              {
                firstName: user.firstName,
                title,
                message,
                type
              }
            )
          }
        } catch (emailError) {
          console.error('Failed to send notification email:', emailError)
        }
      }
      
      return NextResponse.json({
        success: true,
        data: notification
      })
    } catch (error) {
      console.error('Create notification error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to create notification' },
        { status: 500 }
      )
    }
  }, ['notifications:create'])(request, NextResponse)
}

// PATCH /api/notifications - Bulk mark as read/unread
export async function PATCH(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { action, notificationIds } = await request.json()
      
      if (!action || !Array.isArray(notificationIds)) {
        return NextResponse.json(
          { success: false, error: 'Invalid request data' },
          { status: 400 }
        )
      }
      
      let updates: any = {
        updatedAt: new Date().toISOString()
      }
      
      switch (action) {
        case 'mark_read':
          updates.read = true
          updates.readAt = new Date().toISOString()
          break
          
        case 'mark_unread':
          updates.read = false
          updates.readAt = null
          break
          
        case 'delete':
          // Soft delete notifications
          updates.deleted = true
          updates.deletedAt = new Date().toISOString()
          break
          
        default:
          return NextResponse.json(
            { success: false, error: 'Invalid action' },
            { status: 400 }
          )
      }
      
      // Update all specified notifications
      const updatePromises = notificationIds.map(id =>
        client.patch(id).set(updates).commit()
      )
      
      await Promise.all(updatePromises)
      
      return NextResponse.json({
        success: true,
        message: `${notificationIds.length} notifications updated`
      })
    } catch (error) {
      console.error('Bulk notification update error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to update notifications' },
        { status: 500 }
      )
    }
  }, ['notifications:update'])(request, NextResponse)
}

// Notification service functions
export class NotificationService {
  // Create notification for user
  static async createNotification(
    userId: string,
    title: string,
    message: string,
    type: string,
    data?: any,
    sendEmail: boolean = false
  ): Promise<void> {
    try {
      await client.create({
        _type: 'notification',
        user: { _type: 'reference', _ref: userId },
        title,
        message,
        type,
        data: data || {},
        read: false,
        createdAt: new Date().toISOString()
      })
      
      // Send email if requested
      if (sendEmail) {
        const user = await client.fetch(
          groq`*[_type == "user" && _id == $userId][0] {
            firstName,
            email,
            preferences
          }`,
          { userId }
        )
        
        if (user?.email && user?.preferences?.notifications?.email !== false) {
          await sendTemplatedEmail(
            'notification',
            user.email,
            {
              firstName: user.firstName,
              title,
              message,
              type
            }
          )
        }
      }
    } catch (error) {
      console.error('Failed to create notification:', error)
    }
  }
  
  // Send booking confirmation notification
  static async sendBookingConfirmation(bookingId: string): Promise<void> {
    try {
      const booking = await client.fetch(
        groq`*[_type == "booking" && _id == $bookingId][0] {
          guest->{
            _id,
            firstName,
            email
          },
          property->{
            title
          },
          checkIn,
          checkOut,
          totalAmount
        }`,
        { bookingId }
      )
      
      if (booking?.guest) {
        await this.createNotification(
          booking.guest._id,
          'Booking Confirmed',
          `Your booking for ${booking.property.title} has been confirmed.`,
          'booking_confirmed',
          { bookingId, propertyTitle: booking.property.title },
          true
        )
      }
    } catch (error) {
      console.error('Failed to send booking confirmation notification:', error)
    }
  }
  
  // Send property approval notification
  static async sendPropertyApproval(propertyId: string): Promise<void> {
    try {
      const property = await client.fetch(
        groq`*[_type == "property" && _id == $propertyId][0] {
          title,
          owner->{
            _id,
            firstName,
            email
          }
        }`,
        { propertyId }
      )
      
      if (property?.owner) {
        await this.createNotification(
          property.owner._id,
          'Property Approved',
          `Your property "${property.title}" has been approved and is now live.`,
          'property_approved',
          { propertyId, propertyTitle: property.title },
          true
        )
      }
    } catch (error) {
      console.error('Failed to send property approval notification:', error)
    }
  }
}
