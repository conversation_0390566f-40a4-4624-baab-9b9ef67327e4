import { NextRequest, NextResponse } from 'next/server'
import { PaymentService } from '@/lib/payments'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'
import { sendTemplatedEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const callbackData = await request.json()

    console.log('M-Pesa Callback received:', JSON.stringify(callbackData, null, 2))

    const { Body } = callbackData
    const { stkCallback } = Body

    const {
      MerchantRequestID,
      CheckoutRequestID,
      ResultCode,
      ResultDesc,
      CallbackMetadata
    } = stkCallback

    // Extract payment details from callback metadata
    let amount = 0
    let mpesaReceiptNumber = ''
    let transactionDate = ''
    let phoneNumber = ''

    if (CallbackMetadata && CallbackMetadata.Item) {
      CallbackMetadata.Item.forEach((item: any) => {
        switch (item.Name) {
          case 'Amount':
            amount = item.Value
            break
          case 'MpesaReceiptNumber':
            mpesaReceiptNumber = item.Value
            break
          case 'TransactionDate':
            transactionDate = item.Value
            break
          case 'PhoneNumber':
            phoneNumber = item.Value
            break
        }
      })
    }

    // Determine payment status
    const paymentStatus = ResultCode === 0 ? 'succeeded' : 'failed'

    // Find the payment record
    const payment = await client.fetch(
      groq`*[_type == "payment" && paymentId == $checkoutRequestId][0] {
        _id,
        booking->{
          _id,
          guest->{
            _id,
            firstName,
            lastName,
            email
          },
          property->{
            _id,
            title
          },
          checkIn,
          checkOut,
          totalAmount
        },
        amount,
        status
      }`,
      { checkoutRequestId: CheckoutRequestID }
    )

    if (!payment) {
      console.warn(`Payment record not found for CheckoutRequestID: ${CheckoutRequestID}`)
      return NextResponse.json({ ResultCode: 0, ResultDesc: 'Payment not found but acknowledged' })
    }

    // Update booking based on payment status
    if (paymentStatus === 'succeeded') {
      // Payment successful
      await client.patch(payment.booking._id).set({
        'payment.status': 'completed',
        'payment.transactionId': mpesaReceiptNumber,
        'payment.mpesaReceiptNumber': mpesaReceiptNumber,
        'payment.paidAt': new Date().toISOString(),
        'status': 'confirmed',
        'confirmedAt': new Date().toISOString(),
        'updatedAt': new Date().toISOString()
      }).commit()

      // Send confirmation email (implement email service)
      await sendBookingConfirmation(payment.booking)

      console.log(`Payment successful for booking ${payment.booking._id}`)
    } else {
      // Payment failed
      await client.patch(payment.booking._id).set({
        'payment.status': 'failed',
        'status': 'cancelled',
        'cancelledAt': new Date().toISOString(),
        'cancellationReason': ResultDesc,
        'updatedAt': new Date().toISOString()
      }).commit()

      console.log(`Payment failed for booking ${payment.booking._id}: ${ResultDesc}`)
    }

    // Respond to M-Pesa
    return NextResponse.json({
      ResultCode: 0,
      ResultDesc: 'Callback processed successfully'
    })
  } catch (error) {
    console.error('M-Pesa callback error:', error)
    return NextResponse.json({
      ResultCode: 1,
      ResultDesc: 'Callback processing failed'
    })
  }
}

// Email service placeholder
async function sendBookingConfirmation(booking: any) {
  // Implement email sending logic here
  // You can use services like SendGrid, Mailgun, or AWS SES
  console.log(`Sending confirmation email for booking ${booking.bookingNumber}`)
  
  // Example email content
  const emailContent = {
    to: booking.contact.email,
    subject: `Booking Confirmation - ${booking.bookingNumber}`,
    html: `
      <h2>Booking Confirmed!</h2>
      <p>Dear ${booking.contact.firstName},</p>
      <p>Your booking has been confirmed. Here are the details:</p>
      <ul>
        <li>Booking Number: ${booking.bookingNumber}</li>
        <li>Type: ${booking.type}</li>
        <li>Total Amount: KES ${booking.pricing.totalPrice}</li>
        <li>Status: Confirmed</li>
      </ul>
      <p>Thank you for choosing RiftStays!</p>
    `
  }
  
  // Send email using your preferred service
  // await emailService.send(emailContent)
}
