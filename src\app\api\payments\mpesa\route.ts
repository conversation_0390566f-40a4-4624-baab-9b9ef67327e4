import { NextRequest, NextResponse } from 'next/server'
import { bookingService } from '@/lib/booking'

export async function POST(request: NextRequest) {
  try {
    const { bookingId, phoneNumber } = await request.json()

    if (!bookingId || !phoneNumber) {
      return NextResponse.json(
        { error: 'Booking ID and phone number are required' },
        { status: 400 }
      )
    }

    // Initiate M-Pesa payment
    const paymentResponse = await bookingService.initiatePayment(bookingId, phoneNumber)

    return NextResponse.json({
      success: true,
      message: 'Payment initiated successfully',
      checkoutRequestId: paymentResponse.CheckoutRequestID,
      customerMessage: paymentResponse.CustomerMessage
    })
  } catch (error) {
    console.error('M-Pesa payment error:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to initiate payment',
        success: false 
      },
      { status: 500 }
    )
  }
}
