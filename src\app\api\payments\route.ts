import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth'
import { PaymentService, StripePaymentService, MPesaPaymentService } from '@/lib/payments'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'

// POST /api/payments - Create payment intent
export async function POST(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { amount, currency, paymentMethod, bookingId, metadata, phoneNumber, accountReference } = await request.json()

      // Validate required fields
      if (!amount || !paymentMethod || !bookingId) {
        return NextResponse.json(
          { success: false, error: 'Missing required fields' },
          { status: 400 }
        )
      }

      // Verify booking exists and belongs to user
      const booking = await client.fetch(
        groq`*[_type == "booking" && _id == $bookingId && guest._ref == $userId][0]`,
        { bookingId, userId: req.user.userId }
      )

      if (!booking) {
        return NextResponse.json(
          { success: false, error: 'Booking not found or access denied' },
          { status: 404 }
        )
      }

      let result
      
      if (paymentMethod === 'stripe') {
        // Create Stripe payment intent
        const paymentIntent = await StripePaymentService.createPaymentIntent(
          amount,
          currency || 'kes',
          { bookingId, userId: req.user.userId, ...metadata }
        )
        
        result = {
          success: true,
          paymentId: paymentIntent.id,
          clientSecret: paymentIntent.clientSecret,
          status: paymentIntent.status,
          data: paymentIntent
        }
      } else if (paymentMethod === 'mpesa') {
        // Validate M-Pesa specific fields
        if (!phoneNumber || !accountReference) {
          return NextResponse.json(
            { success: false, error: 'Phone number and account reference required for M-Pesa' },
            { status: 400 }
          )
        }
        
        // Initiate M-Pesa STK Push
        result = await MPesaPaymentService.initiateSTKPush({
          phoneNumber,
          amount,
          accountReference,
          transactionDesc: `Payment for booking ${bookingId}`
        })
      } else {
        return NextResponse.json(
          { success: false, error: 'Unsupported payment method' },
          { status: 400 }
        )
      }

      // Save payment record if successful
      if (result.success) {
        await PaymentService.savePaymentRecord({
          paymentId: result.paymentId,
          bookingId,
          userId: req.user.userId,
          amount,
          currency: currency || 'KES',
          method: paymentMethod,
          status: result.status,
          metadata: { ...metadata, phoneNumber, accountReference }
        })
      }

      return NextResponse.json(result)
    } catch (error) {
      console.error('Payment creation error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to create payment' },
        { status: 500 }
      )
    }
  }, ['payments:create'])(request, NextResponse)
}

// GET /api/payments - Get user's payment history
export async function GET(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { searchParams } = new URL(request.url)
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '10')
      const status = searchParams.get('status')
      
      const offset = (page - 1) * limit
      
      // Build query filters
      let filters = [`user._ref == "${req.user.userId}"`]
      if (status) {
        filters.push(`status == "${status}"`)
      }
      
      const filterQuery = filters.join(' && ')
      
      // Get payments with pagination
      const payments = await client.fetch(
        groq`*[_type == "payment" && ${filterQuery}] | order(createdAt desc) [${offset}...${offset + limit}] {
          _id,
          paymentId,
          amount,
          currency,
          method,
          status,
          booking->{
            _id,
            property->{
              _id,
              title,
              images[0]
            },
            checkIn,
            checkOut,
            totalAmount
          },
          createdAt,
          updatedAt,
          metadata
        }`
      )
      
      // Get total count
      const total = await client.fetch(
        groq`count(*[_type == "payment" && ${filterQuery}])`
      )
      
      return NextResponse.json({
        success: true,
        data: {
          payments,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      })
    } catch (error) {
      console.error('Get payments error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch payments' },
        { status: 500 }
      )
    }
  }, ['payments:read'])(request, NextResponse)
}
