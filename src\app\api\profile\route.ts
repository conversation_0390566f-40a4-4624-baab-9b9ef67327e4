import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'

// GET /api/profile - Get current user profile
export async function GET(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const user = await client.fetch(
        groq`*[_type == "user" && _id == $userId][0] {
          _id,
          email,
          firstName,
          lastName,
          role,
          status,
          phone,
          avatar,
          verified,
          createdAt,
          lastLogin,
          preferences,
          profile,
          documents,
          notifications
        }`,
        { userId: req.user.userId }
      )
      
      if (!user) {
        return NextResponse.json(
          { success: false, error: 'User not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json({
        success: true,
        data: user
      })
    } catch (error) {
      console.error('Get profile error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch profile' },
        { status: 500 }
      )
    }
  }, ['profile:read'])(request, NextResponse)
}

// PUT /api/profile - Update user profile
export async function PUT(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const updates = await request.json()
      
      // Remove sensitive fields that shouldn't be updated directly
      const { _id, _type, email, role, status, createdAt, ...safeUpdates } = updates
      
      // Validate phone number format if provided
      if (safeUpdates.phone) {
        const phoneRegex = /^(\+254|0)[17]\d{8}$/
        if (!phoneRegex.test(safeUpdates.phone)) {
          return NextResponse.json(
            { success: false, error: 'Invalid phone number format' },
            { status: 400 }
          )
        }
      }
      
      const updatedUser = await client.patch(req.user.userId).set({
        ...safeUpdates,
        updatedAt: new Date().toISOString()
      }).commit()
      
      // Remove password from response
      const { password, ...userWithoutPassword } = updatedUser
      
      return NextResponse.json({
        success: true,
        data: userWithoutPassword
      })
    } catch (error) {
      console.error('Update profile error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to update profile' },
        { status: 500 }
      )
    }
  }, ['profile:update'])(request, NextResponse)
}

// PATCH /api/profile - Partial profile updates (preferences, notifications, etc.)
export async function PATCH(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { action, data } = await request.json()
      
      let updates: any = {
        updatedAt: new Date().toISOString()
      }
      
      switch (action) {
        case 'update_preferences':
          updates.preferences = {
            ...data.preferences
          }
          break
          
        case 'update_notifications':
          updates.notifications = {
            ...data.notifications
          }
          break
          
        case 'update_avatar':
          updates.avatar = data.avatar
          break
          
        case 'add_document':
          // Get current user to append to existing documents
          const currentUser = await client.fetch(
            groq`*[_type == "user" && _id == $userId][0].documents`,
            { userId: req.user.userId }
          )
          
          updates.documents = [
            ...(currentUser || []),
            {
              type: data.type,
              url: data.url,
              filename: data.filename,
              uploadedAt: new Date().toISOString(),
              verified: false
            }
          ]
          break
          
        case 'remove_document':
          const user = await client.fetch(
            groq`*[_type == "user" && _id == $userId][0].documents`,
            { userId: req.user.userId }
          )
          
          updates.documents = (user || []).filter((doc: any) => doc.url !== data.url)
          break
          
        default:
          return NextResponse.json(
            { success: false, error: 'Invalid action' },
            { status: 400 }
          )
      }
      
      const updatedUser = await client.patch(req.user.userId).set(updates).commit()
      
      // Remove password from response
      const { password, ...userWithoutPassword } = updatedUser
      
      return NextResponse.json({
        success: true,
        data: userWithoutPassword
      })
    } catch (error) {
      console.error('Patch profile error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to update profile' },
        { status: 500 }
      )
    }
  }, ['profile:update'])(request, NextResponse)
}
