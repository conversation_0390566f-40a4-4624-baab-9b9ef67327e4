import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'

// GET /api/profile/stats - Get user statistics
export async function GET(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const userId = req.user.userId
      
      // Get user role to determine which stats to fetch
      const user = await client.fetch(
        groq`*[_type == "user" && _id == $userId][0].role`,
        { userId }
      )
      
      // Base stats for all users
      const baseStats = await client.fetch(
        groq`{
          "totalBookings": count(*[_type == "booking" && guest._ref == $userId]),
          "upcomingBookings": count(*[_type == "booking" && guest._ref == $userId && status == "confirmed" && checkIn > now()]),
          "completedBookings": count(*[_type == "booking" && guest._ref == $userId && status == "completed"]),
          "cancelledBookings": count(*[_type == "booking" && guest._ref == $userId && status == "cancelled"]),
          "savedProperties": count(*[_type == "favorite" && user._ref == $userId && type == "property"]),
          "savedTours": count(*[_type == "favorite" && user._ref == $userId && type == "tour"]),
          "savedSearches": count(*[_type == "savedSearch" && user._ref == $userId]),
          "unreadNotifications": count(*[_type == "notification" && user._ref == $userId && read == false]),
          "totalSpent": sum(*[_type == "booking" && guest._ref == $userId && paymentStatus == "paid"].totalAmount)
        }`,
        { userId }
      )
      
      let additionalStats = {}
      
      // Additional stats for property owners
      if (user === 'property_owner') {
        additionalStats = await client.fetch(
          groq`{
            "activeListings": count(*[_type == "property" && owner._ref == $userId && status == "active"]),
            "pendingListings": count(*[_type == "property" && owner._ref == $userId && status == "pending"]),
            "totalListings": count(*[_type == "property" && owner._ref == $userId]),
            "totalEarnings": sum(*[_type == "booking" && property->owner._ref == $userId && paymentStatus == "paid"].totalAmount),
            "monthlyEarnings": sum(*[_type == "booking" && property->owner._ref == $userId && paymentStatus == "paid" && createdAt > dateTime(now()) - 86400*30].totalAmount),
            "totalViews": sum(*[_type == "property" && owner._ref == $userId].views),
            "totalBookings": count(*[_type == "booking" && property->owner._ref == $userId]),
            "averageRating": avg(*[_type == "review" && property->owner._ref == $userId].rating),
            "totalReviews": count(*[_type == "review" && property->owner._ref == $userId])
          }`,
          { userId }
        )
      }
      
      // Recent activity
      const recentActivity = await client.fetch(
        groq`{
          "recentBookings": *[_type == "booking" && guest._ref == $userId] | order(createdAt desc)[0...5] {
            _id,
            status,
            checkIn,
            checkOut,
            totalAmount,
            property->{
              _id,
              title,
              images[0]
            },
            createdAt
          },
          "recentFavorites": *[_type == "favorite" && user._ref == $userId] | order(createdAt desc)[0...5] {
            _id,
            type,
            property->{
              _id,
              title,
              images[0],
              price
            },
            tour->{
              _id,
              title,
              images[0],
              price
            },
            createdAt
          },
          "recentNotifications": *[_type == "notification" && user._ref == $userId] | order(createdAt desc)[0...5] {
            _id,
            title,
            message,
            type,
            read,
            createdAt
          }
        }`,
        { userId }
      )
      
      // Booking trends (last 12 months)
      const bookingTrends = await client.fetch(
        groq`*[_type == "booking" && guest._ref == $userId && createdAt > dateTime(now()) - 86400*365] {
          "month": dateTime(createdAt).month,
          "year": dateTime(createdAt).year,
          "amount": totalAmount,
          "status": status
        } | group([year, month]) | {
          "period": key,
          "bookings": count(@),
          "totalAmount": sum(amount),
          "confirmed": count(@[status == "confirmed"]),
          "completed": count(@[status == "completed"]),
          "cancelled": count(@[status == "cancelled"])
        } | order(period desc)[0...12]`,
        { userId }
      )
      
      // Property owner specific recent activity
      let ownerActivity = {}
      if (user === 'property_owner') {
        ownerActivity = await client.fetch(
          groq`{
            "recentPropertyBookings": *[_type == "booking" && property->owner._ref == $userId] | order(createdAt desc)[0...5] {
              _id,
              status,
              checkIn,
              checkOut,
              totalAmount,
              guest->{
                _id,
                firstName,
                lastName
              },
              property->{
                _id,
                title
              },
              createdAt
            },
            "recentReviews": *[_type == "review" && property->owner._ref == $userId] | order(createdAt desc)[0...5] {
              _id,
              rating,
              comment,
              guest->{
                _id,
                firstName,
                lastName
              },
              property->{
                _id,
                title
              },
              createdAt
            }
          }`,
          { userId }
        )
      }
      
      return NextResponse.json({
        success: true,
        data: {
          ...baseStats,
          ...additionalStats,
          recentActivity,
          bookingTrends,
          ...ownerActivity,
          userRole: user,
          generatedAt: new Date().toISOString()
        }
      })
    } catch (error) {
      console.error('Get profile stats error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch profile statistics' },
        { status: 500 }
      )
    }
  }, ['profile:read'])(request, NextResponse)
}
