import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth'
import { client } from '@/sanity/lib/client'
import { v4 as uuidv4 } from 'uuid'
import { NotificationService } from '@/app/api/notifications/route'

export async function POST(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const contentType = request.headers.get('content-type')
      let propertyData: any

      if (contentType?.includes('application/json')) {
        propertyData = await request.json()
      } else {
        // Handle FormData for file uploads
        const formData = await request.formData()

        // Extract form fields
        propertyData = {
          title: formData.get('title') as string,
          description: formData.get('description') as string,
          price: {
            amount: Number(formData.get('price')),
            currency: formData.get('currency') as string || 'KES',
            period: formData.get('period') as string || 'monthly'
          },
          location: JSON.parse(formData.get('location') as string || '{}'),
          specifications: {
            bedrooms: Number(formData.get('bedrooms')),
            bathrooms: Number(formData.get('bathrooms')),
            area: Number(formData.get('area')),
            areaUnit: formData.get('areaUnit') as string || 'sqft'
          },
          propertyType: formData.get('propertyType') as string,
          listingType: formData.get('listingType') as string,
          amenities: JSON.parse(formData.get('amenities') as string || '[]'),
          policies: JSON.parse(formData.get('policies') as string || '{}'),
          availability: JSON.parse(formData.get('availability') as string || '{}')
        }

        // Handle image uploads
        const imageFiles = formData.getAll('images') as File[]
        if (imageFiles.length > 0) {
          const imageAssets = await Promise.all(
            imageFiles.map(async (file) => {
              const bytes = await file.arrayBuffer()
              const buffer = Buffer.from(bytes)

              const asset = await client.assets.upload('image', buffer, {
                filename: file.name,
              })

              return {
                _type: 'image',
                asset: {
                  _type: 'reference',
                  _ref: asset._id,
                },
              }
            })
          )
          propertyData.images = imageAssets
        }
      }

      // Create pending property document
      const pendingProperty = {
        _id: `pendingProperty.${uuidv4()}`,
        _type: 'pendingProperty',
        ...propertyData,
        status: 'pending',
        submittedAt: new Date().toISOString(),
      }

      // Save to Sanity
      await client.create(pendingProperty)

      // Send email notification (you can implement this later)
      // await sendPropertySubmissionEmail(pendingProperty)

      return NextResponse.json({ success: true })
    } catch (error) {
      console.error('Error submitting property:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to submit property' },
        { status: 500 }
      )
    }
  }, ['properties:create'])(request, NextResponse)
}