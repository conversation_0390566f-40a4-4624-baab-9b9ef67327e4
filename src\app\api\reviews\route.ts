import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'
import { NotificationService } from '@/app/api/notifications/route'

// GET /api/reviews - Get reviews with filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const propertyId = searchParams.get('propertyId')
    const tourId = searchParams.get('tourId')
    const userId = searchParams.get('userId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    
    const offset = (page - 1) * limit
    
    // Build query filters
    let filters = ['_type == "review"']
    if (propertyId) {
      filters.push(`property._ref == "${propertyId}"`)
    }
    if (tourId) {
      filters.push(`tour._ref == "${tourId}"`)
    }
    if (userId) {
      filters.push(`guest._ref == "${userId}"`)
    }
    
    const filterQuery = filters.join(' && ')
    const orderQuery = `${sortBy} ${sortOrder}`
    
    // Get reviews with pagination
    const reviews = await client.fetch(
      groq`*[${filterQuery}] | order(${orderQuery}) [${offset}...${offset + limit}] {
        _id,
        rating,
        comment,
        ratings: {
          cleanliness,
          communication,
          checkIn,
          accuracy,
          location,
          value
        },
        guest->{
          _id,
          firstName,
          lastName,
          avatar
        },
        property->{
          _id,
          title,
          images[0]
        },
        tour->{
          _id,
          title,
          images[0]
        },
        booking->{
          _id,
          checkIn,
          checkOut
        },
        helpful,
        reported,
        verified,
        response,
        createdAt,
        updatedAt
      }`
    )
    
    // Get total count
    const total = await client.fetch(
      groq`count(*[${filterQuery}])`
    )
    
    // Get average rating if for specific property/tour
    let averageRating = null
    if (propertyId || tourId) {
      averageRating = await client.fetch(
        groq`{
          "overall": avg(*[${filterQuery}].rating),
          "cleanliness": avg(*[${filterQuery}].ratings.cleanliness),
          "communication": avg(*[${filterQuery}].ratings.communication),
          "checkIn": avg(*[${filterQuery}].ratings.checkIn),
          "accuracy": avg(*[${filterQuery}].ratings.accuracy),
          "location": avg(*[${filterQuery}].ratings.location),
          "value": avg(*[${filterQuery}].ratings.value),
          "totalReviews": count(*[${filterQuery}])
        }`
      )
    }
    
    return NextResponse.json({
      success: true,
      data: {
        reviews,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        averageRating
      }
    })
  } catch (error) {
    console.error('Get reviews error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch reviews' },
      { status: 500 }
    )
  }
}

// POST /api/reviews - Create new review
export async function POST(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const reviewData = await request.json()
      
      // Validate required fields
      if (!reviewData.rating || !reviewData.comment || (!reviewData.propertyId && !reviewData.tourId) || !reviewData.bookingId) {
        return NextResponse.json(
          { success: false, error: 'Missing required fields' },
          { status: 400 }
        )
      }
      
      // Validate rating range
      if (reviewData.rating < 1 || reviewData.rating > 5) {
        return NextResponse.json(
          { success: false, error: 'Rating must be between 1 and 5' },
          { status: 400 }
        )
      }
      
      // Check if booking exists and belongs to user
      const booking = await client.fetch(
        groq`*[_type == "booking" && _id == $bookingId && guest._ref == $userId][0] {
          _id,
          status,
          property->{
            _id,
            title,
            owner->{_id}
          },
          tour->{
            _id,
            title,
            operator->{_id}
          }
        }`,
        { bookingId: reviewData.bookingId, userId: req.user.userId }
      )
      
      if (!booking) {
        return NextResponse.json(
          { success: false, error: 'Booking not found or access denied' },
          { status: 404 }
        )
      }
      
      // Check if booking is completed
      if (booking.status !== 'completed') {
        return NextResponse.json(
          { success: false, error: 'Can only review completed bookings' },
          { status: 400 }
        )
      }
      
      // Check if review already exists for this booking
      const existingReview = await client.fetch(
        groq`*[_type == "review" && booking._ref == $bookingId][0]`,
        { bookingId: reviewData.bookingId }
      )
      
      if (existingReview) {
        return NextResponse.json(
          { success: false, error: 'Review already exists for this booking' },
          { status: 409 }
        )
      }
      
      // Create review
      const review = await client.create({
        _type: 'review',
        rating: reviewData.rating,
        comment: reviewData.comment,
        ratings: {
          cleanliness: reviewData.ratings?.cleanliness || reviewData.rating,
          communication: reviewData.ratings?.communication || reviewData.rating,
          checkIn: reviewData.ratings?.checkIn || reviewData.rating,
          accuracy: reviewData.ratings?.accuracy || reviewData.rating,
          location: reviewData.ratings?.location || reviewData.rating,
          value: reviewData.ratings?.value || reviewData.rating
        },
        guest: { _type: 'reference', _ref: req.user.userId },
        property: reviewData.propertyId ? { _type: 'reference', _ref: reviewData.propertyId } : undefined,
        tour: reviewData.tourId ? { _type: 'reference', _ref: reviewData.tourId } : undefined,
        booking: { _type: 'reference', _ref: reviewData.bookingId },
        helpful: 0,
        reported: false,
        verified: true, // Auto-verify reviews from completed bookings
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })
      
      // Update property/tour average rating
      const targetId = reviewData.propertyId || reviewData.tourId
      const targetType = reviewData.propertyId ? 'property' : 'tour'
      
      const averageRating = await client.fetch(
        groq`avg(*[_type == "review" && ${targetType}._ref == $targetId].rating)`,
        { targetId }
      )
      
      await client.patch(targetId).set({
        averageRating: Math.round(averageRating * 10) / 10,
        totalReviews: await client.fetch(
          groq`count(*[_type == "review" && ${targetType}._ref == $targetId])`,
          { targetId }
        ),
        updatedAt: new Date().toISOString()
      }).commit()
      
      // Send notification to property owner/tour operator
      const ownerId = booking.property?.owner?._id || booking.tour?.operator?._id
      if (ownerId) {
        await NotificationService.createNotification(
          ownerId,
          'New Review Received',
          `You received a ${reviewData.rating}-star review for ${booking.property?.title || booking.tour?.title}.`,
          'review_received',
          { reviewId: review._id, rating: reviewData.rating }
        )
      }
      
      return NextResponse.json({
        success: true,
        data: review
      })
    } catch (error) {
      console.error('Create review error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to create review' },
        { status: 500 }
      )
    }
  }, ['reviews:create'])(request, NextResponse)
}

// PATCH /api/reviews - Update review helpfulness or report
export async function PATCH(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { reviewId, action } = await request.json()
      
      if (!reviewId || !action) {
        return NextResponse.json(
          { success: false, error: 'Review ID and action required' },
          { status: 400 }
        )
      }
      
      let updates: any = {
        updatedAt: new Date().toISOString()
      }
      
      switch (action) {
        case 'mark_helpful':
          // Increment helpful count
          const review = await client.fetch(
            groq`*[_type == "review" && _id == $reviewId][0].helpful`,
            { reviewId }
          )
          updates.helpful = (review || 0) + 1
          break
          
        case 'report':
          updates.reported = true
          updates.reportedBy = req.user.userId
          updates.reportedAt = new Date().toISOString()
          break
          
        default:
          return NextResponse.json(
            { success: false, error: 'Invalid action' },
            { status: 400 }
          )
      }
      
      await client.patch(reviewId).set(updates).commit()
      
      return NextResponse.json({
        success: true,
        message: 'Review updated successfully'
      })
    } catch (error) {
      console.error('Update review error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to update review' },
        { status: 500 }
      )
    }
  }, ['reviews:update'])(request, NextResponse)
}
