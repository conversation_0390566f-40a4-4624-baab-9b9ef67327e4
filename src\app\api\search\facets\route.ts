import { NextRequest, NextResponse } from 'next/server'
import { searchService } from '@/lib/search'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)

    // Parse any existing filters to get contextual facets
    const filters: any = {}

    const query = searchParams.get('q')
    if (query) filters.query = query

    const location = searchParams.get('location')
    if (location) filters.location = location

    const propertyType = searchParams.get('propertyType')
    if (propertyType) filters.propertyType = propertyType.split(',')

    const tourCategory = searchParams.get('tourCategory')
    if (tourCategory) filters.tourCategory = tourCategory.split(',')

    const facets = await searchService.getFacets(filters)

    return NextResponse.json({
      success: true,
      data: facets
    })
  } catch (error) {
    console.error('Facets error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to get search facets',
        success: false 
      },
      { status: 500 }
    )
  }
}
