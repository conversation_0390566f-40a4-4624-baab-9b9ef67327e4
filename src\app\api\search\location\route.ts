import { NextRequest, NextResponse } from 'next/server'
import { searchService } from '@/lib/search'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const lat = parseFloat(searchParams.get('lat') || '0')
    const lng = parseFloat(searchParams.get('lng') || '0')
    const radius = parseInt(searchParams.get('radius') || '10')
    const type = searchParams.get('type') as 'property' | 'tour' | undefined

    if (!lat || !lng) {
      return NextResponse.json(
        { 
          error: 'Latitude and longitude are required',
          success: false 
        },
        { status: 400 }
      )
    }

    const results = await searchService.searchByLocation(lat, lng, radius, type)

    return NextResponse.json({
      success: true,
      data: results
    })
  } catch (error) {
    console.error('Location search error:', error)
    return NextResponse.json(
      { 
        error: 'Location search failed',
        success: false 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { lat, lng, radius = 10, type } = body

    if (!lat || !lng) {
      return NextResponse.json(
        { 
          error: 'Latitude and longitude are required',
          success: false 
        },
        { status: 400 }
      )
    }

    const results = await searchService.searchByLocation(lat, lng, radius, type)

    return NextResponse.json({
      success: true,
      data: results
    })
  } catch (error) {
    console.error('Location search error:', error)
    return NextResponse.json(
      { 
        error: 'Location search failed',
        success: false 
      },
      { status: 500 }
    )
  }
}
