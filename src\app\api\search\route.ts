import { NextRequest, NextResponse } from 'next/server'
import { searchService, AdvancedSearchFilters } from '@/lib/search'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse search parameters
    const filters: any = {}

    const query = searchParams.get('q')
    if (query) filters.query = query

    const location = searchParams.get('location')
    if (location) filters.location = location

    const propertyType = searchParams.get('propertyType')
    if (propertyType) filters.propertyType = propertyType.split(',')

    const tourCategory = searchParams.get('tourCategory')
    if (tourCategory) filters.tourCategory = tourCategory.split(',')

    const minPrice = searchParams.get('minPrice')
    const maxPrice = searchParams.get('maxPrice')
    if (minPrice || maxPrice) {
      filters.priceRange = {
        min: parseInt(minPrice || '0'),
        max: parseInt(maxPrice || '0')
      }
    }

    const checkIn = searchParams.get('checkIn')
    const checkOut = searchParams.get('checkOut')
    if (checkIn || checkOut) {
      filters.dateRange = {
        checkIn: checkIn || '',
        checkOut: checkOut || ''
      }
    }

    const adults = searchParams.get('adults')
    const children = searchParams.get('children')
    const infants = searchParams.get('infants')
    if (adults || children || infants) {
      filters.guests = {
        adults: parseInt(adults || '1'),
        children: parseInt(children || '0'),
        infants: parseInt(infants || '0')
      }
    }

    const amenities = searchParams.get('amenities')
    if (amenities) filters.amenities = amenities.split(',')

    const difficulty = searchParams.get('difficulty')
    if (difficulty) filters.difficulty = difficulty.split(',')

    const minDuration = searchParams.get('minDuration')
    const maxDuration = searchParams.get('maxDuration')
    if (minDuration || maxDuration) {
      filters.duration = {
        min: parseInt(minDuration || '0'),
        max: parseInt(maxDuration || '0')
      }
    }

    const lat = searchParams.get('lat')
    const lng = searchParams.get('lng')
    if (lat && lng) {
      filters.coordinates = {
        lat: parseFloat(lat),
        lng: parseFloat(lng),
        radius: parseInt(searchParams.get('radius') || '10')
      }
    }

    const featured = searchParams.get('featured')
    if (featured === 'true') filters.featured = true
    else if (featured === 'false') filters.featured = false

    const verified = searchParams.get('verified')
    if (verified === 'true') filters.verified = true
    else if (verified === 'false') filters.verified = false

    const sortBy = searchParams.get('sortBy')
    if (sortBy) filters.sortBy = sortBy

    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')
    const type = searchParams.get('type') as 'property' | 'tour' | undefined

    let result

    if (type === 'property') {
      result = await searchService.searchProperties(filters, page, limit)
      return NextResponse.json({
        success: true,
        data: {
          properties: result.properties,
          tours: [],
          totalProperties: result.total,
          totalTours: 0,
          hasMore: result.hasMore,
          page,
          limit
        }
      })
    } else if (type === 'tour') {
      result = await searchService.searchTours(filters, page, limit)
      return NextResponse.json({
        success: true,
        data: {
          properties: [],
          tours: result.tours,
          totalProperties: 0,
          totalTours: result.total,
          hasMore: result.hasMore,
          page,
          limit
        }
      })
    } else {
      // Combined search
      const searchResult = await searchService.search(filters, page, limit)
      return NextResponse.json({
        success: true,
        data: {
          ...searchResult,
          page,
          limit
        }
      })
    }
  } catch (error) {
    console.error('Search error:', error)
    return NextResponse.json(
      { 
        error: 'Search failed',
        success: false 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { filters, page = 1, limit = 12, type } = body

    let result

    if (type === 'property') {
      result = await searchService.searchProperties(filters, page, limit)
      return NextResponse.json({
        success: true,
        data: {
          properties: result.properties,
          tours: [],
          totalProperties: result.total,
          totalTours: 0,
          hasMore: result.hasMore,
          page,
          limit
        }
      })
    } else if (type === 'tour') {
      result = await searchService.searchTours(filters, page, limit)
      return NextResponse.json({
        success: true,
        data: {
          properties: [],
          tours: result.tours,
          totalProperties: 0,
          totalTours: result.total,
          hasMore: result.hasMore,
          page,
          limit
        }
      })
    } else {
      // Combined search
      const searchResult = await searchService.search(filters, page, limit)
      return NextResponse.json({
        success: true,
        data: {
          ...searchResult,
          page,
          limit
        }
      })
    }
  } catch (error) {
    console.error('Search error:', error)
    return NextResponse.json(
      { 
        error: 'Search failed',
        success: false 
      },
      { status: 500 }
    )
  }
}
