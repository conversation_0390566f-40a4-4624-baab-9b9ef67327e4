import { NextRequest, NextResponse } from 'next/server'
import { searchService } from '@/lib/search'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''
    const limit = parseInt(searchParams.get('limit') || '5')

    if (!query || query.length < 2) {
      return NextResponse.json({
        success: true,
        data: {
          properties: [],
          tours: [],
          locations: []
        }
      })
    }

    const suggestions = await searchService.getSearchSuggestions(query, limit)

    return NextResponse.json({
      success: true,
      data: suggestions
    })
  } catch (error) {
    console.error('Search suggestions error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to get search suggestions',
        success: false 
      },
      { status: 500 }
    )
  }
}
