import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth'
import { uploadFile, uploadImage, uploadMultipleFiles, validateFile, getFileInfo, deleteFile } from '@/lib/uploads'

// POST /api/upload - Upload single or multiple files
export async function POST(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const formData = await request.formData()
      const files = formData.getAll('files') as File[]
      const type = formData.get('type') as string || 'file'
      const folder = formData.get('folder') as string || 'uploads'
      const maxSize = parseInt(formData.get('maxSize') as string || '10485760') // 10MB default
      
      if (!files || files.length === 0) {
        return NextResponse.json(
          { success: false, error: 'No files provided' },
          { status: 400 }
        )
      }
      
      // Validate all files first
      const validationErrors: string[] = []
      files.forEach((file, index) => {
        const error = validateFile(file, { maxSize })
        if (error) {
          validationErrors.push(`File ${index + 1} (${file.name}): ${error}`)
        }
      })
      
      if (validationErrors.length > 0) {
        return NextResponse.json(
          { success: false, error: 'Validation failed', details: validationErrors },
          { status: 400 }
        )
      }
      
      // Upload files
      let results
      if (files.length === 1) {
        const file = files[0]
        if (!file) {
          return NextResponse.json(
            { success: false, error: 'No file provided' },
            { status: 400 }
          )
        }
        if (type === 'image') {
          results = [await uploadImage(file, file.name, { folder, maxSize })]
        } else {
          results = [await uploadFile(file, file.name, { folder, maxSize })]
        }
      } else {
        results = await uploadMultipleFiles(files, { folder, maxSize })
      }
      
      return NextResponse.json({
        success: true,
        data: {
          files: results,
          count: results.length
        }
      })
    } catch (error) {
      console.error('Upload error:', error)
      return NextResponse.json(
        { success: false, error: error instanceof Error ? error.message : 'Upload failed' },
        { status: 500 }
      )
    }
  }, ['files:upload'])(request, NextResponse)
}

// GET /api/upload/[id] - Get file info
export async function GET(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { searchParams } = new URL(request.url)
      const id = searchParams.get('id')
      
      if (!id) {
        return NextResponse.json(
          { success: false, error: 'File ID required' },
          { status: 400 }
        )
      }
      
      const fileInfo = await getFileInfo(id)
      
      if (!fileInfo) {
        return NextResponse.json(
          { success: false, error: 'File not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json({
        success: true,
        data: fileInfo
      })
    } catch (error) {
      console.error('Get file error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to get file info' },
        { status: 500 }
      )
    }
  }, ['files:read'])(request, NextResponse)
}

// DELETE /api/upload/[id] - Delete file
export async function DELETE(request: NextRequest) {
  return withAuth(async (req: any) => {
    try {
      const { searchParams } = new URL(request.url)
      const id = searchParams.get('id')
      
      if (!id) {
        return NextResponse.json(
          { success: false, error: 'File ID required' },
          { status: 400 }
        )
      }
      
      await deleteFile(id)
      
      return NextResponse.json({
        success: true,
        message: 'File deleted successfully'
      })
    } catch (error) {
      console.error('Delete file error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to delete file' },
        { status: 500 }
      )
    }
  }, ['files:delete'])(request, NextResponse)
}
