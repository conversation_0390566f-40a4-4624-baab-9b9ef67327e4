import { getBlogPost } from '@/sanity/lib/queries'
import { BlogPost } from '@/types/blog'
import { Metadata } from 'next'
import Image from 'next/image'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { PortableText } from '@portabletext/react'
import { urlForImage } from '@/sanity/lib/image'

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  try {
    const post = await getBlogPost(params.slug)

    if (!post) {
      return {
        title: 'Post Not Found',
        description: 'The requested blog post could not be found.'
      }
    }

    return {
      title: `${post.title} | RiftStays Blog`,
      description: post.excerpt || 'Read this blog post on RiftStays',
      openGraph: {
        title: post.title,
        description: post.excerpt || 'Read this blog post on RiftStays',
        images: post.mainImage?.asset?.url ? [post.mainImage.asset.url] : [],
        type: 'article',
        publishedTime: post.publishedAt,
        authors: post.author?.name ? [post.author.name] : [],
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: 'Blog Post | RiftStays',
      description: 'Read this blog post on RiftStays'
    }
  }
}

const components = {
  types: {
    image: ({ value }: any) => {
      if (!value?.asset?._ref) {
        return null
      }
      return (
        <div className="relative my-8 aspect-video">
          <Image
            src={urlForImage(value).url()}
            alt={value.alt || 'Blog post image'}
            fill
            className="object-cover rounded-lg"
          />
        </div>
      )
    },
  },
  marks: {
    link: ({ children, value }: any) => {
      const rel = !value.href.startsWith('/') ? 'noopener noreferrer' : undefined
      const target = !value.href.startsWith('/') ? '_blank' : undefined
      return (
        <a
          href={value.href}
          target={target}
          rel={rel}
          className="text-blue-600 hover:text-blue-800 underline"
        >
          {children}
        </a>
      )
    },
  },
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  // For now, return not found to avoid build errors
  // In production, you would fetch from Sanity
  notFound()
}