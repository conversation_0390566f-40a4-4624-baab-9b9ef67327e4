import { getBlogPosts } from '@/sanity/lib/queries'
import { BlogPost } from '@/types/blog'
import Image from 'next/image'
import Link from 'next/link'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Blog | RiftStays',
  description: 'Latest travel tips, property insights, and destination guides from RiftStays.',
}

export default async function BlogPage() {
  // For now, return empty posts to avoid build errors
  // In production, you would fetch from Sanity
  const posts: BlogPost[] = []

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900">Blog</h1>
          <p className="mt-4 text-lg text-gray-600">
            Discover travel tips, property insights, and destination guides
          </p>
        </div>
      </div>

      {/* Blog Posts Grid */}
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
        {posts.length === 0 ? (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900">No blog posts yet</h3>
            <p className="mt-2 text-gray-600">Check back soon for travel tips and insights!</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {posts.map((post) => (
            <article
              key={post._id}
              className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
            >
              <Link href={`/blog/${post.slug.current}`}>
                <div className="aspect-w-16 aspect-h-9 relative h-48">
                  {post.mainImage?.asset?.url && (
                    <Image
                      src={post.mainImage.asset.url}
                      alt={post.title}
                      fill
                      className="object-cover"
                    />
                  )}
                </div>
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 hover:text-blue-600">
                    {post.title}
                  </h2>
                  <p className="mt-2 text-gray-600 line-clamp-3">
                    {post.excerpt}
                  </p>
                  <div className="mt-4 flex items-center gap-4">
                    {post.author?.image?.asset?.url && (
                      <div className="h-10 w-10 relative rounded-full overflow-hidden">
                        <Image
                          src={post.author.image.asset.url}
                          alt={post.author.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {post.author?.name || 'Anonymous'}
                      </p>
                      <p className="text-sm text-gray-600">
                        {post.publishedAt ? new Date(post.publishedAt).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        }) : 'No date'}
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
            </article>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}