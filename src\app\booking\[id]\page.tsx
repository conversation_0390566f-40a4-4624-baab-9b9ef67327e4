'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { BookingForm } from '@/components/booking/BookingForm'
import { Typography } from '@/components/ui/Typography'
import { OptimizedImage } from '@/components/ui/OptimizedImage'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { BookingType } from '@/types/booking'
import { sampleProperties, sampleTours, getPropertyBySlug, getTourBySlug } from '@/data/sampleData'
import { formatPrice } from '@/lib/formatters'

export default function BookingPage() {
  const params = useParams()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [item, setItem] = useState<any>(null)
  const [bookingType, setBookingType] = useState<BookingType>('house')

  useEffect(() => {
    const id = params.id as string

    // Check if it's a property or tour
    const property = getPropertyBySlug(id)
    const tour = getTourBySlug(id)

    if (property) {
      setItem({
        ...property,
        id: property._id,
        type: property.propertyType === 'villa' ? 'house' : 'hotel' as BookingType,
        images: [`/images/${property.slug.current}.jpg`]
      })
      setBookingType(property.propertyType === 'villa' ? 'house' : 'hotel')
    } else if (tour) {
      setItem({
        ...tour,
        id: tour._id,
        type: 'tour' as BookingType,
        images: tour.images.map(img => img.asset.url)
      })
      setBookingType('tour')
    } else {
      // Item not found, redirect to 404 or properties page
      router.push('/properties')
      return
    }

    setLoading(false)
  }, [params.id, router])

  const handleBookingCreated = (bookingId: string) => {
    router.push(`/booking/${params.id}/payment?bookingId=${bookingId}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!item) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Typography variant="h2">Item not found</Typography>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Typography variant="h1" className="text-center mb-4">
            Complete Your Booking
          </Typography>
          <Typography variant="lead" className="text-center text-gray-600 max-w-2xl mx-auto">
            You're just a few steps away from an amazing experience in Kenya
          </Typography>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
          {/* Property/Tour Information */}
          <div className="space-y-6">
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100">
              {/* Image */}
              <div className="relative h-64 md:h-80">
                <OptimizedImage
                  src={item.images[0]}
                  alt={item.title}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                <div className="absolute bottom-4 left-4 right-4">
                  <Typography variant="h2" className="text-white mb-2">
                    {item.title}
                  </Typography>
                  <Typography variant="body" className="text-white/90">
                    📍 {item.location}
                  </Typography>
                </div>
              </div>

              {/* Details */}
              <div className="p-6">
                <Typography variant="body" className="text-gray-700 mb-6 leading-relaxed">
                  {item.description}
                </Typography>

                {/* Price */}
                <div className="bg-gradient-to-r from-primary-50 to-secondary-50 p-4 rounded-xl mb-6">
                  <Typography variant="h4" className="text-primary-600">
                    KES {formatPrice(item.price)}
                    <Typography variant="body" as="span" className="text-gray-600 ml-2">
                      {bookingType === 'tour' ? 'per person' : 'per night'}
                    </Typography>
                  </Typography>
                </div>

                {/* Amenities/Includes */}
                <div>
                  <Typography variant="h5" className="mb-4">
                    {bookingType === 'tour' ? 'Includes' : 'Amenities'}
                  </Typography>
                  <div className="grid grid-cols-2 gap-2">
                    {(item.amenities || item.includes || []).map((feature: string, index: number) => (
                      <div key={index} className="flex items-center">
                        <span className="text-green-500 mr-2">✓</span>
                        <Typography variant="small" className="text-gray-700">
                          {feature}
                        </Typography>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Additional Info */}
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    {bookingType !== 'tour' && (
                      <>
                        <div>
                          <Typography variant="small" weight="medium" className="text-gray-500">
                            Max Guests
                          </Typography>
                          <Typography variant="body" className="text-gray-900">
                            {item.maxGuests} guests
                          </Typography>
                        </div>
                        <div>
                          <Typography variant="small" weight="medium" className="text-gray-500">
                            Bedrooms
                          </Typography>
                          <Typography variant="body" className="text-gray-900">
                            {item.bedrooms} bedrooms
                          </Typography>
                        </div>
                      </>
                    )}
                    {bookingType === 'tour' && (
                      <>
                        <div>
                          <Typography variant="small" weight="medium" className="text-gray-500">
                            Duration
                          </Typography>
                          <Typography variant="body" className="text-gray-900">
                            {item.duration} hours
                          </Typography>
                        </div>
                        <div>
                          <Typography variant="small" weight="medium" className="text-gray-500">
                            Difficulty
                          </Typography>
                          <Typography variant="body" className="text-gray-900">
                            {item.difficulty}
                          </Typography>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Booking Form */}
          <div>
            <BookingForm
              type={bookingType}
              propertyId={bookingType !== 'tour' ? item.id : undefined}
              tourId={bookingType === 'tour' ? item.id : undefined}
              onBookingCreated={handleBookingCreated}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
