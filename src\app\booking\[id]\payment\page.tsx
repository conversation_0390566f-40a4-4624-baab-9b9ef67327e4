import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { PaymentForm } from '@/components/booking/PaymentForm'

interface PaymentPageProps {
  params: {
    id: string
  }
}

// Mock booking data (in production, fetch from Sanity)
const mockBooking = {
  _id: '1',
  bookingNumber: 'RS20241201ABCD',
  type: 'tour',
  status: 'pending',
  pricing: {
    totalPrice: 24000,
    currency: 'KES'
  },
  tourBooking: {
    tourName: 'Maasai Mara Wildlife Safari',
    date: '2024-12-15',
    time: '06:00'
  },
  contact: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>'
  }
}

export const metadata: Metadata = {
  title: 'Complete Payment | RiftStays',
  description: 'Complete your booking payment securely with M-Pesa',
}

export default function PaymentPage({ params }: PaymentPageProps) {
  // In production, fetch booking data based on ID
  const booking = mockBooking

  if (!booking) {
    notFound()
  }

  if (booking.status !== 'pending') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Payment Not Required
          </h1>
          <p className="text-gray-600 mb-6">
            This booking has already been processed or is not in a payable state.
          </p>
          <a
            href="/bookings"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            View My Bookings
          </a>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Complete Your Payment</h1>
          <p className="mt-2 text-gray-600">
            Secure payment for booking {booking.bookingNumber}
          </p>
        </div>

        {/* Booking Summary */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Booking Summary</h2>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Booking Number:</span>
              <span className="font-medium">{booking.bookingNumber}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Type:</span>
              <span className="font-medium capitalize">{booking.type}</span>
            </div>
            
            {booking.tourBooking && (
              <>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tour:</span>
                  <span className="font-medium">{booking.tourBooking.tourName}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Date:</span>
                  <span className="font-medium">
                    {new Date(booking.tourBooking.date).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Time:</span>
                  <span className="font-medium">{booking.tourBooking.time}</span>
                </div>
              </>
            )}
            
            <div className="flex justify-between">
              <span className="text-gray-600">Customer:</span>
              <span className="font-medium">
                {booking.contact.firstName} {booking.contact.lastName}
              </span>
            </div>
            
            <div className="border-t pt-3 mt-3">
              <div className="flex justify-between text-lg font-semibold">
                <span>Total Amount:</span>
                <span className="text-blue-600">
                  {new Intl.NumberFormat('en-KE', {
                    style: 'currency',
                    currency: 'KES',
                    minimumFractionDigits: 0,
                  }).format(booking.pricing.totalPrice)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Form */}
        <PaymentForm
          bookingId={booking._id}
          amount={booking.pricing.totalPrice}
          bookingNumber={booking.bookingNumber}
          onPaymentSuccess={() => {
            // Redirect to confirmation page
            window.location.href = `/booking/${booking._id}/confirmation`
          }}
          onPaymentError={(error) => {
            console.error('Payment error:', error)
            // Handle payment error
          }}
        />

        {/* Security Notice */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Secure Payment
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  Your payment is processed securely through M-Pesa. We never store your payment information.
                  You will receive an SMS confirmation once payment is complete.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
