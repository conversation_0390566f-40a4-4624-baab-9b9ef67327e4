@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #2AAA8A;
  --secondary: #1D7A6B;
  --accent: #3BC4A0;
}

@theme {
  /* Font families */
  --font-sans: 'Poppins', sans-serif;
  --font-serif: 'Playfair Display', serif;

  /* Golden Ratio Typography Scale (φ ≈ 1.618) */
  /* Base: 16px (1rem) - optimal for body text */
  --font-size-xs: 0.75rem;
  --font-size-xs--line-height: 1.2;
  --font-size-xs--letter-spacing: 0.025em;

  --font-size-sm: 0.875rem;
  --font-size-sm--line-height: 1.3;
  --font-size-sm--letter-spacing: 0.025em;

  --font-size-base: 1rem;
  --font-size-base--line-height: 1.5;
  --font-size-base--letter-spacing: 0;

  --font-size-lg: 1.125rem;
  --font-size-lg--line-height: 1.4;
  --font-size-lg--letter-spacing: 0;

  --font-size-xl: 1.25rem;
  --font-size-xl--line-height: 1.4;
  --font-size-xl--letter-spacing: 0;

  --font-size-2xl: 1.618rem; /* φ¹ (small headings) */
  --font-size-2xl--line-height: 1.3;
  --font-size-2xl--letter-spacing: -0.025em;

  --font-size-3xl: 2.618rem; /* φ² (medium headings) */
  --font-size-3xl--line-height: 1.2;
  --font-size-3xl--letter-spacing: -0.025em;

  --font-size-4xl: 4.236rem; /* φ³ (large headings) */
  --font-size-4xl--line-height: 1.1;
  --font-size-4xl--letter-spacing: -0.05em;

  --font-size-5xl: 6.854rem; /* φ⁴ (hero headings) */
  --font-size-5xl--line-height: 1;
  --font-size-5xl--letter-spacing: -0.05em;

  --font-size-6xl: 11.09rem; /* φ⁵ (display headings) */
  --font-size-6xl--line-height: 0.9;
  --font-size-6xl--letter-spacing: -0.075em;

  --font-size-7xl: 17.944rem; /* φ⁶ (massive display) */
  --font-size-7xl--line-height: 0.9;
  --font-size-7xl--letter-spacing: -0.075em;

  /* Color palette */
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  /* Primary colors (Jungle Green theme) */
  --color-primary-50: #f0fdf9;
  --color-primary-100: #ccfbef;
  --color-primary-200: #99f6e0;
  --color-primary-300: #5eead4;
  --color-primary-400: #2dd4bf;
  --color-primary-500: #2AAA8A;
  --color-primary-600: #0d9488;
  --color-primary-700: #0f766e;
  --color-primary-800: #115e59;
  --color-primary-900: #134e4a;

  /* Secondary colors */
  --color-secondary-50: #f0fdfa;
  --color-secondary-100: #ccfbf1;
  --color-secondary-200: #99f6e4;
  --color-secondary-300: #5eead4;
  --color-secondary-400: #2dd4bf;
  --color-secondary-500: #1D7A6B;
  --color-secondary-600: #0d9488;
  --color-secondary-700: #0f766e;
  --color-secondary-800: #115e59;
  --color-secondary-900: #134e4a;

  /* Accent colors */
  --color-accent-50: #f0fdf9;
  --color-accent-100: #dcfce7;
  --color-accent-200: #bbf7d0;
  --color-accent-300: #86efac;
  --color-accent-400: #4ade80;
  --color-accent-500: #3BC4A0;
  --color-accent-600: #16a34a;
  --color-accent-700: #15803d;
  --color-accent-800: #166534;
  --color-accent-900: #14532d;

  /* Success colors */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;

  /* Warning colors */
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;

  /* Danger colors */
  --color-danger-50: #fef2f2;
  --color-danger-100: #fee2e2;
  --color-danger-200: #fecaca;
  --color-danger-300: #fca5a5;
  --color-danger-400: #f87171;
  --color-danger-500: #ef4444;
  --color-danger-600: #dc2626;
  --color-danger-700: #b91c1c;
  --color-danger-800: #991b1b;
  --color-danger-900: #7f1d1d;

  /* Background images */
  --background-image-gradient-radial: radial-gradient(var(--tw-gradient-stops));
  --background-image-gradient-conic: conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops));
  --background-image-hero-pattern: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%232AAA8A" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Poppins', sans-serif;
  font-size: 1rem; /* 16px base - golden ratio foundation */
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Golden Ratio Typography Hierarchy */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.025em;
  margin-bottom: 0.5em;
  color: var(--foreground);
}

/* Specific heading sizes using golden ratio */
h1 {
  font-size: 4.236rem; /* φ³ = 68px */
  line-height: 1.1;
  letter-spacing: -0.05em;
}

h2 {
  font-size: 2.618rem; /* φ² = 42px */
  line-height: 1.2;
  letter-spacing: -0.025em;
}

h3 {
  font-size: 1.618rem; /* φ¹ = 26px */
  line-height: 1.3;
  letter-spacing: -0.025em;
}

h4 {
  font-size: 1.25rem; /* 20px */
  line-height: 1.4;
  letter-spacing: 0;
}

h5 {
  font-size: 1.125rem; /* 18px */
  line-height: 1.4;
  letter-spacing: 0;
}

h6 {
  font-size: 1rem; /* 16px */
  line-height: 1.5;
  letter-spacing: 0;
}

/* Body text optimization */
p {
  font-size: 1rem; /* 16px base */
  line-height: 1.5;
  margin-bottom: 1em;
  color: var(--foreground);
}

/* Lead text for better hierarchy */
.lead {
  font-size: 1.25rem; /* 20px */
  line-height: 1.4;
  font-weight: 400;
  color: var(--foreground);
}

/* Small text with better readability */
small, .text-small {
  font-size: 0.875rem; /* 14px */
  line-height: 1.3;
  color: var(--foreground);
  opacity: 0.8;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Golden Ratio Typography Utilities */
.text-golden-xs {
  font-size: 0.75rem; /* 12px */
  line-height: 1.2;
  letter-spacing: 0.025em;
}

.text-golden-sm {
  font-size: 0.875rem; /* 14px */
  line-height: 1.3;
  letter-spacing: 0.025em;
}

.text-golden-base {
  font-size: 1rem; /* 16px - golden base */
  line-height: 1.5;
  letter-spacing: 0;
}

.text-golden-lg {
  font-size: 1.125rem; /* 18px */
  line-height: 1.4;
  letter-spacing: 0;
}

.text-golden-xl {
  font-size: 1.25rem; /* 20px */
  line-height: 1.4;
  letter-spacing: 0;
}

.text-golden-2xl {
  font-size: 1.618rem; /* 26px - φ¹ */
  line-height: 1.3;
  letter-spacing: -0.025em;
}

.text-golden-3xl {
  font-size: 2.618rem; /* 42px - φ² */
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.text-golden-4xl {
  font-size: 4.236rem; /* 68px - φ³ */
  line-height: 1.1;
  letter-spacing: -0.05em;
}

.text-golden-5xl {
  font-size: 6.854rem; /* 110px - φ⁴ */
  line-height: 1;
  letter-spacing: -0.05em;
}

.text-golden-6xl {
  font-size: 11.09rem; /* 178px - φ⁵ */
  line-height: 0.9;
  letter-spacing: -0.075em;
}

/* Responsive typography for better mobile experience */
@media (max-width: 640px) {
  h1 {
    font-size: 2.618rem; /* φ² = 42px on mobile */
    line-height: 1.2;
  }

  h2 {
    font-size: 1.618rem; /* φ¹ = 26px on mobile */
    line-height: 1.3;
  }

  .text-golden-4xl {
    font-size: 2.618rem; /* φ² = 42px on mobile */
    line-height: 1.2;
  }

  .text-golden-5xl {
    font-size: 4.236rem; /* φ³ = 68px on mobile */
    line-height: 1.1;
  }

  .text-golden-6xl {
    font-size: 6.854rem; /* φ⁴ = 110px on mobile */
    line-height: 1;
  }
}

/* Text contrast and visibility improvements */
.text-high-contrast {
  color: var(--foreground);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  font-weight: 500;
}

.text-readable {
  color: var(--foreground);
  font-weight: 400;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

/* Enhanced text visibility classes */
.text-pop {
  color: var(--foreground);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.1);
  font-weight: 600;
  letter-spacing: -0.025em;
}

.text-glow {
  text-shadow: 0 0 10px rgba(42, 170, 138, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-crisp {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1;
}

/* Heading enhancements for better visibility */
h1, h2, h3, h4, h5, h6 {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-feature-settings: "kern" 1, "liga" 1;
}

/* Navigation text enhancements */
.nav-text {
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  letter-spacing: -0.01em;
}

/* Button text enhancements */
.btn-text {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.025em;
}

/* Enhanced mobile text visibility */
@media (max-width: 768px) {
  .text-pop {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    font-weight: 700;
  }

  .nav-text {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .btn-text {
    font-weight: 700;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  }
}

/* Link enhancements */
a {
  transition: all 0.2s ease-in-out;
}

a:hover {
  transform: translateY(-1px);
}

/* Focus states for accessibility */
a:focus-visible,
button:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Text selection styling */
::selection {
  background-color: rgba(42, 170, 138, 0.3);
  color: var(--foreground);
}

::-moz-selection {
  background-color: rgba(42, 170, 138, 0.3);
  color: var(--foreground);
}

/* Focus and accessibility improvements */
a:focus-visible,
button:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Ensure minimum touch targets for mobile */
@media (max-width: 768px) {
  button, a {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Logo optimization for crisp rendering */
.logo-crisp {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
}

/* SVG logo specific optimizations */
svg.logo {
  shape-rendering: geometricPrecision;
  text-rendering: optimizeLegibility;
}

/* Ensure logos are always crisp */
img[src$=".svg"] {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}
