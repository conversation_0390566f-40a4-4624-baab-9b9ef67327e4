import { Metadata } from 'next'
import { Typography } from '@/components/ui/Typography'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Help Center | RiftStays',
  description: 'Get help with booking properties, listing your property, and using RiftStays platform.',
}

export default function HelpPage() {
  const faqs = [
    {
      question: "How do I book a property?",
      answer: "Browse properties, select your dates, and click 'Book Now'. Follow the booking process to complete your reservation."
    },
    {
      question: "How do I list my property?",
      answer: "Click 'List Property' in the navigation, fill out the property details form, upload photos, and submit for review."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept M-Pesa, credit cards, and bank transfers. All payments are processed securely."
    },
    {
      question: "Can I cancel my booking?",
      answer: "Cancellation policies vary by property. Check the specific cancellation policy before booking."
    },
    {
      question: "How do I contact the property owner?",
      answer: "Once you've made a booking, you can contact the property owner through our messaging system."
    },
    {
      question: "Is my personal information secure?",
      answer: "Yes, we use industry-standard security measures to protect your personal information."
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <Typography variant="h1" className="mb-4">
            Help Center
          </Typography>
          <Typography variant="lead" className="text-gray-600 max-w-2xl mx-auto">
            Find answers to common questions or get in touch with our support team.
          </Typography>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Quick Help */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-xl p-6">
              <Typography variant="h3" className="mb-6">
                Quick Help
              </Typography>
              
              <div className="space-y-4">
                <Link href="/contact" className="block">
                  <div className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors">
                    <Typography variant="h4" className="text-primary-600 mb-2">
                      Contact Support
                    </Typography>
                    <Typography variant="body" className="text-gray-600">
                      Get personalized help from our support team
                    </Typography>
                  </div>
                </Link>

                <Link href="/list-your-property" className="block">
                  <div className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors">
                    <Typography variant="h4" className="text-primary-600 mb-2">
                      List Property
                    </Typography>
                    <Typography variant="body" className="text-gray-600">
                      Start listing your property in minutes
                    </Typography>
                  </div>
                </Link>

                <Link href="/search" className="block">
                  <div className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors">
                    <Typography variant="h4" className="text-primary-600 mb-2">
                      Search Properties
                    </Typography>
                    <Typography variant="body" className="text-gray-600">
                      Find your perfect accommodation
                    </Typography>
                  </div>
                </Link>
              </div>
            </div>
          </div>

          {/* FAQs */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <Typography variant="h3" className="mb-8">
                Frequently Asked Questions
              </Typography>
              
              <div className="space-y-6">
                {faqs.map((faq, index) => (
                  <div key={index} className="border-b border-gray-200 pb-6 last:border-b-0">
                    <Typography variant="h4" className="text-gray-900 mb-3">
                      {faq.question}
                    </Typography>
                    <Typography variant="body" className="text-gray-600">
                      {faq.answer}
                    </Typography>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="mt-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 text-center text-white">
          <Typography variant="h2" className="text-white mb-4">
            Still Need Help?
          </Typography>
          <Typography variant="lead" className="text-white/90 mb-6 max-w-2xl mx-auto">
            Our support team is here to help you with any questions or issues you may have.
          </Typography>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button 
                variant="secondary"
                className="bg-white text-primary-600 hover:bg-gray-50 font-semibold px-8 py-3 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                Contact Support
              </Button>
            </Link>
            <a href="mailto:<EMAIL>">
              <Button 
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-primary-600 font-semibold px-8 py-3 rounded-full transition-all duration-300"
              >
                Email Us
              </Button>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
