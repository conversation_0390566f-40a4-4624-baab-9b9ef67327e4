import { Suspense } from 'react'
import Link from 'next/link'
import { Property } from '@/types/property'
import { PropertyCardSkeleton } from '@/components/ui/LoadingSpinner'
import { Typography } from '@/components/ui/Typography'
import { Button } from '@/components/ui/Button'
import { OptimizedImage } from '@/components/ui/OptimizedImage'
import { sampleProperties } from '@/data/sampleData'

export const metadata = {
  title: 'Houses to Let in Kenya | RiftStays',
  description: 'Find the perfect house to rent in Kenya. Browse our collection of family homes, modern houses, and rental properties in prime locations across Nairobi and beyond.',
  keywords: 'houses to let Kenya, rental houses Nairobi, family homes for rent, house rentals Kenya',
}

export default async function HousesToLetPage() {
  // Filter properties for rental houses only
  const rentalHouses: Property[] = sampleProperties.filter(
    property => property.purpose === 'rental' && property.propertyType === 'house'
  )

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <Typography variant="h1" className="text-white mb-4">
            Houses to Let in Kenya
          </Typography>
          <Typography variant="lead" className="text-white/90 max-w-2xl">
            Discover your perfect family home. From modern houses in Westlands to spacious properties in Karen, find quality rental houses in Kenya's best neighborhoods.
          </Typography>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-2xl shadow-xl p-6 -mt-8 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-golden-sm font-medium text-gray-700 mb-2">
                Location
              </label>
              <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-golden-base">
                <option value="">All Locations</option>
                <option value="westlands">Westlands</option>
                <option value="karen">Karen</option>
                <option value="kileleshwa">Kileleshwa</option>
                <option value="kilimani">Kilimani</option>
                <option value="lavington">Lavington</option>
              </select>
            </div>
            
            <div>
              <label className="block text-golden-sm font-medium text-gray-700 mb-2">
                Bedrooms
              </label>
              <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-golden-base">
                <option value="">Any</option>
                <option value="1">1 Bedroom</option>
                <option value="2">2 Bedrooms</option>
                <option value="3">3 Bedrooms</option>
                <option value="4">4 Bedrooms</option>
                <option value="5">5+ Bedrooms</option>
              </select>
            </div>

            <div>
              <label className="block text-golden-sm font-medium text-gray-700 mb-2">
                Monthly Rent
              </label>
              <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-golden-base">
                <option value="">Any Price</option>
                <option value="0-50000">Under KES 50,000</option>
                <option value="50000-100000">KES 50,000 - 100,000</option>
                <option value="100000-150000">KES 100,000 - 150,000</option>
                <option value="150000-200000">KES 150,000 - 200,000</option>
                <option value="200000+">Above KES 200,000</option>
              </select>
            </div>

            <div className="flex items-end">
              <Button className="w-full bg-gradient-to-r from-primary-500 to-accent-500 hover:from-primary-600 hover:to-accent-600 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                Search Houses
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Houses Grid */}
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex justify-between items-center mb-8">
          <Typography variant="h2" className="text-gray-900">
            Available Houses ({rentalHouses.length})
          </Typography>
          <div className="flex items-center space-x-4">
            <span className="text-golden-sm text-gray-600">Sort by:</span>
            <select className="border border-gray-300 rounded-lg px-3 py-2 text-golden-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="bedrooms">Bedrooms</option>
              <option value="location">Location</option>
              <option value="newest">Newest First</option>
            </select>
          </div>
        </div>

        {rentalHouses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {rentalHouses.map((house) => (
              <div key={house._id} className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div className="relative h-64">
                  <OptimizedImage
                    src={`/images/${house.slug.current}.jpg`}
                    alt={house.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-4 right-4">
                    <span className="bg-primary-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                      For Rent
                    </span>
                  </div>
                  {house.featured && (
                    <div className="absolute top-4 left-4">
                      <span className="bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                        Featured
                      </span>
                    </div>
                  )}
                </div>
                
                <div className="p-6">
                  <Typography variant="h3" className="text-gray-900 mb-2 line-clamp-2">
                    {house.title}
                  </Typography>
                  
                  <Typography variant="body" className="text-gray-600 mb-4 line-clamp-3">
                    {house.description}
                  </Typography>
                  
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4 text-golden-sm text-gray-500">
                      <span>{house.bedrooms} bed</span>
                      <span>{house.bathrooms} bath</span>
                      <span>Max {house.maxGuests}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mb-4">
                    <Typography variant="body" className="text-gray-600">
                      📍 {house.location}
                    </Typography>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Typography variant="h4" className="text-primary-600 font-bold">
                        {formatCurrency(house.price)}
                      </Typography>
                      <Typography variant="small" className="text-gray-500">
                        per month
                      </Typography>
                    </div>
                    
                    <Link href={`/properties/${house.slug.current}`}>
                      <Button 
                        variant="primary"
                        className="bg-gradient-to-r from-primary-500 to-accent-500 hover:from-primary-600 hover:to-accent-600 text-white font-semibold px-6 py-2 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                      >
                        View Details
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
              <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
            </div>
            <Typography variant="h3" className="text-gray-900 mb-2">
              No houses available
            </Typography>
            <Typography variant="body" className="text-gray-600 mb-6">
              We're currently updating our rental house listings. Please check back soon.
            </Typography>
            <Link href="/list-your-property">
              <Button variant="primary">
                List Your House
              </Button>
            </Link>
          </div>
        )}

        {/* Call to Action */}
        <div className="mt-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 text-center text-white">
          <Typography variant="h2" className="text-white mb-4">
            Have a House to Let?
          </Typography>
          <Typography variant="lead" className="text-white/90 mb-6 max-w-2xl mx-auto">
            Join thousands of property owners who trust RiftStays to find quality tenants for their rental properties.
          </Typography>
          <Link href="/list-your-property">
            <Button 
              variant="secondary"
              className="bg-white text-primary-600 hover:bg-gray-50 font-semibold px-8 py-3 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              List Your Property
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
