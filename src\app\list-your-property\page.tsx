import { Metadata } from 'next'
import { PropertyForm } from '@/components/properties/PropertyForm'

export const metadata: Metadata = {
  title: 'List Your Property | RiftStays - Kenya\'s Leading Property Platform',
  description: 'List your rental property, home for sale, or Airbnb on Kenya\'s leading real estate platform. Reach thousands of potential tenants and buyers.',
}

export default function ListPropertyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-golden-4xl sm:text-golden-5xl font-bold tracking-tight font-serif mb-6">
              List Your Property with RiftStays
            </h1>
            <p className="text-golden-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              Reach thousands of potential tenants and buyers across Kenya.
              Our platform makes it easy to showcase your property and connect with serious inquiries.
            </p>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <section className="py-16 bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-golden-3xl font-bold text-gray-900 font-serif mb-4">
              Why List with RiftStays?
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
              <h3 className="text-golden-xl font-semibold text-gray-900 mb-2">Maximum Exposure</h3>
              <p className="text-gray-600">Your property will be seen by thousands of active property seekers across Kenya.</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="text-golden-xl font-semibold text-gray-900 mb-2">No Upfront Fees</h3>
              <p className="text-gray-600">List your property for free. We only charge a small commission when you get a tenant or buyer.</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-golden-xl font-semibold text-gray-900 mb-2">Full Support</h3>
              <p className="text-gray-600">Our team helps with photography, listing optimization, and managing inquiries.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Property Form Section */}
      <section className="py-16">
        <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <h2 className="text-golden-2xl font-bold text-gray-900 font-serif mb-8 text-center">
              Get Started - List Your Property
            </h2>
            <PropertyForm />
          </div>
        </div>
      </section>
    </div>
  )
}