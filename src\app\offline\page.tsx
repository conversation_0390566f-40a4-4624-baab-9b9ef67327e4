import { OfflineActions } from '@/components/offline/OfflineActions'

export const metadata = {
  title: 'Offline - RiftStays',
  description: 'You are currently offline. Please check your internet connection.',
}

export default function OfflinePage() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <h1 className="text-6xl font-bold text-gray-400 mb-4">📡</h1>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            You're Offline
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            It looks like you've lost your internet connection. Don't worry, you can still browse some cached content.
          </p>
        </div>

        <OfflineActions />

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-900 mb-2">
            While you're offline, you can:
          </h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Browse previously visited pages</li>
            <li>• View cached property listings</li>
            <li>• Read saved blog posts</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
