import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Suspense } from 'react'
import { PropertyCardSkeleton, BlogCardSkeleton } from '@/components/ui/LoadingSpinner'
import { PropertySearchHero } from '@/components/search/PropertySearchHero'
import { Typography } from '@/components/ui/Typography'

export const metadata = {
  title: 'RiftStays - Find Your Perfect Home in Kenya | Rent, Buy, Airbnb',
  description: 'Kenya\'s leading property platform. Find rental properties, homes for sale, and Airbnb accommodations across Nairobi, Mombasa, and beyond. List your property today.',
  keywords: 'Kenya properties, houses for rent, apartments for sale, Airbnb Kenya, real estate Kenya, Nairobi rentals',
  openGraph: {
    title: 'RiftStays - Kenya\'s Leading Property Platform',
    description: 'Find your perfect home in Kenya. Rent, buy, or list properties across Kenya\'s best locations.',
    images: ['/images/kenya-safari-landscape.jpg'],
    type: 'website',
  },
}

// Hero Section - Mobile-first, scalable design
function HeroSection() {
  return (
    <div className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src="/images/kenya-safari-landscape.jpg"
          alt="Kenya Safari Landscape with Acacia Trees"
          fill
          className="object-cover"
          priority
          quality={85}
          sizes="100vw"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-primary-900/90 via-secondary-900/70 to-accent-900/80"></div>
        <div className="absolute inset-0 bg-hero-pattern opacity-30"></div>
      </div>

      {/* Hero Content */}
      <div className="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">
        <div className="space-y-8">
          {/* Main Headline */}
          <div className="space-y-4">
            <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold tracking-tight font-serif bg-gradient-to-r from-white via-yellow-200 to-orange-200 bg-clip-text text-transparent leading-tight text-crisp text-pop">
              Find Your Perfect Home in Kenya
            </h1>
            <p className="text-lg sm:text-xl lg:text-2xl leading-relaxed max-w-4xl mx-auto text-gray-100 font-medium text-crisp text-readable">
              Kenya's leading property platform. Discover rental properties, homes for sale, and vacation stays across Nairobi, Mombasa, and beyond.
            </p>
          </div>

          {/* Value Proposition */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-3xl mx-auto text-sm sm:text-base">
            <div className="flex items-center justify-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
              <svg className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              <span className="text-crisp font-medium">Verified Properties</span>
            </div>
            <div className="flex items-center justify-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
              <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <span className="text-crisp font-medium">Trusted Platform</span>
            </div>
            <div className="flex items-center justify-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
              <svg className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
              <span className="text-crisp font-medium">Easy Listings</span>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6">
            <Link
              href="/properties"
              className="w-full sm:w-auto rounded-full bg-gradient-to-r from-primary-500 to-warning-500 px-8 py-4 text-lg font-semibold text-white shadow-lg hover:from-primary-600 hover:to-warning-600 transform hover:scale-105 transition-all duration-300 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 min-h-[48px] text-center btn-text text-crisp hover:text-glow"
            >
              🏠 Search Properties
            </Link>
            <Link
              href="/list-your-property"
              className="w-full sm:w-auto text-lg font-semibold leading-6 text-white hover:text-yellow-200 transition-all duration-300 group min-h-[48px] flex items-center justify-center border-2 border-white/30 rounded-full px-8 py-4 hover:border-white/50 hover:bg-white/10 backdrop-blur-sm btn-text text-crisp hover:text-glow transform hover:scale-105"
            >
              📝 List Your Property
              <span aria-hidden="true" className="group-hover:translate-x-1 transition-transform duration-300 inline-block ml-2">→</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg className="w-6 h-6 text-white/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </div>
    </div>
  )
}

export default function Home() {
  return (
    <div className="relative">
      {/* Hero Section */}
      <HeroSection />

      {/* Property Search Section */}
      <section className="relative -mt-32 z-10 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-5xl">
          <PropertySearchHero />
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 sm:py-24 bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div className="space-y-2">
              <Typography variant="h2" className="text-primary-600 font-bold">
                10K+
              </Typography>
              <Typography variant="body" className="text-gray-600">
                Properties Listed
              </Typography>
            </div>
            <div className="space-y-2">
              <Typography variant="h2" className="text-secondary-600 font-bold">
                5K+
              </Typography>
              <Typography variant="body" className="text-gray-600">
                Happy Tenants
              </Typography>
            </div>
            <div className="space-y-2">
              <Typography variant="h2" className="text-accent-600 font-bold">
                500+
              </Typography>
              <Typography variant="body" className="text-gray-600">
                Property Owners
              </Typography>
            </div>
            <div className="space-y-2">
              <Typography variant="h2" className="text-warning-600 font-bold">
                50+
              </Typography>
              <Typography variant="body" className="text-gray-600">
                Locations Covered
              </Typography>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 sm:py-24 bg-gradient-to-br from-gray-50 via-white to-primary-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Typography variant="h2" className="text-gray-900 font-serif mb-4">
              Property Solutions for Everyone
            </Typography>
            <Typography variant="lead" className="text-gray-600 max-w-3xl mx-auto">
              Whether you're searching for a home or listing your property, we've got you covered
            </Typography>
          </div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {/* Rental Properties */}
            <Link
              href="/properties?type=rental"
              className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-primary-500 to-warning-500 p-6 sm:p-8 hover:from-primary-600 hover:to-warning-600 transition-all duration-300 text-white transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-colors duration-300"></div>
              <div className="relative text-center sm:text-left">
                <div className="flex flex-col sm:flex-row items-center sm:items-start mb-4">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mb-3 sm:mb-0 sm:mr-4">
                    <span className="text-2xl">🏠</span>
                  </div>
                  <div>
                    <Typography variant="h4" className="text-white font-serif mb-2">
                      Rental Properties
                    </Typography>
                    <Typography variant="body" className="text-white/90 text-sm">
                      Find apartments, houses, and condos for long-term rental
                    </Typography>
                  </div>
                </div>
                <div className="text-xs text-white/70 font-medium">
                  From KES 20,000/month
                </div>
              </div>
            </Link>

            {/* Homes for Sale */}
            <Link
              href="/properties?type=sale"
              className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-secondary-500 to-accent-500 p-6 sm:p-8 hover:from-secondary-600 hover:to-accent-600 transition-all duration-300 text-white transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-colors duration-300"></div>
              <div className="relative text-center sm:text-left">
                <div className="flex flex-col sm:flex-row items-center sm:items-start mb-4">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mb-3 sm:mb-0 sm:mr-4">
                    <span className="text-2xl">🏡</span>
                  </div>
                  <div>
                    <Typography variant="h4" className="text-white font-serif mb-2">
                      Homes for Sale
                    </Typography>
                    <Typography variant="body" className="text-white/90 text-sm">
                      Buy your dream home or investment property
                    </Typography>
                  </div>
                </div>
                <div className="text-xs text-white/70 font-medium">
                  From KES 2M
                </div>
              </div>
            </Link>

            {/* Airbnb & Vacation Rentals */}
            <Link
              href="/properties?type=airbnb"
              className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-success-500 to-secondary-500 p-6 sm:p-8 hover:from-success-600 hover:to-secondary-600 transition-all duration-300 text-white transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-colors duration-300"></div>
              <div className="relative text-center sm:text-left">
                <div className="flex flex-col sm:flex-row items-center sm:items-start mb-4">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mb-3 sm:mb-0 sm:mr-4">
                    <span className="text-2xl">🏖️</span>
                  </div>
                  <div>
                    <Typography variant="h4" className="text-white font-serif mb-2">
                      Vacation Stays
                    </Typography>
                    <Typography variant="body" className="text-white/90 text-sm">
                      Short-term stays and holiday accommodations
                    </Typography>
                  </div>
                </div>
                <div className="text-xs text-white/70 font-medium">
                  From KES 3,000/night
                </div>
              </div>
            </Link>

            {/* List Your Property */}
            <Link
              href="/list-your-property"
              className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-accent-500 to-primary-500 p-6 sm:p-8 hover:from-accent-600 hover:to-primary-600 transition-all duration-300 text-white transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-colors duration-300"></div>
              <div className="relative text-center sm:text-left">
                <div className="flex flex-col sm:flex-row items-center sm:items-start mb-4">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mb-3 sm:mb-0 sm:mr-4">
                    <span className="text-2xl">📝</span>
                  </div>
                  <div>
                    <Typography variant="h4" className="text-white font-serif mb-2">
                      List Your Property
                    </Typography>
                    <Typography variant="body" className="text-white/90 text-sm">
                      Earn income from your property investment
                    </Typography>
                  </div>
                </div>
                <div className="text-xs text-white/70 font-medium">
                  Free to list
                </div>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16 sm:py-24 bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Typography variant="h2" className="text-gray-900 font-serif mb-4">
              How RiftStays Works
            </Typography>
            <Typography variant="lead" className="text-gray-600 max-w-3xl mx-auto">
              Finding your perfect home or listing your property has never been easier
            </Typography>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
            {/* Step 1 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-warning-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl text-white font-bold">1</span>
              </div>
              <Typography variant="h4" className="text-gray-900 font-serif mb-4">
                Search & Discover
              </Typography>
              <Typography variant="body" className="text-gray-600">
                Browse thousands of verified properties across Kenya. Use our smart filters to find exactly what you're looking for.
              </Typography>
            </div>

            {/* Step 2 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-secondary-500 to-accent-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl text-white font-bold">2</span>
              </div>
              <Typography variant="h4" className="text-gray-900 font-serif mb-4">
                Connect & Visit
              </Typography>
              <Typography variant="body" className="text-gray-600">
                Contact property owners directly or schedule viewings. Our platform ensures secure and transparent communication.
              </Typography>
            </div>

            {/* Step 3 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-success-500 to-primary-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl text-white font-bold">3</span>
              </div>
              <Typography variant="h4" className="text-gray-900 font-serif mb-4">
                Move In or List
              </Typography>
              <Typography variant="body" className="text-gray-600">
                Complete your rental, purchase, or listing with confidence. We support you throughout the entire process.
              </Typography>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Properties */}
      <section className="py-16 sm:py-24 bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Typography variant="h2" className="text-gray-900 font-serif mb-4">
              Featured Properties
            </Typography>
            <Typography variant="lead" className="text-gray-600 max-w-3xl mx-auto">
              Handpicked selection of exceptional properties across Kenya
            </Typography>
          </div>
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            <Suspense fallback={
              <>
                <PropertyCardSkeleton />
                <PropertyCardSkeleton />
                <PropertyCardSkeleton />
              </>
            }>
              {/* Featured Property 1 - Safari Lodge */}
              <div className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="relative h-64">
                  <Image
                    src="/images/luxury-safari-lodge.jpg"
                    alt="Luxury Safari Lodge"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4 bg-gradient-to-r from-primary-500 to-warning-500 text-white px-3 py-1 rounded-full text-golden-sm font-semibold">
                    Featured
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-golden-xl font-semibold text-gray-900 font-serif">Modern 3BR Apartment - Kilimani</h3>
                  <p className="mt-2 text-gray-600 flex items-center text-golden-base">
                    <svg className="w-4 h-4 mr-1 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 616 0z" />
                    </svg>
                    Kilimani, Nairobi
                  </p>
                  <div className="mt-4 flex items-center justify-between">
                    <p className="text-golden-2xl font-bold text-primary-600">KES 85,000<span className="text-golden-sm font-normal text-gray-500">/month</span></p>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-warning-400 fill-current" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      <span className="ml-1 text-golden-sm text-gray-600">4.9</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Featured Property 2 - Beach Resort */}
              <div className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="relative h-64">
                  <Image
                    src="/images/coastal-beach-resort.jpg"
                    alt="Coastal Beach Resort"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4 bg-gradient-to-r from-secondary-500 to-accent-500 text-white px-3 py-1 rounded-full text-golden-sm font-semibold">
                    Popular
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-golden-xl font-semibold text-gray-900 font-serif">Beachfront Villa - Diani</h3>
                  <p className="mt-2 text-gray-600 flex items-center text-golden-base">
                    <svg className="w-4 h-4 mr-1 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Diani Beach, Mombasa
                  </p>
                  <div className="mt-4 flex items-center justify-between">
                    <p className="text-golden-2xl font-bold text-secondary-600">KES 12M<span className="text-golden-sm font-normal text-gray-500"> for sale</span></p>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-warning-400 fill-current" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      <span className="ml-1 text-golden-sm text-gray-600">4.8</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Featured Property 3 - City Hotel */}
              <div className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="relative h-64">
                  <Image
                    src="/images/luxury-city-hotel.jpg"
                    alt="Luxury City Hotel"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4 bg-gradient-to-r from-success-500 to-secondary-500 text-white px-3 py-1 rounded-full text-golden-sm font-semibold">
                    Luxury
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-golden-xl font-semibold text-gray-900 font-serif">Cozy Airbnb - Karen</h3>
                  <p className="mt-2 text-gray-600 flex items-center text-golden-base">
                    <svg className="w-4 h-4 mr-1 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Karen, Nairobi
                  </p>
                  <div className="mt-4 flex items-center justify-between">
                    <p className="text-golden-2xl font-bold text-success-600">KES 8,500<span className="text-golden-sm font-normal text-gray-500">/night</span></p>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-warning-400 fill-current" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      <span className="ml-1 text-golden-sm text-gray-600">4.7</span>
                    </div>
                  </div>
                </div>
              </div>
            </Suspense>
          </div>

          {/* View All Properties CTA */}
          <div className="text-center mt-12">
            <Link href="/properties">
              <Button variant="primary" className="px-8 py-3">
                View All Properties
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 sm:py-24 bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Typography variant="h2" className="text-gray-900 font-serif mb-4">
              What Our Users Say
            </Typography>
            <Typography variant="lead" className="text-gray-600 max-w-3xl mx-auto">
              Join thousands of satisfied property owners and tenants across Kenya
            </Typography>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="bg-gray-50 rounded-2xl p-6 sm:p-8">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <Typography variant="body" className="text-gray-700 mb-6 italic">
                "RiftStays made finding my dream apartment in Westlands so easy. The platform is user-friendly and all properties are verified."
              </Typography>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-primary-400 to-warning-400 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">SM</span>
                </div>
                <div className="ml-4">
                  <Typography variant="small" className="font-semibold text-gray-900">
                    Sarah Mwangi
                  </Typography>
                  <Typography variant="small" className="text-gray-600">
                    Tenant, Westlands
                  </Typography>
                </div>
              </div>
            </div>

            {/* Testimonial 2 */}
            <div className="bg-gray-50 rounded-2xl p-6 sm:p-8">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <Typography variant="body" className="text-gray-700 mb-6 italic">
                "I listed my property and found quality tenants within a week. The platform's reach is incredible!"
              </Typography>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-secondary-400 to-accent-400 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">JK</span>
                </div>
                <div className="ml-4">
                  <Typography variant="small" className="font-semibold text-gray-900">
                    John Kamau
                  </Typography>
                  <Typography variant="small" className="text-gray-600">
                    Property Owner, Karen
                  </Typography>
                </div>
              </div>
            </div>

            {/* Testimonial 3 */}
            <div className="bg-gray-50 rounded-2xl p-6 sm:p-8">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <Typography variant="body" className="text-gray-700 mb-6 italic">
                "Perfect for vacation rentals! Our Airbnb bookings increased significantly after listing on RiftStays."
              </Typography>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-success-400 to-primary-400 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">AN</span>
                </div>
                <div className="ml-4">
                  <Typography variant="small" className="font-semibold text-gray-900">
                    Alice Njeri
                  </Typography>
                  <Typography variant="small" className="text-gray-600">
                    Airbnb Host, Diani
                  </Typography>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Recent Blog Posts */}
      <section className="py-16 sm:py-24 bg-gradient-to-br from-secondary-50 via-white to-accent-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Typography variant="h2" className="text-gray-900 font-serif mb-4">
              Property Insights & Market Updates
            </Typography>
            <Typography variant="lead" className="text-gray-600 max-w-3xl mx-auto">
              Stay informed with the latest property market trends and investment tips
            </Typography>
          </div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <Suspense fallback={
              <>
                <BlogCardSkeleton />
                <BlogCardSkeleton />
                <BlogCardSkeleton />
              </>
            }>
              {/* Blog Post 1 */}
              <article className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="relative h-40 sm:h-48">
                  <Image
                    src="/images/kenya-wildlife-safari.jpg"
                    alt="Nairobi Real Estate Investment Guide"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <span className="inline-block bg-primary-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                      Investment Guide
                    </span>
                  </div>
                </div>
                <div className="p-4 sm:p-6">
                  <Typography variant="h4" className="text-gray-900 font-serif group-hover:text-primary-600 transition-colors mb-3 line-clamp-2">
                    Best Areas to Invest in Nairobi Real Estate
                  </Typography>
                  <Typography variant="body" className="text-gray-600 line-clamp-2 mb-4">
                    Discover the most promising neighborhoods for property investment with high rental yields and capital appreciation.
                  </Typography>
                  <div className="flex items-center gap-3">
                    <div className="h-8 w-8 rounded-full bg-gradient-to-r from-primary-400 to-warning-400 flex items-center justify-center">
                      <span className="text-white font-semibold text-xs">RE</span>
                    </div>
                    <div>
                      <Typography variant="small" className="font-medium text-gray-900">
                        Real Estate Expert
                      </Typography>
                      <Typography variant="small" className="text-gray-600">
                        Dec 1, 2024
                      </Typography>
                    </div>
                  </div>
                </div>
              </article>

              {/* Blog Post 2 */}
              <article className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="relative h-48">
                  <Image
                    src="/images/kenyan-coast-beach.jpg"
                    alt="Kenyan Coast Beach"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <span className="inline-block bg-secondary-500 text-white px-3 py-1 rounded-full text-golden-sm font-semibold">
                      Rental Tips
                    </span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-golden-xl font-semibold text-gray-900 font-serif group-hover:text-secondary-600 transition-colors">
                    How to Maximize Your Rental Income
                  </h3>
                  <p className="mt-3 text-gray-600 line-clamp-2 text-golden-base leading-relaxed">
                    Learn proven strategies to increase your property's rental yield and attract quality tenants in Kenya's competitive market...
                  </p>
                  <div className="mt-6 flex items-center gap-4">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-r from-secondary-400 to-accent-400 flex items-center justify-center">
                      <span className="text-white font-semibold text-golden-sm">PM</span>
                    </div>
                    <div>
                      <p className="text-golden-sm font-medium text-gray-900">Property Manager</p>
                      <p className="text-golden-sm text-gray-600">Nov 28, 2024</p>
                    </div>
                  </div>
                </div>
              </article>

              {/* Blog Post 3 */}
              <article className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="relative h-48">
                  <Image
                    src="/images/kenyan-culture.jpg"
                    alt="Kenyan Culture and People"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <span className="inline-block bg-accent-500 text-white px-3 py-1 rounded-full text-golden-sm font-semibold">
                      Market Update
                    </span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-golden-xl font-semibold text-gray-900 font-serif group-hover:text-accent-600 transition-colors">
                    Kenya Property Market Trends 2024
                  </h3>
                  <p className="mt-3 text-gray-600 line-clamp-2 text-golden-base leading-relaxed">
                    Comprehensive analysis of property prices, demand patterns, and emerging opportunities across major Kenyan cities...
                  </p>
                  <div className="mt-6 flex items-center gap-4">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-r from-accent-400 to-primary-400 flex items-center justify-center">
                      <span className="text-white font-semibold text-golden-sm">MA</span>
                    </div>
                    <div>
                      <p className="text-golden-sm font-medium text-gray-900">Market Analyst</p>
                      <p className="text-golden-sm text-gray-600">Nov 25, 2024</p>
                    </div>
                  </div>
                </div>
              </article>
            </Suspense>
          </div>

          {/* View All Blog Posts CTA */}
          <div className="text-center mt-12">
            <Link href="/blog">
              <Button variant="outline" className="px-8 py-3">
                Read More Articles
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-16 sm:py-24 bg-gradient-to-br from-primary-600 via-secondary-600 to-accent-600 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-hero-pattern opacity-20"></div>

        <div className="relative mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 text-center">
          <Typography variant="h2" className="text-white font-serif mb-6">
            Ready to Find Your Perfect Home?
          </Typography>
          <Typography variant="lead" className="text-white/90 mb-8 max-w-2xl mx-auto">
            Join thousands of Kenyans who have found their ideal properties through RiftStays.
            Start your journey today.
          </Typography>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6">
            <Link href="/properties">
              <Button
                variant="secondary"
                className="w-full sm:w-auto bg-white text-primary-600 hover:bg-gray-50 font-semibold px-8 py-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                🔍 Search Properties
              </Button>
            </Link>
            <Link href="/list-your-property">
              <Button
                variant="outline"
                className="w-full sm:w-auto border-2 border-white text-white hover:bg-white hover:text-primary-600 font-semibold px-8 py-4 rounded-full transition-all duration-300"
              >
                📝 List Your Property
              </Button>
            </Link>
          </div>

          {/* Trust Indicators */}
          <div className="mt-12 grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
            <div className="text-white/80">
              <Typography variant="small" className="font-semibold">
                ✓ Verified Properties
              </Typography>
            </div>
            <div className="text-white/80">
              <Typography variant="small" className="font-semibold">
                ✓ Secure Platform
              </Typography>
            </div>
            <div className="text-white/80">
              <Typography variant="small" className="font-semibold">
                ✓ Expert Support
              </Typography>
            </div>
            <div className="text-white/80">
              <Typography variant="small" className="font-semibold">
                ✓ Free to Use
              </Typography>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
