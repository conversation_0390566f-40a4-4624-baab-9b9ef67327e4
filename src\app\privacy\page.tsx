import { Metadata } from 'next'
import { Typography } from '@/components/ui/Typography'

export const metadata: Metadata = {
  title: 'Privacy Policy | RiftStays',
  description: 'Learn how RiftStays protects your privacy and handles your personal information.',
}

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white rounded-2xl shadow-xl p-8 lg:p-12">
          <Typography variant="h1" className="text-center mb-8">
            Privacy Policy
          </Typography>
          
          <div className="prose prose-lg max-w-none">
            <Typography variant="body" className="text-gray-600 mb-8">
              Last updated: {new Date().toLocaleDateString()}
            </Typography>

            <section className="mb-8">
              <Typography variant="h2" className="mb-4">
                1. Information We Collect
              </Typography>
              <Typography variant="body" className="mb-4">
                We collect information you provide directly to us, such as when you create an account, 
                list a property, make a booking, or contact us for support.
              </Typography>
              <ul className="list-disc pl-6 mb-4 space-y-2">
                <li>Personal information (name, email, phone number)</li>
                <li>Property information and photos</li>
                <li>Payment and billing information</li>
                <li>Communication preferences</li>
              </ul>
            </section>

            <section className="mb-8">
              <Typography variant="h2" className="mb-4">
                2. How We Use Your Information
              </Typography>
              <Typography variant="body" className="mb-4">
                We use the information we collect to:
              </Typography>
              <ul className="list-disc pl-6 mb-4 space-y-2">
                <li>Provide and improve our services</li>
                <li>Process bookings and payments</li>
                <li>Send you important updates and notifications</li>
                <li>Provide customer support</li>
                <li>Comply with legal obligations</li>
              </ul>
            </section>

            <section className="mb-8">
              <Typography variant="h2" className="mb-4">
                3. Information Sharing
              </Typography>
              <Typography variant="body" className="mb-4">
                We do not sell, trade, or otherwise transfer your personal information to third parties 
                without your consent, except as described in this policy.
              </Typography>
            </section>

            <section className="mb-8">
              <Typography variant="h2" className="mb-4">
                4. Data Security
              </Typography>
              <Typography variant="body" className="mb-4">
                We implement appropriate security measures to protect your personal information against 
                unauthorized access, alteration, disclosure, or destruction.
              </Typography>
            </section>

            <section className="mb-8">
              <Typography variant="h2" className="mb-4">
                5. Your Rights
              </Typography>
              <Typography variant="body" className="mb-4">
                You have the right to access, update, or delete your personal information. 
                Contact <NAME_EMAIL> for any privacy-related requests.
              </Typography>
            </section>

            <section className="mb-8">
              <Typography variant="h2" className="mb-4">
                6. Contact Us
              </Typography>
              <Typography variant="body">
                If you have any questions about this Privacy Policy, please contact us at:
                <br />
                Email: <EMAIL>
                <br />
                Phone: +254 700 000 000
                <br />
                Address: Nairobi, Kenya
              </Typography>
            </section>
          </div>
        </div>
      </div>
    </div>
  )
}
