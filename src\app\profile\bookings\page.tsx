'use client'

import { useState, useEffect } from 'react'
import { ProfileLayout } from '@/components/profile/ProfileLayout'
import { BookingHistory } from '@/components/profile/BookingHistory'
import { User } from '@/types/auth'

export default function BookingsPage() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchUserData()
  }, [])

  const fetchUserData = async () => {
    try {
      // Mock user data - replace with actual API call
      const mockUser: User = {
        _id: 'user123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'customer',
        status: 'active',
        permissions: [],
        createdAt: '2024-01-15T00:00:00Z',
        updatedAt: '2024-12-19T00:00:00Z'
      }
      setUser(mockUser)
    } catch (error) {
      console.error('Failed to fetch user data:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <ProfileLayout user={user}>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">My Bookings</h1>
          <p className="text-gray-600">View and manage all your property and tour bookings</p>
        </div>

        {user && <BookingHistory userId={user._id} />}
      </div>
    </ProfileLayout>
  )
}
