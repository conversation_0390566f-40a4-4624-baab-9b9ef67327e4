'use client'

import { useState, useEffect } from 'react'
import { ProfileLayout } from '@/components/profile/ProfileLayout'
import { User } from '@/types/auth'
import { Property } from '@/types/property'
import { Tour } from '@/types/tour'
import { PropertyCard } from '@/components/properties/PropertyCard'
import { TourCard } from '@/components/tours/TourCard'
import { HeartIcon, TrashIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

interface FavoriteItem {
  _id: string
  type: 'property' | 'tour'
  item: Property | Tour
  addedAt: string
}

export default function FavoritesPage() {
  const [user, setUser] = useState<User | null>(null)
  const [favorites, setFavorites] = useState<FavoriteItem[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'properties' | 'tours'>('all')

  useEffect(() => {
    fetchUserData()
    fetchFavorites()
  }, [])

  const fetchUserData = async () => {
    try {
      // Mock user data - replace with actual API call
      const mockUser: User = {
        _id: 'user123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'customer',
        status: 'active',
        permissions: [],
        createdAt: '2024-01-15T00:00:00Z',
        updatedAt: '2024-12-19T00:00:00Z'
      }
      setUser(mockUser)
    } catch (error) {
      console.error('Failed to fetch user data:', error)
    }
  }

  const fetchFavorites = async () => {
    try {
      // Mock favorites data - replace with actual API call
      const mockFavorites: FavoriteItem[] = [
        {
          _id: 'fav1',
          type: 'property',
          addedAt: '2024-12-15T10:30:00Z',
          item: {
            _id: 'prop1',
            title: 'Luxury Apartment - Kilimani',
            slug: { current: 'luxury-apartment-kilimani' },
            description: 'Modern 3-bedroom apartment with stunning city views',
            price: 85000,
            location: 'Kilimani, Nairobi',
            bedrooms: 3,
            bathrooms: 2,
            maxGuests: 6,
            category: 'luxury',
            propertyType: 'apartment',
            purpose: 'rental',
            images: [],
            featured: true,
            verified: true,
            status: 'available',
            createdAt: '2024-11-01T00:00:00Z',
            updatedAt: '2024-12-01T00:00:00Z'
          } as Property
        },
        {
          _id: 'fav2',
          type: 'tour',
          addedAt: '2024-12-10T14:20:00Z',
          item: {
            _id: 'tour1',
            title: 'Maasai Mara Safari Experience',
            slug: { current: 'maasai-mara-safari-experience' },
            description: 'Experience the great migration and wildlife of Maasai Mara',
            shortDescription: '3-day safari adventure',
            price: 45000,
            duration: 3,
            maxGuests: 8,
            minGuests: 2,
            difficulty: 'moderate',
            category: 'wildlife',
            location: 'Maasai Mara, Kenya',
            images: [],
            featured: true,
            active: true,
            createdAt: '2024-10-15T00:00:00Z',
            updatedAt: '2024-11-20T00:00:00Z'
          } as Tour
        },
        {
          _id: 'fav3',
          type: 'property',
          addedAt: '2024-12-08T09:15:00Z',
          item: {
            _id: 'prop2',
            title: 'Beachfront Villa - Diani',
            slug: { current: 'beachfront-villa-diani' },
            description: 'Stunning beachfront villa with private beach access',
            price: 150000,
            location: 'Diani Beach, Mombasa',
            bedrooms: 4,
            bathrooms: 3,
            maxGuests: 8,
            category: 'luxury',
            propertyType: 'villa',
            purpose: 'airbnb',
            images: [],
            featured: true,
            verified: true,
            status: 'available',
            createdAt: '2024-10-20T00:00:00Z',
            updatedAt: '2024-11-25T00:00:00Z'
          } as Property
        }
      ]
      setFavorites(mockFavorites)
    } catch (error) {
      console.error('Failed to fetch favorites:', error)
    } finally {
      setLoading(false)
    }
  }

  const removeFavorite = async (favoriteId: string) => {
    try {
      // Remove from API - replace with actual API call
      setFavorites(prev => prev.filter(fav => fav._id !== favoriteId))
    } catch (error) {
      console.error('Failed to remove favorite:', error)
    }
  }

  const clearAllFavorites = async () => {
    if (window.confirm('Are you sure you want to remove all favorites?')) {
      try {
        // Clear all from API - replace with actual API call
        setFavorites([])
      } catch (error) {
        console.error('Failed to clear favorites:', error)
      }
    }
  }

  const filteredFavorites = favorites.filter(favorite => {
    if (filter === 'all') return true
    if (filter === 'properties') return favorite.type === 'property'
    if (filter === 'tours') return favorite.type === 'tour'
    return true
  })

  const propertiesCount = favorites.filter(f => f.type === 'property').length
  const toursCount = favorites.filter(f => f.type === 'tour').length

  if (loading) {
    return (
      <ProfileLayout user={user}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout user={user}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Favorites</h1>
            <p className="text-gray-600">Properties and tours you've saved for later</p>
          </div>
          
          {favorites.length > 0 && (
            <button
              onClick={clearAllFavorites}
              className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Clear All
            </button>
          )}
        </div>

        {/* Filter Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setFilter('all')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                filter === 'all'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              All ({favorites.length})
            </button>
            <button
              onClick={() => setFilter('properties')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                filter === 'properties'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Properties ({propertiesCount})
            </button>
            <button
              onClick={() => setFilter('tours')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                filter === 'tours'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Tours ({toursCount})
            </button>
          </nav>
        </div>

        {/* Favorites Grid */}
        {filteredFavorites.length === 0 ? (
          <div className="text-center py-12">
            <HeartIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No favorites yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start exploring and save properties or tours you love
            </p>
            <div className="mt-6">
              <Link
                href="/search"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
              >
                Start Exploring
              </Link>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredFavorites.map((favorite) => (
              <div key={favorite._id} className="relative group">
                {/* Remove Button */}
                <button
                  onClick={() => removeFavorite(favorite._id)}
                  className="absolute top-2 right-2 z-10 p-2 bg-white rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-50"
                  title="Remove from favorites"
                >
                  <TrashIcon className="h-4 w-4 text-red-500" />
                </button>

                {/* Card */}
                {favorite.type === 'property' ? (
                  <PropertyCard property={favorite.item as Property} />
                ) : (
                  <TourCard tour={favorite.item as Tour} />
                )}

                {/* Added Date */}
                <div className="mt-2 text-xs text-gray-500">
                  Added {new Date(favorite.addedAt).toLocaleDateString()}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </ProfileLayout>
  )
}
