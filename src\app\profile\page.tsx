'use client'

import { useState, useEffect } from 'react'
import { ProfileLayout } from '@/components/profile/ProfileLayout'
import { User } from '@/types/auth'
import { 
  CalendarDaysIcon, 
  HeartIcon, 
  BookmarkIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'

interface DashboardStats {
  totalBookings: number
  upcomingBookings: number
  completedBookings: number
  cancelledBookings: number
  savedProperties: number
  savedSearches: number
  unreadNotifications: number
}

interface RecentActivity {
  id: string
  type: 'booking' | 'favorite' | 'search'
  title: string
  description: string
  date: string
  status?: 'confirmed' | 'pending' | 'cancelled'
}

export default function ProfileDashboard() {
  const [user, setUser] = useState<User | null>(null)
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchUserData()
    fetchDashboardStats()
    fetchRecentActivity()
  }, [])

  const fetchUserData = async () => {
    try {
      // Mock user data - replace with actual API call
      const mockUser: User = {
        _id: 'user123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'customer',
        status: 'active',
        permissions: [],
        createdAt: '2024-01-15T00:00:00Z',
        updatedAt: '2024-12-19T00:00:00Z'
      }
      setUser(mockUser)
    } catch (error) {
      console.error('Failed to fetch user data:', error)
    }
  }

  const fetchDashboardStats = async () => {
    try {
      // Mock stats - replace with actual API call
      const mockStats: DashboardStats = {
        totalBookings: 12,
        upcomingBookings: 2,
        completedBookings: 8,
        cancelledBookings: 2,
        savedProperties: 8,
        savedSearches: 3,
        unreadNotifications: 5
      }
      setStats(mockStats)
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error)
    }
  }

  const fetchRecentActivity = async () => {
    try {
      // Mock activity - replace with actual API call
      const mockActivity: RecentActivity[] = [
        {
          id: '1',
          type: 'booking',
          title: 'Safari Lodge Booking Confirmed',
          description: 'Your booking for Maasai Mara Safari Lodge has been confirmed',
          date: '2024-12-18T10:30:00Z',
          status: 'confirmed'
        },
        {
          id: '2',
          type: 'favorite',
          title: 'Added to Favorites',
          description: 'Beachfront Villa - Diani Beach',
          date: '2024-12-17T15:45:00Z'
        },
        {
          id: '3',
          type: 'search',
          title: 'Saved Search',
          description: 'Luxury apartments in Westlands under KES 100,000',
          date: '2024-12-16T09:20:00Z'
        },
        {
          id: '4',
          type: 'booking',
          title: 'Payment Pending',
          description: 'Complete payment for your Nakuru tour booking',
          date: '2024-12-15T14:15:00Z',
          status: 'pending'
        }
      ]
      setRecentActivity(mockActivity)
    } catch (error) {
      console.error('Failed to fetch recent activity:', error)
    } finally {
      setLoading(false)
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'booking':
        return CalendarDaysIcon
      case 'favorite':
        return HeartIcon
      case 'search':
        return BookmarkIcon
      default:
        return ClockIcon
    }
  }

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />
      case 'pending':
        return <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />
      case 'cancelled':
        return <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  if (loading) {
    return (
      <ProfileLayout user={user}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout user={user}>
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Welcome back, {user?.firstName}! Here's what's happening with your account.</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Total Bookings</p>
                <p className="text-2xl font-bold">{stats?.totalBookings || 0}</p>
              </div>
              <CalendarDaysIcon className="h-8 w-8 text-blue-200" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">Upcoming</p>
                <p className="text-2xl font-bold">{stats?.upcomingBookings || 0}</p>
              </div>
              <ClockIcon className="h-8 w-8 text-green-200" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">Saved Properties</p>
                <p className="text-2xl font-bold">{stats?.savedProperties || 0}</p>
              </div>
              <HeartIcon className="h-8 w-8 text-purple-200" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">Saved Searches</p>
                <p className="text-2xl font-bold">{stats?.savedSearches || 0}</p>
              </div>
              <BookmarkIcon className="h-8 w-8 text-orange-200" />
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Link
            href="/search"
            className="bg-primary-50 border border-primary-200 rounded-lg p-6 hover:bg-primary-100 transition-colors duration-200"
          >
            <h3 className="text-lg font-semibold text-primary-900 mb-2">Find Properties</h3>
            <p className="text-primary-700 text-sm">Search for your next home or vacation rental</p>
          </Link>

          <Link
            href="/profile/bookings"
            className="bg-secondary-50 border border-secondary-200 rounded-lg p-6 hover:bg-secondary-100 transition-colors duration-200"
          >
            <h3 className="text-lg font-semibold text-secondary-900 mb-2">Manage Bookings</h3>
            <p className="text-secondary-700 text-sm">View and manage your current bookings</p>
          </Link>

          <Link
            href="/profile/favorites"
            className="bg-accent-50 border border-accent-200 rounded-lg p-6 hover:bg-accent-100 transition-colors duration-200"
          >
            <h3 className="text-lg font-semibold text-accent-900 mb-2">View Favorites</h3>
            <p className="text-accent-700 text-sm">Check out your saved properties and tours</p>
          </Link>
        </div>

        {/* Recent Activity */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Recent Activity</h2>
            <Link href="/profile/notifications" className="text-primary-600 hover:text-primary-800 text-sm font-medium">
              View all notifications
            </Link>
          </div>

          <div className="space-y-4">
            {recentActivity.map((activity) => {
              const IconComponent = getActivityIcon(activity.type)
              return (
                <div key={activity.id} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0">
                    <IconComponent className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                      {getStatusIcon(activity.status)}
                    </div>
                    <p className="text-sm text-gray-600">{activity.description}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {new Date(activity.date).toLocaleDateString()} at {new Date(activity.date).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </ProfileLayout>
  )
}
