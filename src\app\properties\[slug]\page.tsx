import { getPropertyBySlug } from '@/sanity/lib/queries'
import { Property } from '@/types/property'
import { Metadata } from 'next'
import Image from 'next/image'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { OptimizedImage } from '@/components/ui/OptimizedImage'
import { Button } from '@/components/ui/Button'
import { formatPrice } from '@/lib/utils'
import { urlForImageWithDimensions } from '@/sanity/lib/image'

interface PropertyPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: PropertyPageProps): Promise<Metadata> {
  try {
    const property = await getPropertyBySlug(params.slug)
    
    if (!property) {
      return {
        title: 'Property Not Found',
        description: 'The requested property could not be found.'
      }
    }

    const imageUrl = property.images?.[0]
      ? urlForImageWithDimensions(property.images[0], 1200, 630, 85)?.url()
      : undefined

    return {
      title: `${property.title} | RiftStays`,
      description: property.description || `Book ${property.title} on RiftStays`,
      openGraph: {
        title: property.title,
        description: property.description || `Book ${property.title} on RiftStays`,
        images: imageUrl ? [imageUrl] : [],
        type: 'website',
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: 'Property | RiftStays',
      description: 'View this property on RiftStays'
    }
  }
}

export default async function PropertyPage({ params }: PropertyPageProps) {
  try {
    const property = await getPropertyBySlug(params.slug)

    if (!property) {
      notFound()
    }

    return (
      <div className="min-h-screen bg-gray-50">
        {/* Property Header */}
        <div className="bg-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Property Images */}
              <div className="space-y-4">
                {property.images && property.images.length > 0 && (
                  <div className="relative h-96 rounded-lg overflow-hidden">
                    <OptimizedImage
                      src={property.images[0]}
                      alt={property.title}
                      fill
                      className="object-cover"
                      priority
                    />
                  </div>
                )}

                {/* Additional images grid */}
                {property.images && property.images.length > 1 && (
                  <div className="grid grid-cols-3 gap-2">
                    {property.images.slice(1, 4).map((image, index) => (
                      <div key={index} className="relative h-24 rounded overflow-hidden">
                        <OptimizedImage
                          src={image}
                          alt={`${property.title} - Image ${index + 2}`}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Property Details */}
              <div className="space-y-6">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800 capitalize">
                      {property.propertyType}
                    </span>
                    {property.featured && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        Featured
                      </span>
                    )}
                  </div>

                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {property.title}
                  </h1>

                  <div className="flex items-center text-gray-600 mb-4">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {property.location}
                  </div>

                  <div className="text-3xl font-bold text-primary-600 mb-4">
                    {formatPrice(property.price)}/night
                  </div>
                </div>

                {/* Property Stats */}
                <div className="grid grid-cols-3 gap-4 py-4 border-t border-b border-gray-200">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{property.bedrooms}</div>
                    <div className="text-sm text-gray-600">Bedrooms</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{property.bathrooms}</div>
                    <div className="text-sm text-gray-600">Bathrooms</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{property.maxGuests}</div>
                    <div className="text-sm text-gray-600">Max Guests</div>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-3">Description</h2>
                  <p className="text-gray-600 leading-relaxed">{property.description}</p>
                </div>

                {/* Amenities */}
                {property.amenities && property.amenities.length > 0 && (
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-3">Amenities</h2>
                    <div className="grid grid-cols-2 gap-2">
                      {property.amenities.map((amenity, index) => (
                        <div key={index} className="flex items-center text-gray-600">
                          <svg className="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          {amenity}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Booking Button */}
                <div className="pt-6">
                  <Link href={`/booking/${property._id}?type=house`}>
                    <Button className="w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                      Book Now
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error fetching property:', error)
    notFound()
  }
}
