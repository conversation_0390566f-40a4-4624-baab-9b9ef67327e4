import { Suspense } from 'react'
import Link from 'next/link'
import { FilterSidebar } from '@/components/properties/FilterSidebar'
import { PropertyCard } from '@/components/properties/PropertyCard'
import { Property } from '@/types/property'
import { PropertyCardSkeleton } from '@/components/ui/LoadingSpinner'
import { Typography } from '@/components/ui/Typography'
import { Button } from '@/components/ui/Button'
import { OptimizedImage } from '@/components/ui/OptimizedImage'
import { Breadcrumb } from '@/components/navigation/Breadcrumb'
import { getPropertiesForListing } from '@/sanity/lib/queries'
// Remove sampleProperties import as we're now using real data

export const metadata = {
  title: 'Properties for Rent, Sale & Airbnb | RiftStays Kenya',
  description: 'Browse properties for rent, sale, and short-term stays across Kenya. Find apartments, houses, and vacation rentals in Nairobi, Mombasa, and beyond.',
  keywords: 'properties Kenya, houses for rent, apartments for sale, Airbnb Kenya, real estate Kenya',
}

export default async function PropertiesPage() {
  // Fetch properties from Sanity CMS
  let properties: Property[] = []

  try {
    properties = await getPropertiesForListing()
  } catch (error) {
    console.error('Failed to fetch properties:', error)
    // Fallback to empty array - could also use sample data as fallback
    properties = []
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <Breadcrumb className="mb-6" />

          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <Typography variant="h1" className="text-gray-900 mb-2">
                Properties in Kenya
              </Typography>
              <Typography variant="body" className="text-gray-600">
                {properties.length} properties available for rent, sale, and short-term stays
              </Typography>
            </div>
            <div className="mt-4 md:mt-0">
              <Link href="/list-your-property">
                <Button variant="primary">
                  List Your Property
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="lg:grid lg:grid-cols-4 lg:gap-8">
          {/* Sidebar */}
          <aside className="lg:col-span-1">
            <div className="sticky top-4">
              <Suspense fallback={<div className="h-96 bg-white rounded-lg shadow animate-pulse" />}>
                <FilterSidebar properties={properties} />
              </Suspense>
            </div>
          </aside>

          {/* Properties Grid */}
          <main className="lg:col-span-3 mt-8 lg:mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {properties.map((property) => (
                <Suspense key={property._id} fallback={<PropertyCardSkeleton />}>
                  <PropertyCard property={property} />
                </Suspense>
              ))}
            </div>

            {/* Empty State */}
            {properties.length === 0 && (
              <div className="text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <Typography variant="h3" className="mt-4 text-gray-900">
                  No properties found
                </Typography>
                <Typography variant="body" className="mt-2 text-gray-600">
                  Try adjusting your search filters or check back later for new listings.
                </Typography>
                <div className="mt-6">
                  <Link href="/list-your-property">
                    <Button variant="primary">
                      List Your Property
                    </Button>
                  </Link>
                </div>
              </div>
            )}

            {/* Pagination would go here */}
            {properties.length > 0 && (
              <div className="mt-8 flex justify-center">
                <Typography variant="small" className="text-gray-600">
                  Showing {properties.length} properties
                </Typography>
              </div>
            )}
          </main>
        </div>

        {/* Call to Action */}
        <div className="mt-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 text-center text-white">
          <Typography variant="h2" className="text-white mb-4">
            Can't find what you're looking for?
          </Typography>
          <Typography variant="lead" className="text-white/90 mb-6 max-w-2xl mx-auto">
            List your property with us and reach thousands of potential tenants and buyers across Kenya.
          </Typography>
          <Link href="/list-your-property">
            <Button
              variant="secondary"
              className="bg-white text-primary-600 hover:bg-gray-50 font-semibold px-8 py-3 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              List Your Property
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}