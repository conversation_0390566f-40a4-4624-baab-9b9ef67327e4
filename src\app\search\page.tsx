'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Property } from '@/types/property'
import { Tour } from '@/types/tour'
import { AdvancedSearchFilters } from '@/lib/search'
import { SearchFilters } from '@/components/search/SearchFilters'
import { AdvancedSearchModal } from '@/components/search/AdvancedSearchModal'
import { MapSearch } from '@/components/search/MapSearch'
import { PropertyCard } from '@/components/properties/PropertyCard'
import { TourCard } from '@/components/tours/TourCard'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { MapIcon, ListBulletIcon } from '@heroicons/react/24/outline'

function SearchPageContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  
  const [properties, setProperties] = useState<Property[]>([])
  const [tours, setTours] = useState<Tour[]>([])
  const [loading, setLoading] = useState(false)
  const [totalResults, setTotalResults] = useState(0)
  const [filters, setFilters] = useState<AdvancedSearchFilters>({})
  const [showAdvancedModal, setShowAdvancedModal] = useState(false)
  const [viewMode, setViewMode] = useState<'list' | 'map'>('list')
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(false)

  // Initialize filters from URL params
  useEffect(() => {
    const initialFilters: AdvancedSearchFilters = {}
    
    if (searchParams.get('q')) initialFilters.query = searchParams.get('q')!
    if (searchParams.get('location')) initialFilters.location = searchParams.get('location')!
    if (searchParams.get('propertyType')) {
      initialFilters.propertyType = searchParams.get('propertyType')!.split(',')
    }
    if (searchParams.get('tourCategory')) {
      initialFilters.tourCategory = searchParams.get('tourCategory')!.split(',')
    }
    if (searchParams.get('minPrice') || searchParams.get('maxPrice')) {
      initialFilters.priceRange = {
        min: parseInt(searchParams.get('minPrice') || '0'),
        max: parseInt(searchParams.get('maxPrice') || '0')
      }
    }
    if (searchParams.get('sortBy')) {
      initialFilters.sortBy = searchParams.get('sortBy') as AdvancedSearchFilters['sortBy']
    }

    setFilters(initialFilters)
    performSearch(initialFilters, 1)
  }, [searchParams])

  const performSearch = async (searchFilters: AdvancedSearchFilters, pageNum = 1) => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      
      Object.entries(searchFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== '' && value !== null) {
          if (Array.isArray(value) && value.length > 0) {
            params.set(key, value.join(','))
          } else if (typeof value === 'object' && value !== null) {
            Object.entries(value).forEach(([subKey, subValue]) => {
              if (subValue !== undefined && subValue !== '' && subValue !== 0) {
                params.set(`${key === 'priceRange' ? (subKey === 'min' ? 'minPrice' : 'maxPrice') : subKey}`, subValue.toString())
              }
            })
          } else {
            params.set(key, value.toString())
          }
        }
      })

      params.set('page', pageNum.toString())
      params.set('limit', '12')

      const response = await fetch(`/api/search?${params.toString()}`)
      
      if (response.ok) {
        const data = await response.json()
        
        if (pageNum === 1) {
          setProperties(data.data.properties || [])
          setTours(data.data.tours || [])
        } else {
          setProperties(prev => [...prev, ...(data.data.properties || [])])
          setTours(prev => [...prev, ...(data.data.tours || [])])
        }
        
        setTotalResults((data.data.totalProperties || 0) + (data.data.totalTours || 0))
        setHasMore(data.data.hasMore || false)
        setPage(pageNum)
      }
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFiltersChange = (newFilters: AdvancedSearchFilters) => {
    setFilters(newFilters)
    updateURL(newFilters)
    performSearch(newFilters, 1)
  }

  const updateURL = (searchFilters: AdvancedSearchFilters) => {
    const params = new URLSearchParams()
    
    Object.entries(searchFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== '' && value !== null) {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','))
        } else if (typeof value === 'object' && value !== null) {
          Object.entries(value).forEach(([subKey, subValue]) => {
            if (subValue !== undefined && subValue !== '' && subValue !== 0) {
              params.set(subKey === 'min' ? 'minPrice' : subKey === 'max' ? 'maxPrice' : subKey, subValue.toString())
            }
          })
        } else {
          params.set(key, value.toString())
        }
      }
    })

    router.push(`/search?${params.toString()}`)
  }

  const handleLocationSearch = async (lat: number, lng: number, radius: number) => {
    const locationFilters = {
      ...filters,
      coordinates: { lat, lng, radius }
    }
    setFilters(locationFilters)
    performSearch(locationFilters, 1)
  }

  const loadMore = () => {
    if (hasMore && !loading) {
      performSearch(filters, page + 1)
    }
  }

  const allResults = [...properties, ...tours]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Search Filters */}
      <SearchFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onAdvancedSearch={() => setShowAdvancedModal(true)}
        resultCount={totalResults}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* View Mode Toggle */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            Search Results
            {totalResults > 0 && (
              <span className="text-lg font-normal text-gray-600 ml-2">
                ({totalResults} found)
              </span>
            )}
          </h1>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md ${
                viewMode === 'list'
                  ? 'bg-primary-100 text-primary-700'
                  : 'bg-white text-gray-400 hover:text-gray-600'
              }`}
            >
              <ListBulletIcon className="h-5 w-5" />
            </button>
            <button
              onClick={() => setViewMode('map')}
              className={`p-2 rounded-md ${
                viewMode === 'map'
                  ? 'bg-primary-100 text-primary-700'
                  : 'bg-white text-gray-400 hover:text-gray-600'
              }`}
            >
              <MapIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        {viewMode === 'list' ? (
          <div>
            {loading && page === 1 ? (
              <div className="flex justify-center py-12">
                <LoadingSpinner />
              </div>
            ) : allResults.length > 0 ? (
              <div>
                {/* Results Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {properties.map((property) => (
                    <PropertyCard key={property._id} property={property} />
                  ))}
                  {tours.map((tour) => (
                    <TourCard key={tour._id} tour={tour} />
                  ))}
                </div>

                {/* Load More Button */}
                {hasMore && (
                  <div className="flex justify-center mt-8">
                    <button
                      onClick={loadMore}
                      disabled={loading}
                      className="px-6 py-3 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? 'Loading...' : 'Load More'}
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">No results found</p>
                <p className="text-gray-400 mt-2">Try adjusting your search criteria</p>
              </div>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Map */}
            <div className="lg:col-span-1">
              <MapSearch
                onLocationSelect={(lat, lng, address) => {
                  console.log('Location selected:', { lat, lng, address })
                }}
                onSearch={handleLocationSearch}
                className="h-full"
              />
            </div>

            {/* Results List */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-y-auto">
                <div className="p-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">
                    Results ({totalResults})
                  </h3>
                </div>
                <div className="p-4 space-y-4">
                  {allResults.map((item) => (
                    <div key={item._id} className="border border-gray-200 rounded-lg p-4">
                      {'propertyType' in item ? (
                        <PropertyCard property={item as Property} />
                      ) : (
                        <TourCard tour={item as Tour} />
                      )}
                    </div>
                  ))}
                  
                  {allResults.length === 0 && !loading && (
                    <p className="text-gray-500 text-center py-8">
                      No results found in this area
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Advanced Search Modal */}
      <AdvancedSearchModal
        isOpen={showAdvancedModal}
        onClose={() => setShowAdvancedModal(false)}
        onSearch={handleFiltersChange}
        initialFilters={filters}
      />
    </div>
  )
}

export default function SearchPage() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <SearchPageContent />
    </Suspense>
  )
}
