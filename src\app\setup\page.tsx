'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

export default function SetupPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState('')

  const handleSetup = async () => {
    setLoading(true)
    setError('')
    setResult(null)

    try {
      const response = await fetch('/api/admin/setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.success) {
        setResult(data)
      } else {
        setError(data.error || 'Setup failed')
      }
    } catch (error) {
      console.error('Setup error:', error)
      setError('An error occurred during setup')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
            <svg className="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            RiftStays Setup
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Initialize your RiftStays admin accounts
          </p>
        </div>

        {!result && !error && (
          <div className="text-center">
            <p className="mb-6 text-gray-600">
              Click the button below to create demo admin and employee accounts for development.
            </p>
            <Button
              onClick={handleSetup}
              disabled={loading}
              className="w-full"
            >
              {loading ? <LoadingSpinner className="mr-2" /> : null}
              {loading ? 'Setting up...' : 'Create Demo Accounts'}
            </Button>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
            <button
              onClick={() => setError('')}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        )}

        {result && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
            <h3 className="font-bold mb-2">Setup Complete!</h3>
            <p className="mb-4">{result.message}</p>
            
            <div className="space-y-3">
              <h4 className="font-semibold">Demo Accounts Created:</h4>
              {result.accounts?.map((account: any, index: number) => (
                <div key={index} className="bg-white p-3 rounded border">
                  <div className="text-sm">
                    <div><strong>Email:</strong> {account.email}</div>
                    <div><strong>Password:</strong> {account.password}</div>
                    <div><strong>Role:</strong> {account.role}</div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 space-y-2">
              <a
                href="/admin/login"
                className="block w-full text-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Go to Admin Login
              </a>
              <a
                href="/"
                className="block w-full text-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                Go to Homepage
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
