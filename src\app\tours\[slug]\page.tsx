import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Image from 'next/image'
import { BookingForm } from '@/components/booking/BookingForm'
import { Tour } from '@/types/booking'

interface TourPageProps {
  params: {
    slug: string
  }
}

// Mock tour data (in production, fetch from Sanity)
const mockTour: Tour = {
  _id: '1',
  title: 'Maasai Mara Wildlife Safari',
  slug: { current: 'maasai-mara-wildlife-safari' },
  description: 'Experience the breathtaking wildlife of Maasai Mara National Reserve. Witness the Great Migration, spot the Big Five, and immerse yourself in the natural beauty of Kenya\'s most famous game reserve. This full-day safari includes professional guides, game drives, and a delicious lunch with views of the Mara River.',
  shortDescription: 'Full-day wildlife safari in Maasai Mara with game drives and lunch.',
  images: [
    {
      asset: { url: '/api/placeholder/800/600' },
      alt: 'Maasai Mara Safari'
    },
    {
      asset: { url: '/api/placeholder/800/600' },
      alt: 'Wildlife in Maasai Mara'
    }
  ],
  price: 12000,
  duration: 8,
  maxGuests: 8,
  minGuests: 2,
  difficulty: 'easy',
  category: 'wildlife',
  location: 'Maasai Mara, Kenya',
  coordinates: { lat: -1.4061, lng: 35.0036 },
  includes: ['Transportation from Nairobi', 'Professional safari guide', 'Game drives', 'Lunch at safari lodge', 'Park entrance fees', 'Bottled water'],
  excludes: ['Accommodation', 'Dinner', 'Personal expenses', 'Tips for guide', 'Travel insurance'],
  itinerary: [
    { time: '06:00', activity: 'Departure from Nairobi', description: 'Pick up from your hotel and departure to Maasai Mara National Reserve' },
    { time: '10:00', activity: 'First Game Drive', description: 'Morning game drive to spot wildlife including lions, elephants, and cheetahs' },
    { time: '13:00', activity: 'Lunch Break', description: 'Delicious lunch at a safari lodge with panoramic views of the Mara River' },
    { time: '15:00', activity: 'Afternoon Game Drive', description: 'Continue wildlife viewing and photography opportunities' },
    { time: '17:30', activity: 'Departure', description: 'Begin return journey to Nairobi' },
    { time: '21:00', activity: 'Arrival in Nairobi', description: 'Drop off at your hotel' }
  ],
  meetingPoint: 'Hotel lobby or designated pickup point in Nairobi CBD',
  cancellationPolicy: 'Free cancellation up to 24 hours before the tour. 50% refund for cancellations within 24 hours.',
  availability: {
    daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
    times: ['06:00'],
    blackoutDates: []
  },
  featured: true,
  active: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

export async function generateMetadata({ params }: TourPageProps): Promise<Metadata> {
  // In production, fetch tour data here
  const tour = mockTour

  if (!tour) {
    return {
      title: 'Tour Not Found',
      description: 'The requested tour could not be found.'
    }
  }

  return {
    title: `${tour.title} | RiftStays Tours`,
    description: tour.description,
    openGraph: {
      title: tour.title,
      description: tour.description,
      images: tour.images.map(img => img.asset.url),
      type: 'website',
    },
  }
}

export default function TourPage({ params }: TourPageProps) {
  // In production, fetch tour data based on slug
  const tour = mockTour

  if (!tour) {
    notFound()
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(price)
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'moderate': return 'bg-yellow-100 text-yellow-800'
      case 'challenging': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="relative h-[60vh] min-h-[500px]">
        <Image
          src={tour.images[0]?.asset.url || '/placeholder.jpg'}
          alt={tour.images[0]?.alt || tour.title}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/30" />
        <div className="absolute bottom-0 left-0 right-0 p-8">
          <div className="mx-auto max-w-7xl">
            <div className="flex items-center gap-4 mb-4">
              <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                {tour.category}
              </span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(tour.difficulty)}`}>
                {tour.difficulty}
              </span>
            </div>
            <h1 className="text-4xl font-bold text-white sm:text-5xl mb-2">
              {tour.title}
            </h1>
            <div className="flex items-center text-white/90 text-lg">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              {tour.location}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Overview */}
            <div className="bg-white rounded-lg shadow-sm p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Tour Overview</h2>
              <p className="text-gray-600 leading-relaxed mb-6">
                {tour.description}
              </p>
              
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">{tour.duration}</div>
                  <div className="text-sm text-gray-600">Hours</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">{tour.maxGuests}</div>
                  <div className="text-sm text-gray-600">Max Guests</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">{tour.minGuests}</div>
                  <div className="text-sm text-gray-600">Min Guests</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">{tour.difficulty}</div>
                  <div className="text-sm text-gray-600">Difficulty</div>
                </div>
              </div>
            </div>

            {/* Itinerary */}
            <div className="bg-white rounded-lg shadow-sm p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Itinerary</h3>
              <div className="space-y-4">
                {tour.itinerary.map((item, index) => (
                  <div key={index} className="flex gap-4">
                    <div className="flex-shrink-0 w-16 text-sm font-medium text-blue-600">
                      {item.time}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.activity}</h4>
                      <p className="text-gray-600 text-sm mt-1">{item.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Includes & Excludes */}
            <div className="bg-white rounded-lg shadow-sm p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">What's Included</h3>
                  <ul className="space-y-2">
                    {tour.includes.map((item, index) => (
                      <li key={index} className="flex items-center text-gray-600">
                        <svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">What's Not Included</h3>
                  <ul className="space-y-2">
                    {tour.excludes.map((item, index) => (
                      <li key={index} className="flex items-center text-gray-600">
                        <svg className="w-5 h-5 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            {/* Meeting Point & Cancellation */}
            <div className="bg-white rounded-lg shadow-sm p-8">
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Meeting Point</h3>
                  <p className="text-gray-600">{tour.meetingPoint}</p>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Cancellation Policy</h3>
                  <p className="text-gray-600">{tour.cancellationPolicy}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Booking Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-gray-900">
                    {formatPrice(tour.price)}
                  </div>
                  <div className="text-gray-600">per person</div>
                </div>
                
                <BookingForm
                  type="tour"
                  tourId={tour._id}
                  onBookingCreated={(bookingId) => {
                    // Redirect to payment page
                    window.location.href = `/booking/${bookingId}/payment`
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
