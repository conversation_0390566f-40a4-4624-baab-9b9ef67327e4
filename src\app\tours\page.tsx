import { Suspense } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Tour } from '@/types/tour'
import { PropertyCardSkeleton } from '@/components/ui/LoadingSpinner'
import { Breadcrumb } from '@/components/navigation/Breadcrumb'
import { getTours } from '@/sanity/lib/queries'

// Tours data is now fetched from Sanity CMS

export default async function ToursPage() {
  // Fetch tours from Sanity CMS
  let tours: Tour[] = []

  try {
    tours = await getTours()
  } catch (error) {
    console.error('Failed to fetch tours:', error)
    // Fallback to empty array
    tours = []
  }
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Header */}
      <div className="relative bg-gradient-to-br from-primary-600 via-secondary-500 to-accent-600 text-white py-20 overflow-hidden">
        <div className="absolute inset-0">
          <img
            src="/images/kenya-wildlife-safari.jpg"
            alt="Kenya Wildlife Safari"
            className="w-full h-full object-cover opacity-30"
          />
          <div className="bg-gradient-to-r from-primary-900/80 via-secondary-900/60 to-accent-900/80 absolute inset-0"></div>
        </div>
        <div className="absolute inset-0 bg-hero-pattern"></div>
        <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-5xl font-bold tracking-tight sm:text-6xl font-serif bg-gradient-to-r from-white via-yellow-200 to-orange-200 bg-clip-text text-transparent">
              Tours & Experiences
            </h1>
            <p className="mt-6 text-xl max-w-3xl mx-auto text-gray-100 leading-relaxed">
              Discover Kenya's incredible wildlife, culture, and landscapes with our expert guided tours
            </p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <Breadcrumb className="mb-6" />

        <div className="flex flex-wrap gap-4 mb-8">
          <select className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="">All Categories</option>
            <option value="wildlife">Wildlife Safari</option>
            <option value="cultural">Cultural Tour</option>
            <option value="adventure">Adventure</option>
            <option value="city">City Tour</option>
            <option value="nature">Nature Walk</option>
            <option value="beach">Beach Activity</option>
          </select>
          
          <select className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="">All Difficulties</option>
            <option value="easy">Easy</option>
            <option value="moderate">Moderate</option>
            <option value="challenging">Challenging</option>
          </select>
          
          <select className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="">All Durations</option>
            <option value="half-day">Half Day (2-4 hours)</option>
            <option value="full-day">Full Day (6-8 hours)</option>
            <option value="multi-day">Multi Day</option>
          </select>
        </div>

        {/* Tours Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {tours.map((tour) => (
            <Suspense key={tour._id} fallback={<PropertyCardSkeleton />}>
              <TourCard tour={tour} />
            </Suspense>
          ))}
        </div>

        {/* Empty State */}
        {tours.length === 0 && (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            <h3 className="mt-4 text-lg font-medium text-gray-900">No tours available</h3>
            <p className="mt-2 text-gray-600">Check back later for exciting tour offerings.</p>
          </div>
        )}
      </div>
    </div>
  )
}

function TourCard({ tour }: { tour: Tour }) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(price)
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-success-100 text-success-800'
      case 'moderate': return 'bg-warning-100 text-warning-800'
      case 'challenging': return 'bg-danger-100 text-danger-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTourImage = (tourId: string) => {
    const images = {
      '1': '/images/kenya-safari-landscape.jpg',
      '2': '/images/kenyan-culture.jpg',
      '3': '/images/kenyan-coast-beach.jpg'
    }
    return images[tourId as keyof typeof images] || images['1']
  }

  return (
    <Link href={`/tours/${tour.slug.current}`} className="group block">
      <article className="bg-white rounded-2xl shadow-lg overflow-hidden transition-all duration-300 group-hover:shadow-xl group-hover:-translate-y-2 transform">
        <div className="relative h-56">
          <Image
            src={getTourImage(tour._id)}
            alt={tour.images[0]?.alt || tour.title}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-500"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
          {tour.featured && (
            <div className="absolute top-4 left-4 bg-gradient-to-r from-primary-500 to-warning-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">
              Featured
            </div>
          )}
          <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-sm font-semibold shadow-lg ${getDifficultyColor(tour.difficulty)}`}>
            {tour.difficulty}
          </div>
        </div>
        
        <div className="p-6">
          <div className="flex items-center gap-2 mb-3">
            <span className="text-sm text-primary-600 font-semibold bg-primary-50 px-2 py-1 rounded-full">{tour.category}</span>
            <span className="text-gray-300">•</span>
            <span className="text-sm text-gray-600 font-medium">{tour.duration} hours</span>
          </div>

          <h3 className="text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors line-clamp-2 mb-3 font-serif">
            {tour.title}
          </h3>

          <p className="text-gray-600 text-sm line-clamp-2 mb-4 leading-relaxed">
            {tour.shortDescription}
          </p>

          <div className="flex items-center justify-between mb-4">
            <div>
              <span className="text-2xl font-bold text-primary-600">{formatPrice(tour.price)}</span>
              <span className="text-sm text-gray-600"> per person</span>
            </div>
            <div className="text-sm text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
              {tour.minGuests}-{tour.maxGuests} guests
            </div>
          </div>

          <div className="flex items-center text-sm text-gray-500">
            <svg className="w-4 h-4 mr-1 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            {tour.location}
          </div>
        </div>
      </article>
    </Link>
  )
}
