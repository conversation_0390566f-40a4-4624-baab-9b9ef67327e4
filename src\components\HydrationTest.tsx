'use client'

import { useState, useEffect } from 'react'
import { ClientOnly } from './ClientOnly'
import { formatCurrency, formatDate, formatDateTime, formatPercentage } from '@/lib/formatters'

/**
 * Component to test hydration behavior
 * This component demonstrates proper hydration handling
 */
export function HydrationTest() {
  const [mounted, setMounted] = useState(false)
  const [clientTime, setClientTime] = useState<string>('')

  useEffect(() => {
    setMounted(true)
    setClientTime(new Date().toLocaleTimeString())
  }, [])

  return (
    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
      <h3 className="text-lg font-semibold text-green-800 mb-2">
        Hydration Test Component
      </h3>
      
      <div className="space-y-2 text-sm">
        <p>
          <strong>Component mounted:</strong> {mounted ? '✅ Yes' : '❌ No'}
        </p>
        
        <ClientOnly fallback={<p><strong>Client time:</strong> Loading...</p>}>
          <p>
            <strong>Client time:</strong> {clientTime || 'Not set'}
          </p>
        </ClientOnly>
        
        <p>
          <strong>Window available:</strong> {typeof window !== 'undefined' ? '✅ Yes' : '❌ No (SSR)'}
        </p>
        
        <ClientOnly fallback={<p><strong>localStorage test:</strong> Loading...</p>}>
          <p>
            <strong>localStorage test:</strong> {
              (() => {
                try {
                  localStorage.setItem('hydration-test', 'success')
                  const result = localStorage.getItem('hydration-test')
                  localStorage.removeItem('hydration-test')
                  return result === 'success' ? '✅ Working' : '❌ Failed'
                } catch {
                  return '❌ Not available'
                }
              })()
            }
          </p>
        </ClientOnly>

        <ClientOnly fallback={<p><strong>Formatting tests:</strong> Loading...</p>}>
          <div className="space-y-1">
            <p><strong>Currency formatting:</strong> {formatCurrency(12345)}</p>
            <p><strong>Date formatting:</strong> {formatDate(new Date())}</p>
            <p><strong>DateTime formatting:</strong> {formatDateTime(new Date())}</p>
            <p><strong>Percentage formatting:</strong> {formatPercentage(0.1234)}</p>
          </div>
        </ClientOnly>
      </div>
    </div>
  )
}
