'use client'

import { useState, useEffect } from 'react'
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  HomeIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarDaysIcon
} from '@heroicons/react/24/outline'

interface AnalyticsData {
  overview: {
    totalRevenue: number
    totalBookings: number
    totalProperties: number
    totalUsers: number
    revenueGrowth: number
    bookingsGrowth: number
    propertiesGrowth: number
    usersGrowth: number
  }
  revenueChart: Array<{
    month: string
    revenue: number
    bookings: number
  }>
  topProperties: Array<{
    id: string
    title: string
    location: string
    revenue: number
    bookings: number
    rating: number
  }>
  userActivity: Array<{
    date: string
    newUsers: number
    activeUsers: number
    bookings: number
  }>
  bookingStatus: {
    confirmed: number
    pending: number
    cancelled: number
    completed: number
  }
}

export function AdvancedAnalytics() {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')

  useEffect(() => {
    fetchAnalyticsData()
  }, [timeRange])

  const fetchAnalyticsData = async () => {
    try {
      // Mock analytics data - replace with actual API call
      const mockData: AnalyticsData = {
        overview: {
          totalRevenue: 12500000,
          totalBookings: 1247,
          totalProperties: 156,
          totalUsers: 3421,
          revenueGrowth: 15.3,
          bookingsGrowth: 8.7,
          propertiesGrowth: 12.1,
          usersGrowth: 23.4
        },
        revenueChart: [
          { month: 'Jan', revenue: 850000, bookings: 89 },
          { month: 'Feb', revenue: 920000, bookings: 95 },
          { month: 'Mar', revenue: 1100000, bookings: 112 },
          { month: 'Apr', revenue: 980000, bookings: 98 },
          { month: 'May', revenue: 1250000, bookings: 125 },
          { month: 'Jun', revenue: 1180000, bookings: 118 },
          { month: 'Jul', revenue: 1350000, bookings: 135 },
          { month: 'Aug', revenue: 1420000, bookings: 142 },
          { month: 'Sep', revenue: 1280000, bookings: 128 },
          { month: 'Oct', revenue: 1380000, bookings: 138 },
          { month: 'Nov', revenue: 1450000, bookings: 145 },
          { month: 'Dec', revenue: 1520000, bookings: 152 }
        ],
        topProperties: [
          {
            id: '1',
            title: 'Luxury Villa - Karen',
            location: 'Karen, Nairobi',
            revenue: 450000,
            bookings: 23,
            rating: 4.9
          },
          {
            id: '2',
            title: 'Beachfront Resort - Diani',
            location: 'Diani Beach, Mombasa',
            revenue: 380000,
            bookings: 19,
            rating: 4.8
          },
          {
            id: '3',
            title: 'Safari Lodge - Maasai Mara',
            location: 'Maasai Mara, Kenya',
            revenue: 320000,
            bookings: 16,
            rating: 4.7
          }
        ],
        userActivity: [
          { date: '2024-12-01', newUsers: 45, activeUsers: 234, bookings: 12 },
          { date: '2024-12-02', newUsers: 38, activeUsers: 267, bookings: 15 },
          { date: '2024-12-03', newUsers: 52, activeUsers: 289, bookings: 18 },
          { date: '2024-12-04', newUsers: 41, activeUsers: 245, bookings: 14 },
          { date: '2024-12-05', newUsers: 47, activeUsers: 278, bookings: 16 }
        ],
        bookingStatus: {
          confirmed: 856,
          pending: 234,
          cancelled: 89,
          completed: 68
        }
      }
      setData(mockData)
    } catch (error) {
      console.error('Failed to fetch analytics data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-KE').format(num)
  }

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? (
      <ArrowTrendingUpIcon className="h-4 w-4 text-green-500" />
    ) : (
      <ArrowTrendingDownIcon className="h-4 w-4 text-red-500" />
    )
  }

  const getGrowthColor = (growth: number) => {
    return growth >= 0 ? 'text-green-600' : 'text-red-600'
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
          ))}
        </div>
        <div className="animate-pulse bg-gray-200 h-64 rounded-lg"></div>
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
        <div className="flex space-x-2">
          {[
            { key: '7d', label: '7 Days' },
            { key: '30d', label: '30 Days' },
            { key: '90d', label: '90 Days' },
            { key: '1y', label: '1 Year' }
          ].map((range) => (
            <button
              key={range.key}
              onClick={() => setTimeRange(range.key as any)}
              className={`px-3 py-2 text-sm font-medium rounded-md ${
                timeRange === range.key
                  ? 'bg-primary-100 text-primary-700 border border-primary-300'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              {range.label}
            </button>
          ))}
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(data.overview.totalRevenue)}
              </p>
              <div className={`flex items-center mt-2 ${getGrowthColor(data.overview.revenueGrowth)}`}>
                {getGrowthIcon(data.overview.revenueGrowth)}
                <span className="ml-1 text-sm font-medium">
                  {Math.abs(data.overview.revenueGrowth)}%
                </span>
              </div>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Bookings</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatNumber(data.overview.totalBookings)}
              </p>
              <div className={`flex items-center mt-2 ${getGrowthColor(data.overview.bookingsGrowth)}`}>
                {getGrowthIcon(data.overview.bookingsGrowth)}
                <span className="ml-1 text-sm font-medium">
                  {Math.abs(data.overview.bookingsGrowth)}%
                </span>
              </div>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <CalendarDaysIcon className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Properties</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatNumber(data.overview.totalProperties)}
              </p>
              <div className={`flex items-center mt-2 ${getGrowthColor(data.overview.propertiesGrowth)}`}>
                {getGrowthIcon(data.overview.propertiesGrowth)}
                <span className="ml-1 text-sm font-medium">
                  {Math.abs(data.overview.propertiesGrowth)}%
                </span>
              </div>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <HomeIcon className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatNumber(data.overview.totalUsers)}
              </p>
              <div className={`flex items-center mt-2 ${getGrowthColor(data.overview.usersGrowth)}`}>
                {getGrowthIcon(data.overview.usersGrowth)}
                <span className="ml-1 text-sm font-medium">
                  {Math.abs(data.overview.usersGrowth)}%
                </span>
              </div>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <UserGroupIcon className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Revenue Chart */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Revenue Trend</h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-primary-500 rounded-full mr-2"></div>
              <span className="text-sm text-gray-600">Revenue</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-secondary-500 rounded-full mr-2"></div>
              <span className="text-sm text-gray-600">Bookings</span>
            </div>
          </div>
        </div>
        
        {/* Simple Bar Chart Representation */}
        <div className="space-y-3">
          {data.revenueChart.slice(-6).map((item, index) => (
            <div key={item.month} className="flex items-center space-x-4">
              <div className="w-12 text-sm text-gray-600">{item.month}</div>
              <div className="flex-1 flex items-center space-x-2">
                <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                  <div 
                    className="bg-primary-500 h-4 rounded-full"
                    style={{ width: `${(item.revenue / 1600000) * 100}%` }}
                  ></div>
                </div>
                <div className="w-24 text-sm text-gray-900 text-right">
                  {formatCurrency(item.revenue)}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Top Properties and Booking Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Properties */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Top Performing Properties</h3>
          <div className="space-y-4">
            {data.topProperties.map((property, index) => (
              <div key={property.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-primary-700">#{index + 1}</span>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">{property.title}</h4>
                      <p className="text-xs text-gray-500">{property.location}</p>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{formatCurrency(property.revenue)}</p>
                  <p className="text-xs text-gray-500">{property.bookings} bookings</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Booking Status */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Booking Status Distribution</h3>
          <div className="space-y-4">
            {Object.entries(data.bookingStatus).map(([status, count]) => {
              const total = Object.values(data.bookingStatus).reduce((a, b) => a + b, 0)
              const percentage = (count / total) * 100
              const colors = {
                confirmed: 'bg-green-500',
                pending: 'bg-yellow-500',
                cancelled: 'bg-red-500',
                completed: 'bg-blue-500'
              }
              
              return (
                <div key={status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${colors[status as keyof typeof colors]}`}></div>
                    <span className="text-sm font-medium text-gray-900 capitalize">{status}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${colors[status as keyof typeof colors]}`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-12 text-right">{count}</span>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}
