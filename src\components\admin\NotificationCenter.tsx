'use client'

import { useState, useEffect } from 'react'
import {
  BellIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  TrashIcon,
  FunnelIcon
} from '@heroicons/react/24/outline'
import { formatDateTime } from '@/lib/formatters'

interface Notification {
  _id: string
  type: 'info' | 'warning' | 'success' | 'error'
  title: string
  message: string
  category: 'booking' | 'payment' | 'property' | 'user' | 'system'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  read: boolean
  actionRequired: boolean
  createdAt: string
  data?: any
}

export function NotificationCenter() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'unread' | 'urgent'>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')

  useEffect(() => {
    fetchNotifications()
    
    // Set up real-time notifications (mock)
    const interval = setInterval(() => {
      // Simulate new notifications
      if (Math.random() > 0.8) {
        addNewNotification()
      }
    }, 30000) // Check every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const fetchNotifications = async () => {
    try {
      // Mock notifications data - replace with actual API call
      const mockNotifications: Notification[] = [
        {
          _id: '1',
          type: 'warning',
          title: 'Payment Failed',
          message: 'Payment for booking #RS-2024-001 has failed. Customer needs to retry payment.',
          category: 'payment',
          priority: 'high',
          read: false,
          actionRequired: true,
          createdAt: '2024-12-19T10:30:00Z',
          data: { bookingId: 'RS-2024-001', amount: 85000 }
        },
        {
          _id: '2',
          type: 'success',
          title: 'New Property Listed',
          message: 'Luxury Villa in Karen has been successfully listed and is now available for booking.',
          category: 'property',
          priority: 'medium',
          read: false,
          actionRequired: false,
          createdAt: '2024-12-19T09:15:00Z',
          data: { propertyId: 'prop123' }
        },
        {
          _id: '3',
          type: 'info',
          title: 'New User Registration',
          message: 'Sarah Johnson has registered and completed profile verification.',
          category: 'user',
          priority: 'low',
          read: true,
          actionRequired: false,
          createdAt: '2024-12-19T08:45:00Z',
          data: { userId: 'user456' }
        },
        {
          _id: '4',
          type: 'error',
          title: 'System Alert',
          message: 'High server load detected. Performance monitoring required.',
          category: 'system',
          priority: 'urgent',
          read: false,
          actionRequired: true,
          createdAt: '2024-12-19T07:20:00Z'
        },
        {
          _id: '5',
          type: 'info',
          title: 'Booking Confirmed',
          message: 'Booking #RS-2024-002 for Beachfront Villa has been confirmed.',
          category: 'booking',
          priority: 'medium',
          read: true,
          actionRequired: false,
          createdAt: '2024-12-18T16:30:00Z',
          data: { bookingId: 'RS-2024-002' }
        }
      ]
      setNotifications(mockNotifications)
    } catch (error) {
      console.error('Failed to fetch notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const addNewNotification = () => {
    const newNotification: Notification = {
      _id: Date.now().toString(),
      type: 'info',
      title: 'New Booking Request',
      message: `New booking request received for ${formatDateTime(new Date())}`,
      category: 'booking',
      priority: 'medium',
      read: false,
      actionRequired: true,
      createdAt: new Date().toISOString()
    }
    
    setNotifications(prev => [newNotification, ...prev])
  }

  const markAsRead = async (notificationId: string) => {
    try {
      // Mark as read in API - replace with actual API call
      setNotifications(prev => 
        prev.map(notif => 
          notif._id === notificationId 
            ? { ...notif, read: true }
            : notif
        )
      )
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
    }
  }

  const deleteNotification = async (notificationId: string) => {
    try {
      // Delete from API - replace with actual API call
      setNotifications(prev => prev.filter(notif => notif._id !== notificationId))
    } catch (error) {
      console.error('Failed to delete notification:', error)
    }
  }

  const markAllAsRead = async () => {
    try {
      // Mark all as read in API - replace with actual API call
      setNotifications(prev => prev.map(notif => ({ ...notif, read: true })))
    } catch (error) {
      console.error('Failed to mark all as read:', error)
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
      case 'error':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      default:
        return <InformationCircleIcon className="h-5 w-5 text-blue-500" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500 bg-red-50'
      case 'high':
        return 'border-l-orange-500 bg-orange-50'
      case 'medium':
        return 'border-l-yellow-500 bg-yellow-50'
      default:
        return 'border-l-blue-500 bg-blue-50'
    }
  }

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread' && notification.read) return false
    if (filter === 'urgent' && notification.priority !== 'urgent') return false
    if (categoryFilter !== 'all' && notification.category !== categoryFilter) return false
    return true
  })

  const unreadCount = notifications.filter(n => !n.read).length
  const urgentCount = notifications.filter(n => n.priority === 'urgent' && !n.read).length

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse bg-gray-200 h-20 rounded-lg"></div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BellIcon className="h-6 w-6 text-gray-600" />
          <h2 className="text-2xl font-bold text-gray-900">Notifications</h2>
          {unreadCount > 0 && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
              {unreadCount} unread
            </span>
          )}
          {urgentCount > 0 && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-500 text-white">
              {urgentCount} urgent
            </span>
          )}
        </div>
        
        <button
          onClick={markAllAsRead}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Mark all as read
        </button>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap items-center gap-4">
        <div className="flex items-center space-x-2">
          <FunnelIcon className="h-4 w-4 text-gray-400" />
          <span className="text-sm font-medium text-gray-700">Filter:</span>
        </div>
        
        <div className="flex space-x-2">
          {[
            { key: 'all', label: 'All' },
            { key: 'unread', label: 'Unread' },
            { key: 'urgent', label: 'Urgent' }
          ].map((filterOption) => (
            <button
              key={filterOption.key}
              onClick={() => setFilter(filterOption.key as any)}
              className={`px-3 py-1 text-sm rounded-full border ${
                filter === filterOption.key
                  ? 'bg-primary-100 text-primary-800 border-primary-300'
                  : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
              }`}
            >
              {filterOption.label}
            </button>
          ))}
        </div>

        <div className="flex space-x-2">
          {[
            { key: 'all', label: 'All Categories' },
            { key: 'booking', label: 'Bookings' },
            { key: 'payment', label: 'Payments' },
            { key: 'property', label: 'Properties' },
            { key: 'user', label: 'Users' },
            { key: 'system', label: 'System' }
          ].map((category) => (
            <button
              key={category.key}
              onClick={() => setCategoryFilter(category.key)}
              className={`px-3 py-1 text-sm rounded-full border ${
                categoryFilter === category.key
                  ? 'bg-secondary-100 text-secondary-800 border-secondary-300'
                  : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
              }`}
            >
              {category.label}
            </button>
          ))}
        </div>
      </div>

      {/* Notifications List */}
      <div className="space-y-3">
        {filteredNotifications.length === 0 ? (
          <div className="text-center py-12">
            <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filter === 'all' ? 'All caught up!' : `No ${filter} notifications found.`}
            </p>
          </div>
        ) : (
          filteredNotifications.map((notification) => (
            <div
              key={notification._id}
              className={`border-l-4 p-4 rounded-lg ${getPriorityColor(notification.priority)} ${
                !notification.read ? 'shadow-md' : 'opacity-75'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <div className="flex-shrink-0 mt-0.5">
                    {getNotificationIcon(notification.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className={`text-sm font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>
                        {notification.title}
                      </h4>
                      {!notification.read && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      )}
                      {notification.actionRequired && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                          Action Required
                        </span>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                    
                    <div className="flex items-center space-x-4 mt-2">
                      <span className="text-xs text-gray-500">
                        {formatDateTime(notification.createdAt)}
                      </span>
                      <span className="text-xs text-gray-500 capitalize">
                        {notification.category}
                      </span>
                      <span className={`text-xs font-medium capitalize ${
                        notification.priority === 'urgent' ? 'text-red-600' :
                        notification.priority === 'high' ? 'text-orange-600' :
                        notification.priority === 'medium' ? 'text-yellow-600' :
                        'text-blue-600'
                      }`}>
                        {notification.priority} priority
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  {!notification.read && (
                    <button
                      onClick={() => markAsRead(notification._id)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                      title="Mark as read"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>
                  )}
                  
                  <button
                    onClick={() => deleteNotification(notification._id)}
                    className="p-1 text-gray-400 hover:text-red-600"
                    title="Delete notification"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}
