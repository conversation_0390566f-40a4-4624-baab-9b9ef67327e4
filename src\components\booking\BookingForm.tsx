'use client'

import { useState } from 'react'
import { BookingRequest, BookingType, BookingGuest } from '@/types/booking'
import { Button } from '@/components/ui/Button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Typography } from '@/components/ui/Typography'

interface BookingFormProps {
  type: BookingType
  propertyId?: string
  tourId?: string
  initialDates?: {
    checkIn: string
    checkOut: string
  }
  onBookingCreated?: (bookingId: string) => void
}

export function BookingForm({ 
  type, 
  propertyId, 
  tourId, 
  initialDates,
  onBookingCreated 
}: BookingFormProps) {
  const [loading, setLoading] = useState(false)
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState<Partial<BookingRequest>>({
    type,
    ...(propertyId && { propertyId }),
    ...(tourId && { tourId }),
    ...(initialDates && { dates: initialDates }),
    guests: {
      adults: 2,
      children: 0,
      infants: 0
    },
    contact: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      country: 'Kenya'
    },
    additionalServices: {}
  })

  const handleGuestChange = (field: keyof BookingGuest, value: number) => {
    setFormData(prev => ({
      ...prev,
      guests: {
        ...prev.guests!,
        [field]: Math.max(0, value)
      }
    }))
  }

  const handleContactChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      contact: {
        ...prev.contact!,
        [field]: value
      }
    }))
  }

  const handleServiceChange = (service: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      additionalServices: {
        ...prev.additionalServices,
        [service]: checked
      }
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (result.success) {
        onBookingCreated?.(result.booking.id)
        setStep(4) // Go to payment step
      } else {
        alert(result.error || 'Failed to create booking')
      }
    } catch (error) {
      console.error('Booking error:', error)
      alert('Failed to create booking. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const renderStep1 = () => (
    <div className="space-y-8">
      <Typography variant="h3" className="text-primary-600 mb-6">
        Booking Details
      </Typography>
      
      {type !== 'tour' && (
        <div className="bg-gradient-to-r from-primary-50 to-secondary-50 p-6 rounded-xl border border-primary-100">
          <Typography variant="h5" className="mb-4 text-primary-700">
            Stay Dates
          </Typography>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Typography variant="small" weight="medium" className="text-gray-700 mb-3">
                Check-in Date
              </Typography>
              <input
                type="date"
                value={formData.dates?.checkIn || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  dates: { ...prev.dates!, checkIn: e.target.value }
                }))}
                className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-primary-400 focus:ring-2 focus:ring-primary-100 transition-all duration-300 text-golden-base"
                required
              />
            </div>
            <div>
              <Typography variant="small" weight="medium" className="text-gray-700 mb-3">
                Check-out Date
              </Typography>
              <input
                type="date"
                value={formData.dates?.checkOut || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  dates: { ...prev.dates!, checkOut: e.target.value }
                }))}
                className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-primary-400 focus:ring-2 focus:ring-primary-100 transition-all duration-300 text-golden-base"
                required
              />
            </div>
          </div>
        </div>
      )}

      {type === 'tour' && (
        <div className="bg-gradient-to-r from-accent-50 to-warning-50 p-6 rounded-xl border border-accent-100">
          <Typography variant="h5" className="mb-4 text-accent-700">
            Tour Schedule
          </Typography>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Typography variant="small" weight="medium" className="text-gray-700 mb-3">
                Tour Date
              </Typography>
              <input
                type="date"
                value={formData.tourDate || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, tourDate: e.target.value }))}
                className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-accent-400 focus:ring-2 focus:ring-accent-100 transition-all duration-300 text-golden-base"
                required
              />
            </div>
            <div>
              <Typography variant="small" weight="medium" className="text-gray-700 mb-3">
                Tour Time
              </Typography>
              <select
                value={formData.tourTime || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, tourTime: e.target.value }))}
                className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-accent-400 focus:ring-2 focus:ring-accent-100 transition-all duration-300 text-golden-base"
                required
              >
                <option value="">Select time</option>
                <option value="08:00">8:00 AM</option>
                <option value="10:00">10:00 AM</option>
                <option value="14:00">2:00 PM</option>
                <option value="16:00">4:00 PM</option>
              </select>
            </div>
          </div>
        </div>
      )}

      <div className="bg-gradient-to-r from-secondary-50 to-primary-50 p-6 rounded-xl border border-secondary-100">
        <Typography variant="h5" className="mb-6 text-secondary-700">
          Guest Information
        </Typography>
        <div className="space-y-6">
          <div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-xs border border-gray-100">
            <div>
              <Typography variant="body" weight="medium" className="text-gray-900">
                Adults
              </Typography>
              <Typography variant="small" className="text-gray-600">
                Ages 13+
              </Typography>
            </div>
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={() => handleGuestChange('adults', formData.guests!.adults - 1)}
                className="w-10 h-10 rounded-full border-2 border-primary-200 flex items-center justify-center hover:border-primary-400 hover:bg-primary-50 transition-all duration-200 text-primary-600 font-semibold"
                disabled={formData.guests!.adults <= 1}
              >
                −
              </button>
              <Typography variant="body" weight="semibold" className="w-8 text-center text-gray-900">
                {formData.guests?.adults}
              </Typography>
              <button
                type="button"
                onClick={() => handleGuestChange('adults', formData.guests!.adults + 1)}
                className="w-10 h-10 rounded-full border-2 border-primary-200 flex items-center justify-center hover:border-primary-400 hover:bg-primary-50 transition-all duration-200 text-primary-600 font-semibold"
              >
                +
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-xs border border-gray-100">
            <div>
              <Typography variant="body" weight="medium" className="text-gray-900">
                Children
              </Typography>
              <Typography variant="small" className="text-gray-600">
                Ages 2-12
              </Typography>
            </div>
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={() => handleGuestChange('children', formData.guests!.children - 1)}
                className="w-10 h-10 rounded-full border-2 border-secondary-200 flex items-center justify-center hover:border-secondary-400 hover:bg-secondary-50 transition-all duration-200 text-secondary-600 font-semibold"
                disabled={formData.guests!.children <= 0}
              >
                −
              </button>
              <Typography variant="body" weight="semibold" className="w-8 text-center text-gray-900">
                {formData.guests?.children}
              </Typography>
              <button
                type="button"
                onClick={() => handleGuestChange('children', formData.guests!.children + 1)}
                className="w-10 h-10 rounded-full border-2 border-secondary-200 flex items-center justify-center hover:border-secondary-400 hover:bg-secondary-50 transition-all duration-200 text-secondary-600 font-semibold"
              >
                +
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-xs border border-gray-100">
            <div>
              <Typography variant="body" weight="medium" className="text-gray-900">
                Infants
              </Typography>
              <Typography variant="small" className="text-gray-600">
                Under 2 years
              </Typography>
            </div>
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={() => handleGuestChange('infants', formData.guests!.infants - 1)}
                className="w-10 h-10 rounded-full border-2 border-accent-200 flex items-center justify-center hover:border-accent-400 hover:bg-accent-50 transition-all duration-200 text-accent-600 font-semibold"
                disabled={formData.guests!.infants <= 0}
              >
                −
              </button>
              <Typography variant="body" weight="semibold" className="w-8 text-center text-gray-900">
                {formData.guests?.infants}
              </Typography>
              <button
                type="button"
                onClick={() => handleGuestChange('infants', formData.guests!.infants + 1)}
                className="w-10 h-10 rounded-full border-2 border-accent-200 flex items-center justify-center hover:border-accent-400 hover:bg-accent-50 transition-all duration-200 text-accent-600 font-semibold"
              >
                +
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="pt-6">
        <Button onClick={() => setStep(2)} size="lg" className="w-full">
          Continue to Contact Information
        </Button>
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-8">
      <Typography variant="h3" className="text-primary-600 mb-6">
        Contact Information
      </Typography>

      <div className="bg-gradient-to-r from-primary-50 to-secondary-50 p-6 rounded-xl border border-primary-100">
        <Typography variant="h5" className="mb-6 text-primary-700">
          Personal Details
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Typography variant="small" weight="medium" className="text-gray-700 mb-3">
              First Name *
            </Typography>
            <input
              type="text"
              value={formData.contact?.firstName || ''}
              onChange={(e) => handleContactChange('firstName', e.target.value)}
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-primary-400 focus:ring-2 focus:ring-primary-100 transition-all duration-300 text-golden-base"
              placeholder="Enter your first name"
              required
            />
          </div>
          <div>
            <Typography variant="small" weight="medium" className="text-gray-700 mb-3">
              Last Name *
            </Typography>
            <input
              type="text"
              value={formData.contact?.lastName || ''}
              onChange={(e) => handleContactChange('lastName', e.target.value)}
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-primary-400 focus:ring-2 focus:ring-primary-100 transition-all duration-300 text-golden-base"
              placeholder="Enter your last name"
              required
            />
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-r from-secondary-50 to-accent-50 p-6 rounded-xl border border-secondary-100">
        <Typography variant="h5" className="mb-6 text-secondary-700">
          Contact Details
        </Typography>
        <div className="space-y-6">
          <div>
            <Typography variant="small" weight="medium" className="text-gray-700 mb-3">
              Email Address *
            </Typography>
            <input
              type="email"
              value={formData.contact?.email || ''}
              onChange={(e) => handleContactChange('email', e.target.value)}
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-secondary-400 focus:ring-2 focus:ring-secondary-100 transition-all duration-300 text-golden-base"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div>
            <Typography variant="small" weight="medium" className="text-gray-700 mb-3">
              Phone Number *
            </Typography>
            <input
              type="tel"
              value={formData.contact?.phone || ''}
              onChange={(e) => handleContactChange('phone', e.target.value)}
              placeholder="+254 7XX XXX XXX"
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-secondary-400 focus:ring-2 focus:ring-secondary-100 transition-all duration-300 text-golden-base"
              required
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 pt-6">
        <Button variant="outline" onClick={() => setStep(1)} size="lg" className="sm:w-auto">
          ← Back to Details
        </Button>
        <Button onClick={() => setStep(3)} size="lg" className="flex-1">
          Continue to Services →
        </Button>
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-8">
      <Typography variant="h3" className="text-primary-600 mb-6">
        Additional Services
      </Typography>

      <div className="bg-gradient-to-r from-accent-50 to-warning-50 p-6 rounded-xl border border-accent-100">
        <Typography variant="h5" className="mb-6 text-accent-700">
          Enhance Your Experience
        </Typography>
        <div className="space-y-4">
          <label className="flex items-start p-4 bg-white rounded-lg border-2 border-gray-100 hover:border-accent-200 transition-all duration-200 cursor-pointer">
            <input
              type="checkbox"
              checked={formData.additionalServices?.airportTransfer || false}
              onChange={(e) => handleServiceChange('airportTransfer', e.target.checked)}
              className="mt-1 rounded border-gray-300 text-accent-600 focus:ring-accent-500 focus:ring-2"
            />
            <div className="ml-4">
              <Typography variant="body" weight="medium" className="text-gray-900">
                Airport Transfer
              </Typography>
              <Typography variant="small" className="text-gray-600">
                Convenient pickup and drop-off service
              </Typography>
              <Typography variant="small" weight="semibold" className="text-accent-600">
                +KES 2,500
              </Typography>
            </div>
          </label>

          <label className="flex items-start p-4 bg-white rounded-lg border-2 border-gray-100 hover:border-accent-200 transition-all duration-200 cursor-pointer">
            <input
              type="checkbox"
              checked={formData.additionalServices?.carRental || false}
              onChange={(e) => handleServiceChange('carRental', e.target.checked)}
              className="mt-1 rounded border-gray-300 text-accent-600 focus:ring-accent-500 focus:ring-2"
            />
            <div className="ml-4">
              <Typography variant="body" weight="medium" className="text-gray-900">
                Car Rental
              </Typography>
              <Typography variant="small" className="text-gray-600">
                Explore at your own pace with a rental car
              </Typography>
              <Typography variant="small" weight="semibold" className="text-accent-600">
                +KES 5,000/day
              </Typography>
            </div>
          </label>

          <label className="flex items-start p-4 bg-white rounded-lg border-2 border-gray-100 hover:border-accent-200 transition-all duration-200 cursor-pointer">
            <input
              type="checkbox"
              checked={formData.additionalServices?.insurance || false}
              onChange={(e) => handleServiceChange('insurance', e.target.checked)}
              className="mt-1 rounded border-gray-300 text-accent-600 focus:ring-accent-500 focus:ring-2"
            />
            <div className="ml-4">
              <Typography variant="body" weight="medium" className="text-gray-900">
                Travel Insurance
              </Typography>
              <Typography variant="small" className="text-gray-600">
                Comprehensive coverage for peace of mind
              </Typography>
              <Typography variant="small" weight="semibold" className="text-accent-600">
                +5% of booking total
              </Typography>
            </div>
          </label>
        </div>
      </div>

      <div className="bg-gradient-to-r from-primary-50 to-secondary-50 p-6 rounded-xl border border-primary-100">
        <Typography variant="h5" className="mb-4 text-primary-700">
          Special Requests
        </Typography>
        <Typography variant="small" className="text-gray-600 mb-4">
          Let us know if you have any special requirements or requests for your stay/tour.
        </Typography>
        <textarea
          value={formData.specialRequests || ''}
          onChange={(e) => setFormData(prev => ({ ...prev, specialRequests: e.target.value }))}
          rows={4}
          className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-primary-400 focus:ring-2 focus:ring-primary-100 transition-all duration-300 text-golden-base resize-none"
          placeholder="Any special requests, dietary requirements, accessibility needs, or other preferences..."
        />
      </div>

      <div className="flex flex-col sm:flex-row gap-4 pt-6">
        <Button variant="outline" onClick={() => setStep(2)} size="lg" className="sm:w-auto">
          ← Back to Contact
        </Button>
        <Button onClick={handleSubmit} disabled={loading} size="lg" className="flex-1">
          {loading ? (
            <div className="flex items-center justify-center">
              <LoadingSpinner size="sm" />
              <span className="ml-2">Creating Booking...</span>
            </div>
          ) : (
            'Complete Booking →'
          )}
        </Button>
      </div>
    </div>
  )

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-2xl shadow-xl border border-gray-100">
      {/* Progress indicator */}
      <div className="mb-10">
        <div className="flex items-center justify-between relative">
          {/* Progress line */}
          <div className="absolute top-1/2 left-0 right-0 h-1 bg-gray-200 rounded-full -translate-y-1/2 -z-10">
            <div
              className="h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full transition-all duration-500"
              style={{ width: `${((step - 1) / 2) * 100}%` }}
            />
          </div>

          {[
            { number: 1, title: 'Details', icon: '📅' },
            { number: 2, title: 'Contact', icon: '👤' },
            { number: 3, title: 'Services', icon: '✨' }
          ].map((stepInfo) => (
            <div key={stepInfo.number} className="flex flex-col items-center">
              <div
                className={`flex items-center justify-center w-12 h-12 rounded-full border-4 transition-all duration-300 ${
                  step >= stepInfo.number
                    ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white border-primary-300 shadow-lg'
                    : 'bg-white text-gray-400 border-gray-200'
                }`}
              >
                <span className="text-lg">{step >= stepInfo.number ? '✓' : stepInfo.icon}</span>
              </div>
              <Typography
                variant="small"
                weight="medium"
                className={`mt-2 ${step >= stepInfo.number ? 'text-primary-600' : 'text-gray-400'}`}
              >
                {stepInfo.title}
              </Typography>
            </div>
          ))}
        </div>
        <div className="mt-6 text-center">
          <Typography variant="body" className="text-gray-600">
            Step {step} of 3: Complete your booking details
          </Typography>
        </div>
      </div>

      {/* Form content */}
      {step === 1 && renderStep1()}
      {step === 2 && renderStep2()}
      {step === 3 && renderStep3()}
    </div>
  )
}
