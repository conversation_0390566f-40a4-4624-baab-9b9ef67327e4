'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatCurrency } from '@/lib/formatters'

interface PaymentFormProps {
  bookingId: string
  amount: number
  bookingNumber: string
  onPaymentSuccess?: () => void
  onPaymentError?: (error: string) => void
}

export function PaymentForm({ 
  bookingId, 
  amount, 
  bookingNumber,
  onPaymentSuccess,
  onPaymentError 
}: PaymentFormProps) {
  const [phoneNumber, setPhoneNumber] = useState('')
  const [loading, setLoading] = useState(false)
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle')
  const [statusMessage, setStatusMessage] = useState('')

  const formatPhoneNumber = (phone: string) => {
    // Remove all non-digits
    const digits = phone.replace(/\D/g, '')
    
    // Format as Kenyan number
    if (digits.startsWith('0')) {
      return '+254' + digits.substring(1)
    } else if (digits.startsWith('254')) {
      return '+' + digits
    } else if (digits.startsWith('7') || digits.startsWith('1')) {
      return '+254' + digits
    }
    return phone
  }

  const handlePayment = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!phoneNumber) {
      alert('Please enter your phone number')
      return
    }

    setLoading(true)
    setPaymentStatus('processing')
    setStatusMessage('Initiating payment...')

    try {
      const response = await fetch('/api/payments/mpesa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookingId,
          phoneNumber: formatPhoneNumber(phoneNumber)
        }),
      })

      const result = await response.json()

      if (result.success) {
        setStatusMessage('Payment request sent to your phone. Please check your M-Pesa and enter your PIN.')
        
        // Poll for payment status
        pollPaymentStatus(result.checkoutRequestId)
      } else {
        setPaymentStatus('error')
        setStatusMessage(result.error || 'Failed to initiate payment')
        onPaymentError?.(result.error || 'Payment failed')
      }
    } catch (error) {
      console.error('Payment error:', error)
      setPaymentStatus('error')
      setStatusMessage('Failed to process payment. Please try again.')
      onPaymentError?.('Payment processing failed')
    } finally {
      setLoading(false)
    }
  }

  const pollPaymentStatus = async (checkoutRequestId: string) => {
    // In a real implementation, you would poll the payment status
    // For now, we'll simulate the process
    let attempts = 0
    const maxAttempts = 30 // 5 minutes with 10-second intervals

    const poll = async () => {
      attempts++
      
      if (attempts > maxAttempts) {
        setPaymentStatus('error')
        setStatusMessage('Payment timeout. Please try again or contact support.')
        return
      }

      try {
        // In production, you would check the actual payment status
        // For demo purposes, we'll simulate success after 30 seconds
        if (attempts >= 3) {
          setPaymentStatus('success')
          setStatusMessage('Payment successful! Your booking is confirmed.')
          onPaymentSuccess?.()
          return
        }

        setStatusMessage(`Waiting for payment confirmation... (${attempts}/${maxAttempts})`)
        setTimeout(poll, 10000) // Check every 10 seconds
      } catch (error) {
        console.error('Status check error:', error)
        setTimeout(poll, 10000)
      }
    }

    setTimeout(poll, 5000) // Start checking after 5 seconds
  }

  // Using safe formatters from lib/formatters.ts to prevent hydration mismatches

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-xs">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold text-gray-900">Complete Payment</h3>
        <p className="text-gray-600 mt-2">Booking: {bookingNumber}</p>
        <p className="text-2xl font-bold text-blue-600 mt-2">{formatCurrency(amount)}</p>
      </div>

      {paymentStatus === 'idle' && (
        <form onSubmit={handlePayment} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              M-Pesa Phone Number
            </label>
            <input
              type="tel"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              placeholder="0712 345 678"
              className="w-full rounded-md border-gray-300 shadow-xs focus:border-blue-500 focus:ring-3 focus:ring-blue-500"
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              Enter the phone number registered with M-Pesa
            </p>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">How it works:</h4>
            <ol className="text-sm text-blue-800 space-y-1">
              <li>1. Click "Pay with M-Pesa" below</li>
              <li>2. You'll receive an M-Pesa prompt on your phone</li>
              <li>3. Enter your M-Pesa PIN to complete payment</li>
              <li>4. You'll receive a confirmation SMS</li>
            </ol>
          </div>

          <Button 
            type="submit" 
            disabled={loading || !phoneNumber}
            className="w-full bg-green-600 hover:bg-green-700"
          >
            {loading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Processing...
              </>
            ) : (
              'Pay with M-Pesa'
            )}
          </Button>
        </form>
      )}

      {paymentStatus === 'processing' && (
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <LoadingSpinner size="lg" />
          </div>
          <div>
            <h4 className="font-medium text-gray-900">Processing Payment</h4>
            <p className="text-sm text-gray-600 mt-2">{statusMessage}</p>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg">
            <p className="text-sm text-yellow-800">
              <strong>Check your phone:</strong> You should receive an M-Pesa payment request. 
              Enter your PIN to complete the transaction.
            </p>
          </div>
        </div>
      )}

      {paymentStatus === 'success' && (
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div>
            <h4 className="font-medium text-green-900">Payment Successful!</h4>
            <p className="text-sm text-green-700 mt-2">{statusMessage}</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-sm text-green-800">
              You will receive a confirmation email shortly with your booking details.
            </p>
          </div>
        </div>
      )}

      {paymentStatus === 'error' && (
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <div>
            <h4 className="font-medium text-red-900">Payment Failed</h4>
            <p className="text-sm text-red-700 mt-2">{statusMessage}</p>
          </div>
          <Button 
            onClick={() => {
              setPaymentStatus('idle')
              setStatusMessage('')
            }}
            variant="outline"
            className="w-full"
          >
            Try Again
          </Button>
        </div>
      )}

      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          <span>Secure payment powered by M-Pesa</span>
        </div>
      </div>
    </div>
  )
}
