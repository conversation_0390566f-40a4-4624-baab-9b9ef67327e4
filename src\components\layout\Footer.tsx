'use client'

import { Settings } from '@/types/settings'
import Link from 'next/link'
import { FooterLogo } from '@/components/ui/Logo'

interface FooterProps {
  settings: Settings | null
}

export function Footer({ settings }: FooterProps) {
  // Fallback navigation and data
  const defaultNavigation = [
    { title: 'Home', url: '/' },
    { title: 'Search', url: '/search' },
    { title: 'Rent', url: '/properties?type=rental' },
    { title: 'Buy', url: '/properties?type=sale' },
    { title: 'Airbnb', url: '/properties?type=airbnb' },
    { title: 'Tours', url: '/tours' },
    { title: 'Blog', url: '/blog' },
    { title: 'List Property', url: '/list-your-property' }
  ]

  const defaultFooterNavigation = [
    { title: 'Privacy Policy', url: '/privacy' },
    { title: 'Terms of Service', url: '/terms' },
    { title: 'Help Center', url: '/help' },
    { title: 'About Us', url: '/about' }
  ]

  const defaultContactInfo = {
    email: '<EMAIL>',
    phone: '+254 700 000 000',
    address: 'Nairobi, Kenya'
  }

  // Use CMS data if available, otherwise use defaults
  const navigation = settings?.mainNavigation && settings.mainNavigation.length > 0 ? settings.mainNavigation : defaultNavigation
  const footerNavigation = settings?.footerNavigation && settings.footerNavigation.length > 0 ? settings.footerNavigation : defaultFooterNavigation
  const siteTitle = settings?.title || 'RiftStays'
  const description = settings?.description || 'Find your perfect home in Kenya. Rent apartments, buy houses, or list your property on Kenya\'s leading real estate platform.'
  const contactInfo = settings?.contactInfo || defaultContactInfo
  const copyright = settings?.copyright || `© ${new Date().getFullYear()} RiftStays. All rights reserved.`
  return (
    <footer className="bg-gradient-to-br from-gray-900 via-primary-900 to-secondary-900 text-white relative overflow-hidden">
      <div className="absolute inset-0 bg-hero-pattern opacity-10"></div>
      <div className="relative mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          <div className="space-y-6 md:col-span-2">
            <FooterLogo
              siteTitle={siteTitle}
              variant="white"
              className="mb-2"
            />
            <p className="text-gray-300 text-lg leading-relaxed max-w-md">{description}</p>
          </div>

          <div>
            <h3 className="text-sm font-semibold uppercase tracking-wider text-primary-400 mb-6">
              Navigation
            </h3>
            <ul className="space-y-3">
              {navigation.map((link) => (
                <li key={link.url}>
                  <Link
                    href={link.url}
                    className="text-base text-gray-300 hover:text-primary-400 transition-colors duration-300 hover:translate-x-1 transform inline-block"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-6">
              Support
            </h3>
            <ul className="space-y-3">
              {footerNavigation.map((link) => (
                <li key={link.url}>
                  <Link
                    href={link.url}
                    className="text-base text-gray-300 hover:text-primary-400 transition-colors duration-300 hover:translate-x-1 transform inline-block"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-6">
              Contact
            </h3>
            <ul className="space-y-3">
              <li>
                <a
                  href={`mailto:${contactInfo.email}`}
                  className="text-base text-gray-300 hover:text-primary-400 transition-colors duration-300"
                >
                  {contactInfo.email}
                </a>
              </li>
              <li>
                <a
                  href={`tel:${contactInfo.phone}`}
                  className="text-base text-gray-300 hover:text-primary-400 transition-colors duration-300"
                >
                  {contactInfo.phone}
                </a>
              </li>
              <li className="text-base text-gray-300">
                {contactInfo.address}
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 border-t border-gray-800 pt-8">
          <p className="text-base text-gray-400">{copyright}</p>
        </div>
      </div>
    </footer>
  )
} 