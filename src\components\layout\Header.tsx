'use client'

import { useState } from 'react'
import { Settings } from '@/types/settings'
import Link from 'next/link'
import { HeaderLogo } from '@/components/ui/Logo'
import { ClientOnly } from '@/components/ClientOnly'
import { NavigationContent } from './NavigationContent'
import { MobileNavigationContent } from './MobileNavigationContent'

interface HeaderProps {
  settings: Settings | null
}

export function Header({ settings }: HeaderProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  // Mock user state - in a real app, this would come from auth context
  const [user, setUser] = useState<any>(null) // Replace with actual user type

  const handleLogout = () => {
    setUser(null)
    // Add actual logout logic here
  }

  // Fallback navigation structure if CMS data is not available
  const defaultNavigation = [
    { title: 'Home', url: '/' },
    { title: 'Search', url: '/search' },
    { title: 'Rent', url: '/properties?type=rental' },
    { title: 'Buy', url: '/properties?type=sale' },
    { title: 'Airbnb', url: '/properties?type=airbnb' },
    { title: 'Tours', url: '/tours' },
    { title: 'Blog', url: '/blog' }
  ]

  // Use CMS navigation if available, otherwise use default
  const navigation = settings?.mainNavigation && settings.mainNavigation.length > 0 ? settings.mainNavigation : defaultNavigation
  const siteTitle = settings?.title || 'RiftStays'

  return (
    <header className="bg-white/95 backdrop-blur-md shadow-lg sticky top-0 z-50 border-b border-primary-100">
      <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8" aria-label="Top">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            <Link href="/" className="group">
              <HeaderLogo siteTitle={siteTitle} className="group-hover:scale-105 transition-transform duration-300" />
            </Link>
          </div>

          <ClientOnly fallback={<div className="hidden md:flex md:items-center md:space-x-6 h-10" />}>
            <NavigationContent
              navigation={navigation}
              user={user}
              onLogout={handleLogout}
            />
          </ClientOnly>

          {/* Mobile menu button */}
          <div className="flex md:hidden">
            <button
              type="button"
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
              aria-controls="mobile-menu"
              aria-expanded="false"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <span className="sr-only">Open main menu</span>
              {!mobileMenuOpen ? (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile menu dropdown */}
      <ClientOnly>
        <MobileNavigationContent
          navigation={navigation}
          isOpen={mobileMenuOpen}
          onClose={() => setMobileMenuOpen(false)}
        />
      </ClientOnly>
    </header>
  )
}