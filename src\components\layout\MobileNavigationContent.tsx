'use client'

import { usePathname, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'

interface NavigationItem {
  title: string
  url: string
}

interface MobileNavigationContentProps {
  navigation: NavigationItem[]
  isOpen: boolean
  onClose: () => void
}

export function MobileNavigationContent({ navigation, isOpen, onClose }: MobileNavigationContentProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // Function to determine if a navigation item is active
  const isActiveLink = (url: string) => {
    // Handle exact matches for simple paths
    if (url === pathname) {
      return true
    }

    // Handle query parameter based routes
    if (url.includes('?')) {
      const [path, queryString] = url.split('?')
      if (path === pathname) {
        const urlParams = new URLSearchParams(queryString)
        const currentType = searchParams.get('type')
        const linkType = urlParams.get('type')
        return currentType === linkType
      }
    }

    // Handle blog routes (including individual blog posts)
    if (url === '/blog' && pathname.startsWith('/blog')) {
      return true
    }

    // Handle property listing routes
    if (url === '/properties' && pathname.startsWith('/properties')) {
      return true
    }

    // Handle tours routes (including individual tours)
    if (url === '/tours' && pathname.startsWith('/tours')) {
      return true
    }

    // Handle profile routes
    if (url === '/profile' && pathname.startsWith('/profile')) {
      return true
    }

    // Handle admin routes
    if (url === '/admin' && pathname.startsWith('/admin')) {
      return true
    }

    return false
  }

  if (!isOpen) return null

  return (
    <div className="md:hidden">
      <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-b border-gray-200 shadow-lg">
        {navigation.map((link) => {
          const isActive = isActiveLink(link.url)
          return (
            <Link
              key={link.url}
              href={link.url}
              className={`block px-3 py-2 rounded-md text-base font-medium nav-text text-crisp transition-all duration-300 ${
                isActive
                  ? 'text-primary-600 bg-primary-50 text-pop'
                  : 'text-gray-900 hover:text-primary-600 hover:bg-gray-50 hover:text-pop'
              }`}
              onClick={onClose}
            >
              {link.title}
            </Link>
          )
        })}
        <Link
          href="/list-your-property"
          className="block px-3 py-2 mt-4"
          onClick={onClose}
        >
          <Button
            variant="primary"
            className={`w-full justify-center font-semibold btn-text text-crisp px-6 py-2 rounded-full shadow-lg transition-all duration-300 ${
              isActiveLink('/list-your-property')
                ? 'bg-gradient-to-r from-primary-600 to-accent-600 text-white ring-2 ring-primary-300 text-glow'
                : 'bg-gradient-to-r from-primary-500 to-accent-500 hover:from-primary-600 hover:to-accent-600 text-white hover:text-glow'
            }`}
          >
            List Property
          </Button>
        </Link>
      </div>
    </div>
  )
}
