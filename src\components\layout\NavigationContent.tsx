'use client'

import { usePathname, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { UserNavigation } from '@/components/navigation/UserNavigation'

interface NavigationItem {
  title: string
  url: string
}

interface NavigationContentProps {
  navigation: NavigationItem[]
  user: any
  onLogout: () => void
}

export function NavigationContent({ navigation, user, onLogout }: NavigationContentProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // Function to determine if a navigation item is active
  const isActiveLink = (url: string) => {
    // Handle exact matches for simple paths
    if (url === pathname) {
      return true
    }

    // Handle query parameter based routes
    if (url.includes('?')) {
      const [path, queryString] = url.split('?')
      if (path === pathname) {
        const urlParams = new URLSearchParams(queryString)
        const currentType = searchParams.get('type')
        const linkType = urlParams.get('type')
        return currentType === linkType
      }
    }

    // Handle blog routes (including individual blog posts)
    if (url === '/blog' && pathname.startsWith('/blog')) {
      return true
    }

    // Handle property listing routes
    if (url === '/properties' && pathname.startsWith('/properties')) {
      return true
    }

    // Handle tours routes (including individual tours)
    if (url === '/tours' && pathname.startsWith('/tours')) {
      return true
    }

    // Handle profile routes
    if (url === '/profile' && pathname.startsWith('/profile')) {
      return true
    }

    // Handle admin routes
    if (url === '/admin' && pathname.startsWith('/admin')) {
      return true
    }

    return false
  }

  return (
    <div className="hidden md:flex md:items-center md:space-x-6">
      {navigation.map((link) => {
        const isActive = isActiveLink(link.url)
        return (
          <Link
            key={link.url}
            href={link.url}
            className={`text-golden-base font-medium nav-text text-crisp transition-colors duration-300 relative group min-h-[44px] min-w-[44px] flex items-center ${
              isActive
                ? 'text-primary-600 text-pop'
                : 'text-gray-900 hover:text-primary-600 hover:text-pop'
            }`}
          >
            {link.title}
            <span className={`absolute -bottom-1 left-0 h-0.5 bg-gradient-to-r from-primary-500 to-secondary-500 transition-all duration-300 ${
              isActive
                ? 'w-full'
                : 'w-0 group-hover:w-full'
            }`}></span>
          </Link>
        )
      })}

      <Link href="/list-your-property">
        <Button
          variant="primary"
          className={`font-semibold btn-text text-crisp px-6 py-2 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ${
            isActiveLink('/list-your-property')
              ? 'bg-gradient-to-r from-primary-600 to-accent-600 text-white ring-2 ring-primary-300 text-glow'
              : 'bg-gradient-to-r from-primary-500 to-accent-500 hover:from-primary-600 hover:to-accent-600 text-white hover:text-glow'
          }`}
        >
          List Property
        </Button>
      </Link>

      {/* User Navigation */}
      <UserNavigation
        user={user}
        onLogout={onLogout}
        className="ml-4"
      />
    </div>
  )
}
