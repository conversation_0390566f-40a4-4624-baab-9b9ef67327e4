'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline'

interface BreadcrumbItem {
  label: string
  href: string
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[]
  className?: string
}

export function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  const pathname = usePathname()

  // Generate breadcrumb items from pathname if not provided
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = pathname.split('/').filter(Boolean)
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Home', href: '/' }
    ]

    let currentPath = ''
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`
      
      // Convert segment to readable label
      let label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')

      // Special cases for better labels
      switch (segment) {
        case 'houses-to-let':
          label = 'Houses to Let'
          break
        case 'list-your-property':
          label = 'List Property'
          break
        case 'properties':
          label = 'Properties'
          break
        case 'tours':
          label = 'Tours'
          break
        case 'blog':
          label = 'Blog'
          break
        case 'admin':
          label = 'Admin'
          break
        case 'profile':
          label = 'Profile'
          break
        case 'search':
          label = 'Search'
          break
        case 'contact':
          label = 'Contact'
          break
        case 'about':
          label = 'About Us'
          break
        case 'help':
          label = 'Help Center'
          break
        case 'privacy':
          label = 'Privacy Policy'
          break
        case 'terms':
          label = 'Terms of Service'
          break
      }

      breadcrumbs.push({
        label,
        href: currentPath
      })
    })

    return breadcrumbs
  }

  const breadcrumbItems = items || generateBreadcrumbs()

  // Don't show breadcrumbs on home page
  if (pathname === '/') {
    return null
  }

  return (
    <nav className={`flex ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {breadcrumbItems.map((item, index) => (
          <li key={item.href} className="flex items-center">
            {index > 0 && (
              <ChevronRightIcon className="h-4 w-4 text-gray-400 mx-2" />
            )}
            
            {index === 0 ? (
              <Link
                href={item.href}
                className="text-gray-500 hover:text-primary-600 transition-colors duration-200 flex items-center"
              >
                <HomeIcon className="h-4 w-4" />
                <span className="sr-only">{item.label}</span>
              </Link>
            ) : index === breadcrumbItems.length - 1 ? (
              <span className="text-gray-900 font-medium" aria-current="page">
                {item.label}
              </span>
            ) : (
              <Link
                href={item.href}
                className="text-gray-500 hover:text-primary-600 transition-colors duration-200 font-medium"
              >
                {item.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

// Utility function to create custom breadcrumb items
export function createBreadcrumbItems(items: Array<{ label: string; href: string }>): BreadcrumbItem[] {
  return [
    { label: 'Home', href: '/' },
    ...items
  ]
}
