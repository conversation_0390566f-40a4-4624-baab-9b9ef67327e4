'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  UserIcon, 
  Cog6ToothIcon, 
  HeartIcon, 
  CalendarIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  BellIcon
} from '@heroicons/react/24/outline'

interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: 'user' | 'admin' | 'owner'
}

interface UserNavigationProps {
  user: User | null
  onLogout: () => void
  className?: string
}

export function UserNavigation({ user, onLogout, className = '' }: UserNavigationProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const pathname = usePathname()

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Close dropdown when route changes
  useEffect(() => {
    setIsOpen(false)
  }, [pathname])

  if (!user) {
    return (
      <div className={`flex items-center space-x-4 ${className}`}>
        <Link
          href="/auth/login"
          className="text-gray-700 hover:text-primary-600 font-medium nav-text text-crisp transition-all duration-200 hover:text-pop transform hover:scale-105"
        >
          Sign In
        </Link>
        <Link
          href="/auth/register"
          className="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-full font-medium btn-text text-crisp transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg hover:text-glow"
        >
          Sign Up
        </Link>
      </div>
    )
  }

  const userMenuItems = [
    {
      label: 'Profile',
      href: '/profile',
      icon: UserIcon,
      description: 'View and edit your profile'
    },
    {
      label: 'My Bookings',
      href: '/profile/bookings',
      icon: CalendarIcon,
      description: 'Manage your bookings'
    },
    {
      label: 'Favorites',
      href: '/profile/favorites',
      icon: HeartIcon,
      description: 'Your saved properties'
    },
    {
      label: 'Settings',
      href: '/profile/settings',
      icon: Cog6ToothIcon,
      description: 'Account settings'
    }
  ]

  // Add admin link for admin users
  if (user.role === 'admin') {
    userMenuItems.unshift({
      label: 'Admin Dashboard',
      href: '/admin',
      icon: Cog6ToothIcon,
      description: 'Manage the platform'
    })
  }

  return (
    <div className={`relative ${className}`}>
      <div className="flex items-center space-x-4">
        {/* Notifications */}
        <button className="relative p-2 text-gray-500 hover:text-primary-600 transition-colors duration-200">
          <BellIcon className="h-6 w-6" />
          {/* Notification badge */}
          <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
        </button>

        {/* User Menu */}
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="flex items-center space-x-2 p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
          >
            {user.avatar ? (
              <img
                src={user.avatar}
                alt={user.name}
                className="h-8 w-8 rounded-full object-cover"
              />
            ) : (
              <div className="h-8 w-8 bg-primary-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
            <span className="hidden md:block text-gray-700 font-medium nav-text text-crisp">
              {user.name}
            </span>
            <ChevronDownIcon className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
          </button>

          {/* Dropdown Menu */}
          {isOpen && (
            <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
              {/* User Info */}
              <div className="px-4 py-3 border-b border-gray-100">
                <p className="text-sm font-medium text-gray-900">{user.name}</p>
                <p className="text-sm text-gray-500">{user.email}</p>
              </div>

              {/* Menu Items */}
              <div className="py-2">
                {userMenuItems.map((item) => {
                  const Icon = item.icon
                  const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
                  
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={`flex items-center px-4 py-3 text-sm hover:bg-gray-50 transition-all duration-200 text-crisp ${
                        isActive ? 'text-primary-600 bg-primary-50 text-pop' : 'text-gray-700 hover:text-pop'
                      }`}
                    >
                      <Icon className="h-5 w-5 mr-3" />
                      <div>
                        <div className="font-medium">{item.label}</div>
                        <div className="text-xs text-gray-500">{item.description}</div>
                      </div>
                    </Link>
                  )
                })}
              </div>

              {/* Logout */}
              <div className="border-t border-gray-100 pt-2">
                <button
                  onClick={onLogout}
                  className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                >
                  <ArrowRightOnRectangleIcon className="h-5 w-5 mr-3" />
                  <div>
                    <div className="font-medium">Sign Out</div>
                    <div className="text-xs text-gray-500">Sign out of your account</div>
                  </div>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
