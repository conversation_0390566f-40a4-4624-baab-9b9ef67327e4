'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/Button'

export function OfflineActions() {
  const handleTryAgain = () => {
    if (typeof window !== 'undefined') {
      window.location.reload()
    }
  }

  return (
    <div className="space-y-4">
      <Link href="/" className="w-full">
        <Button className="w-full">
          Return to Homepage
        </Button>
      </Link>
      
      <button
        onClick={handleTryAgain}
        className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
      >
        Try Again
      </button>
    </div>
  )
}
