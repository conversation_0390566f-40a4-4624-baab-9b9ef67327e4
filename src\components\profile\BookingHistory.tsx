'use client'

import { useState, useEffect } from 'react'
import {
  CalendarDaysIcon,
  MapPinIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  EyeIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'
import { formatCurrency, formatDate } from '@/lib/formatters'

interface Booking {
  _id: string
  bookingNumber: string
  type: 'property' | 'tour'
  title: string
  location: string
  checkIn: string
  checkOut?: string
  guests: {
    adults: number
    children: number
    infants: number
  }
  totalPrice: number
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed'
  paymentStatus: 'paid' | 'pending' | 'failed'
  createdAt: string
  image?: string
  contact: {
    firstName: string
    lastName: string
    email: string
    phone: string
  }
}

interface BookingHistoryProps {
  userId?: string
}

export function BookingHistory({ userId }: BookingHistoryProps) {
  const [bookings, setBookings] = useState<Booking[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'upcoming' | 'completed' | 'cancelled'>('all')

  useEffect(() => {
    fetchBookings()
  }, [userId])

  const fetchBookings = async () => {
    try {
      // Mock bookings data - replace with actual API call
      const mockBookings: Booking[] = [
        {
          _id: '1',
          bookingNumber: 'RS-2024-001',
          type: 'property',
          title: 'Luxury Apartment - Kilimani',
          location: 'Kilimani, Nairobi',
          checkIn: '2024-12-25T00:00:00Z',
          checkOut: '2024-12-30T00:00:00Z',
          guests: { adults: 2, children: 1, infants: 0 },
          totalPrice: 425000,
          status: 'confirmed',
          paymentStatus: 'paid',
          createdAt: '2024-12-15T10:30:00Z',
          contact: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '+254700000000'
          }
        },
        {
          _id: '2',
          bookingNumber: 'RS-2024-002',
          type: 'tour',
          title: 'Maasai Mara Safari Experience',
          location: 'Maasai Mara, Kenya',
          checkIn: '2024-12-20T06:00:00Z',
          checkOut: '2024-12-22T18:00:00Z',
          guests: { adults: 4, children: 2, infants: 0 },
          totalPrice: 180000,
          status: 'completed',
          paymentStatus: 'paid',
          createdAt: '2024-11-28T14:20:00Z',
          contact: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '+254700000000'
          }
        },
        {
          _id: '3',
          bookingNumber: 'RS-2024-003',
          type: 'property',
          title: 'Beachfront Villa - Diani',
          location: 'Diani Beach, Mombasa',
          checkIn: '2025-01-15T00:00:00Z',
          checkOut: '2025-01-20T00:00:00Z',
          guests: { adults: 6, children: 0, infants: 0 },
          totalPrice: 750000,
          status: 'pending',
          paymentStatus: 'pending',
          createdAt: '2024-12-18T09:15:00Z',
          contact: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '+254700000000'
          }
        }
      ]
      setBookings(mockBookings)
    } catch (error) {
      console.error('Failed to fetch bookings:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircleIcon className="h-4 w-4" />
      case 'pending':
        return <ClockIcon className="h-4 w-4" />
      case 'cancelled':
        return <XCircleIcon className="h-4 w-4" />
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4" />
      default:
        return <ClockIcon className="h-4 w-4" />
    }
  }

  const filteredBookings = bookings.filter(booking => {
    if (filter === 'all') return true
    if (filter === 'upcoming') return booking.status === 'confirmed' && new Date(booking.checkIn) > new Date()
    if (filter === 'completed') return booking.status === 'completed'
    if (filter === 'cancelled') return booking.status === 'cancelled'
    return true
  })

  // Using safe formatters from lib/formatters.ts to prevent hydration mismatches

  const getTotalGuests = (guests: Booking['guests']) => {
    return guests.adults + guests.children + guests.infants
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
        ))}
      </div>
    )
  }

  return (
    <div>
      {/* Filter Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { key: 'all', label: 'All Bookings' },
            { key: 'upcoming', label: 'Upcoming' },
            { key: 'completed', label: 'Completed' },
            { key: 'cancelled', label: 'Cancelled' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                filter === tab.key
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Bookings List */}
      {filteredBookings.length === 0 ? (
        <div className="text-center py-12">
          <CalendarDaysIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No bookings found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {filter === 'all' ? 'You haven\'t made any bookings yet.' : `No ${filter} bookings found.`}
          </p>
          <div className="mt-6">
            <Link
              href="/search"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              Start Searching
            </Link>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {filteredBookings.map((booking) => (
            <div key={booking._id} className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{booking.title}</h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                        {getStatusIcon(booking.status)}
                        <span className="ml-1 capitalize">{booking.status}</span>
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <MapPinIcon className="h-4 w-4 mr-2 text-gray-400" />
                        {booking.location}
                      </div>
                      
                      <div className="flex items-center">
                        <CalendarDaysIcon className="h-4 w-4 mr-2 text-gray-400" />
                        {formatDate(booking.checkIn)}
                        {booking.checkOut && ` - ${formatDate(booking.checkOut)}`}
                      </div>
                      
                      <div className="flex items-center">
                        <UserGroupIcon className="h-4 w-4 mr-2 text-gray-400" />
                        {getTotalGuests(booking.guests)} guests
                      </div>
                      
                      <div className="flex items-center">
                        <CurrencyDollarIcon className="h-4 w-4 mr-2 text-gray-400" />
                        {formatCurrency(booking.totalPrice)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      <EyeIcon className="h-4 w-4 mr-2" />
                      View
                    </button>
                    
                    <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                      Receipt
                    </button>
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Booking #{booking.bookingNumber}</span>
                    <span className="text-gray-500">
                      Booked on {formatDate(booking.createdAt)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
