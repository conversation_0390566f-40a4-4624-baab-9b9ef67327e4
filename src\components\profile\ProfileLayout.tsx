'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import Link from 'next/link'
import { User } from '@/types/auth'
import { 
  UserIcon, 
  BookmarkIcon, 
  CalendarDaysIcon, 
  CogIcon,
  DocumentTextIcon,
  HeartIcon,
  BellIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline'

interface ProfileLayoutProps {
  children: React.ReactNode
  user?: User | null
}

export function ProfileLayout({ children, user }: ProfileLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [loading, setLoading] = useState(!user)

  useEffect(() => {
    // Check if user is authenticated
    if (!user && !loading) {
      router.push('/auth/login?redirect=' + encodeURIComponent(pathname))
    }
  }, [user, loading, router, pathname])

  const navigation = [
    {
      name: 'Dashboard',
      href: '/profile',
      icon: UserIcon,
      description: 'Overview and quick actions'
    },
    {
      name: 'My Bookings',
      href: '/profile/bookings',
      icon: CalendarDaysIcon,
      description: 'View and manage your bookings'
    },
    {
      name: 'Favorites',
      href: '/profile/favorites',
      icon: HeartIcon,
      description: 'Saved properties and tours'
    },
    {
      name: 'Saved Searches',
      href: '/profile/searches',
      icon: BookmarkIcon,
      description: 'Your saved search criteria'
    },
    {
      name: 'Documents',
      href: '/profile/documents',
      icon: DocumentTextIcon,
      description: 'Identity verification documents'
    },
    {
      name: 'Notifications',
      href: '/profile/notifications',
      icon: BellIcon,
      description: 'Alerts and updates'
    },
    {
      name: 'Settings',
      href: '/profile/settings',
      icon: CogIcon,
      description: 'Account and privacy settings'
    }
  ]

  const handleLogout = () => {
    // Clear user session
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')
    router.push('/')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="lg:grid lg:grid-cols-12 lg:gap-x-8">
          {/* Sidebar */}
          <aside className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              {/* User Info */}
              <div className="p-6 bg-gradient-to-r from-primary-500 to-secondary-500 text-white">
                <div className="flex items-center">
                  {user.avatar?.asset?.url ? (
                    <img
                      className="h-16 w-16 rounded-full border-2 border-white"
                      src={user.avatar.asset.url}
                      alt={`${user.firstName} ${user.lastName}`}
                    />
                  ) : (
                    <div className="h-16 w-16 rounded-full bg-white/20 flex items-center justify-center border-2 border-white">
                      <UserIcon className="h-8 w-8 text-white" />
                    </div>
                  )}
                  <div className="ml-4">
                    <h2 className="text-lg font-semibold">
                      {user.firstName} {user.lastName}
                    </h2>
                    <p className="text-sm text-white/80">{user.email}</p>
                    <p className="text-xs text-white/60 capitalize">{user.role} Account</p>
                  </div>
                </div>
              </div>

              {/* Navigation */}
              <nav className="p-2">
                {navigation.map((item) => {
                  const isActive = pathname === item.href
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`group flex items-center px-3 py-3 text-sm font-medium rounded-md transition-colors duration-200 ${
                        isActive
                          ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-500'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <item.icon
                        className={`flex-shrink-0 -ml-1 mr-3 h-5 w-5 ${
                          isActive
                            ? 'text-primary-500'
                            : 'text-gray-400 group-hover:text-gray-500'
                        }`}
                      />
                      <div>
                        <div className="font-medium">{item.name}</div>
                        <div className="text-xs text-gray-500">{item.description}</div>
                      </div>
                    </Link>
                  )
                })}

                {/* Logout */}
                <button
                  onClick={handleLogout}
                  className="w-full group flex items-center px-3 py-3 text-sm font-medium rounded-md text-red-700 hover:bg-red-50 hover:text-red-900 transition-colors duration-200 mt-4 border-t border-gray-200 pt-4"
                >
                  <ArrowRightOnRectangleIcon className="flex-shrink-0 -ml-1 mr-3 h-5 w-5 text-red-400 group-hover:text-red-500" />
                  <div>
                    <div className="font-medium">Sign Out</div>
                    <div className="text-xs text-red-500">End your session</div>
                  </div>
                </button>
              </nav>
            </div>

            {/* Quick Stats */}
            <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-sm font-medium text-gray-900 mb-4">Quick Stats</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Total Bookings</span>
                  <span className="text-sm font-medium text-gray-900">12</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Saved Properties</span>
                  <span className="text-sm font-medium text-gray-900">8</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Member Since</span>
                  <span className="text-sm font-medium text-gray-900">
                    {new Date(user.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          </aside>

          {/* Main Content */}
          <main className="mt-8 lg:mt-0 lg:col-span-9">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
