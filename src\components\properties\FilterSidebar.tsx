'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Property } from '@/types/property'

interface FilterSidebarProps {
  properties?: Property[]
  onFilter?: (query: string) => void
}

const propertyTypes = [
  { id: 'house', label: 'House' },
  { id: 'apartment', label: 'Apartment' },
  { id: 'villa', label: 'Villa' },
  { id: 'cabin', label: 'Cabin' },
]

export function FilterSidebar({ properties = [], onFilter }: FilterSidebarProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [selectedTypes, setSelectedTypes] = useState<string[]>(
    searchParams.get('types')?.split(',') || []
  )
  const [location, setLocation] = useState(searchParams.get('location') || '')
  const [priceRange, setPriceRange] = useState({
    min: Number(searchParams.get('minPrice')) || 0,
    max: Number(searchParams.get('maxPrice')) || 1000,
  })

  // Get unique locations from properties
  const locations = Array.from(new Set(properties.map(p => p.location)))

  useEffect(() => {
    // Build GROQ query based on filters
    let query = `*[_type == "property"`
    
    if (selectedTypes.length > 0) {
      query += ` && propertyType in [${selectedTypes.map(t => `"${t}"`).join(',')}]`
    }
    
    if (location) {
      query += ` && location == "${location}"`
    }
    
    query += ` && price >= ${priceRange.min} && price <= ${priceRange.max}`
    query += `]`

    onFilter?.(query)

    // Update URL with filter parameters
    const params = new URLSearchParams()
    if (selectedTypes.length > 0) params.set('types', selectedTypes.join(','))
    if (location) params.set('location', location)
    if (priceRange.min > 0) params.set('minPrice', priceRange.min.toString())
    if (priceRange.max < 1000) params.set('maxPrice', priceRange.max.toString())
    
    router.push(`?${params.toString()}`)
  }, [selectedTypes, location, priceRange, onFilter, router])

  const handleTypeChange = (typeId: string) => {
    setSelectedTypes(prev =>
      prev.includes(typeId)
        ? prev.filter(id => id !== typeId)
        : [...prev, typeId]
    )
  }

  return (
    <div className="w-64 space-y-8">
      {/* Property Types */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900">Property Type</h3>
        <div className="mt-4 space-y-2">
          {propertyTypes.map(type => (
            <div key={type.id} className="flex items-center">
              <input
                type="checkbox"
                id={type.id}
                checked={selectedTypes.includes(type.id)}
                onChange={() => handleTypeChange(type.id)}
                className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <label htmlFor={type.id} className="ml-2 text-sm text-gray-700">
                {type.label}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Location */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900">Location</h3>
        <select
          value={location}
          onChange={(e) => setLocation(e.target.value)}
          className="mt-4 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
        >
          <option value="">All Locations</option>
          {locations.map((loc) => (
            <option key={loc} value={loc}>
              {loc}
            </option>
          ))}
        </select>
      </div>

      {/* Price Range */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900">Price Range</h3>
        <div className="mt-4 space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">${priceRange.min}</span>
            <span className="text-sm text-gray-600">${priceRange.max}</span>
          </div>
          <div className="relative">
            <input
              type="range"
              min="0"
              max="1000"
              step="10"
              value={priceRange.min}
              onChange={(e) => setPriceRange(prev => ({ ...prev, min: Number(e.target.value) }))}
              className="w-full"
            />
            <input
              type="range"
              min="0"
              max="1000"
              step="10"
              value={priceRange.max}
              onChange={(e) => setPriceRange(prev => ({ ...prev, max: Number(e.target.value) }))}
              className="w-full absolute top-0"
            />
          </div>
        </div>
      </div>
    </div>
  )
} 