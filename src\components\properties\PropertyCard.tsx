'use client'

import { Property } from '@/types/property'
import { OptimizedImage } from '@/components/ui/OptimizedImage'
import Link from 'next/link'
import { formatPrice } from '@/lib/utils'
import { urlForImageWithDimensions, generateBlurDataURL } from '@/sanity/lib/image'
import { memo } from 'react'

interface PropertyCardProps {
  property: Property
  priority?: boolean
  loading?: 'lazy' | 'eager'
}

export const PropertyCard = memo(function PropertyCard({
  property,
  priority = false,
  loading = 'lazy'
}: PropertyCardProps) {
  // Helper function to check if image is a Sanity asset object
  const isSanityImageAsset = (image: any) => {
    return image?.asset?._ref &&
           typeof image.asset._ref === 'string' &&
           image.asset._ref.startsWith('image-')
  }

  // Helper function to check if image is a simple string URL
  const isStringUrl = (image: any) => {
    return typeof image === 'string'
  }

  // Get image URL with error handling
  const getImageUrl = () => {
    if (!property.images?.[0]) return '/placeholder.jpg'

    const firstImage = property.images[0]

    try {
      // Handle string URLs (for sample/mock data)
      if (isStringUrl(firstImage)) {
        return firstImage
      }

      // Handle Sanity image assets
      if (isSanityImageAsset(firstImage)) {
        return urlForImageWithDimensions(firstImage, 400, 300, 85)?.url() || '/placeholder.jpg'
      }

      // Fallback for any other format
      console.warn(`Unsupported image format for property ${property.title}:`, firstImage)
      return '/placeholder.jpg'
    } catch (error) {
      console.warn(`Error processing image for property ${property.title}:`, error)
      return '/placeholder.jpg'
    }
  }

  // Get blur data URL with error handling (only for Sanity assets)
  const getBlurDataURL = () => {
    if (!property.images?.[0] || !isSanityImageAsset(property.images[0])) {
      return undefined
    }

    try {
      return generateBlurDataURL(property.images[0])
    } catch (error) {
      console.warn(`Error generating blur data URL for property ${property.title}:`, error)
      return undefined
    }
  }

  const imageUrl = getImageUrl()
  const blurDataURL = getBlurDataURL()

  return (
    <Link
      href={`/properties/${property.slug}`}
      className="group block"
      prefetch={false}
    >
      <article className="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1">
        <div className="relative h-48">
          <OptimizedImage
            src={imageUrl || '/placeholder.jpg'}
            alt={property.title}
            width={400}
            height={300}
            fill
            objectFit="cover"
            priority={priority}
            loading={loading}
            {...(blurDataURL && { blurDataURL })}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            quality={85}
          />
        </div>
        <div className="p-4">
          <h3 className="text-golden-lg font-semibold mb-2 group-hover:text-blue-600 transition-colors line-clamp-2 leading-tight">
            {property.title}
          </h3>
          <p className="text-gray-600 text-golden-sm mb-2 line-clamp-1">{property.location}</p>
          <div className="flex items-center justify-between">
            <span className="text-blue-600 font-semibold text-golden-base">
              {formatPrice(property.price)}/night
            </span>
          </div>
        </div>
      </article>
    </Link>
  )
})