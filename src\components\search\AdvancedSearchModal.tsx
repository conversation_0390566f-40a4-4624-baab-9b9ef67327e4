'use client'

import { useState, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { XMarkIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline'
import { AdvancedSearchFilters } from '@/lib/search'

interface AdvancedSearchModalProps {
  isOpen: boolean
  onClose: () => void
  onSearch: (filters: AdvancedSearchFilters) => void
  initialFilters?: AdvancedSearchFilters
}

export function AdvancedSearchModal({ 
  isOpen, 
  onClose, 
  onSearch, 
  initialFilters = {} 
}: AdvancedSearchModalProps) {
  const [filters, setFilters] = useState<AdvancedSearchFilters>(initialFilters)
  const [facets, setFacets] = useState<any>(null)

  useEffect(() => {
    if (isOpen) {
      fetchFacets()
    }
  }, [isOpen])

  const fetchFacets = async () => {
    try {
      const response = await fetch('/api/search/facets')
      if (response.ok) {
        const data = await response.json()
        setFacets(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch facets:', error)
    }
  }

  const handleFilterChange = (key: keyof AdvancedSearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(filters)
    onClose()
  }

  const clearFilters = () => {
    setFilters({})
  }

  const propertyTypes = [
    'apartment', 'house', 'villa', 'cabin', 'cottage', 'studio', 'penthouse'
  ]

  const tourCategories = [
    'wildlife', 'cultural', 'adventure', 'beach', 'mountain', 'city', 'historical'
  ]

  const difficulties = [
    'easy', 'moderate', 'challenging', 'expert'
  ]

  const amenities = [
    'wifi', 'parking', 'pool', 'gym', 'spa', 'restaurant', 'bar', 'beach_access',
    'air_conditioning', 'heating', 'kitchen', 'laundry', 'pet_friendly', 'smoking_allowed'
  ]

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                    Advanced Search
                  </Dialog.Title>
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Search Query */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Search Keywords
                    </label>
                    <input
                      type="text"
                      value={filters.query || ''}
                      onChange={(e) => handleFilterChange('query', e.target.value)}
                      placeholder="Enter keywords..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>

                  {/* Location */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Location
                    </label>
                    <input
                      type="text"
                      value={filters.location || ''}
                      onChange={(e) => handleFilterChange('location', e.target.value)}
                      placeholder="Enter location..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Property Types */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Property Types
                      </label>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {propertyTypes.map((type) => (
                          <label key={type} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.propertyType?.includes(type) || false}
                              onChange={(e) => {
                                const current = filters.propertyType || []
                                if (e.target.checked) {
                                  handleFilterChange('propertyType', [...current, type])
                                } else {
                                  handleFilterChange('propertyType', current.filter(t => t !== type))
                                }
                              }}
                              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            />
                            <span className="ml-2 text-sm text-gray-700 capitalize">
                              {type.replace('_', ' ')}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>

                    {/* Tour Categories */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Tour Categories
                      </label>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {tourCategories.map((category) => (
                          <label key={category} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.tourCategory?.includes(category) || false}
                              onChange={(e) => {
                                const current = filters.tourCategory || []
                                if (e.target.checked) {
                                  handleFilterChange('tourCategory', [...current, category])
                                } else {
                                  handleFilterChange('tourCategory', current.filter(c => c !== category))
                                }
                              }}
                              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            />
                            <span className="ml-2 text-sm text-gray-700 capitalize">
                              {category}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Price Range */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Price Range (KES)
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      <input
                        type="number"
                        placeholder="Min price"
                        value={filters.priceRange?.min || ''}
                        onChange={(e) => handleFilterChange('priceRange', {
                          ...filters.priceRange,
                          min: parseInt(e.target.value) || 0
                        })}
                        className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      />
                      <input
                        type="number"
                        placeholder="Max price"
                        value={filters.priceRange?.max || ''}
                        onChange={(e) => handleFilterChange('priceRange', {
                          ...filters.priceRange,
                          max: parseInt(e.target.value) || 0
                        })}
                        className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>
                  </div>

                  {/* Guests */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Number of Guests
                    </label>
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">Adults</label>
                        <input
                          type="number"
                          min="1"
                          value={filters.guests?.adults || 1}
                          onChange={(e) => handleFilterChange('guests', {
                            ...filters.guests,
                            adults: parseInt(e.target.value) || 1
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">Children</label>
                        <input
                          type="number"
                          min="0"
                          value={filters.guests?.children || 0}
                          onChange={(e) => handleFilterChange('guests', {
                            ...filters.guests,
                            children: parseInt(e.target.value) || 0
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">Infants</label>
                        <input
                          type="number"
                          min="0"
                          value={filters.guests?.infants || 0}
                          onChange={(e) => handleFilterChange('guests', {
                            ...filters.guests,
                            infants: parseInt(e.target.value) || 0
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-between pt-6">
                    <button
                      type="button"
                      onClick={clearFilters}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      Clear All
                    </button>
                    <div className="flex space-x-3">
                      <button
                        type="button"
                        onClick={onClose}
                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 flex items-center"
                      >
                        <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                        Search
                      </button>
                    </div>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
