'use client'

import { useState, useEffect, useRef } from 'react'
import { MapPinIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline'

interface MapSearchProps {
  onLocationSelect: (lat: number, lng: number, address: string) => void
  onSearch: (lat: number, lng: number, radius: number) => void
  initialLocation?: { lat: number; lng: number }
  className?: string
}

export function MapSearch({ 
  onLocationSelect, 
  onSearch, 
  initialLocation,
  className = '' 
}: MapSearchProps) {
  const [isMapLoaded, setIsMapLoaded] = useState(false)
  const [selectedLocation, setSelectedLocation] = useState(initialLocation)
  const [radius, setRadius] = useState(10) // Default 10km radius
  const [address, setAddress] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)
  const markerRef = useRef<any>(null)

  // Load Google Maps script
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.google) {
      const script = document.createElement('script')
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`
      script.async = true
      script.defer = true
      script.onload = () => setIsMapLoaded(true)
      document.head.appendChild(script)
    } else if (window.google) {
      setIsMapLoaded(true)
    }
  }, [])

  // Initialize map
  useEffect(() => {
    if (isMapLoaded && mapRef.current && !mapInstanceRef.current) {
      const defaultLocation = initialLocation || { lat: -1.2921, lng: 36.8219 } // Nairobi

      mapInstanceRef.current = new window.google.maps.Map(mapRef.current, {
        center: defaultLocation,
        zoom: 10,
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }]
          }
        ]
      })

      // Add click listener
      mapInstanceRef.current.addListener('click', (event: any) => {
        const lat = event.latLng.lat()
        const lng = event.latLng.lng()
        handleLocationSelect(lat, lng)
      })

      // Initialize with default location
      if (initialLocation) {
        handleLocationSelect(initialLocation.lat, initialLocation.lng)
      }
    }
  }, [isMapLoaded, initialLocation])

  const handleLocationSelect = async (lat: number, lng: number) => {
    setSelectedLocation({ lat, lng })

    // Update marker
    if (markerRef.current) {
      markerRef.current.setMap(null)
    }

    markerRef.current = new window.google.maps.Marker({
      position: { lat, lng },
      map: mapInstanceRef.current,
      title: 'Selected Location'
    })

    // Reverse geocoding to get address
    try {
      const geocoder = new window.google.maps.Geocoder()
      const response = await geocoder.geocode({ location: { lat, lng } })
      
      if (response.results[0]) {
        const formattedAddress = response.results[0].formatted_address
        setAddress(formattedAddress)
        onLocationSelect(lat, lng, formattedAddress)
      }
    } catch (error) {
      console.error('Geocoding error:', error)
      onLocationSelect(lat, lng, `${lat.toFixed(6)}, ${lng.toFixed(6)}`)
    }
  }

  const handleSearch = async () => {
    if (!selectedLocation) {
      alert('Please select a location on the map first')
      return
    }

    setIsSearching(true)
    try {
      await onSearch(selectedLocation.lat, selectedLocation.lng, radius)
    } finally {
      setIsSearching(false)
    }
  }

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude
          const lng = position.coords.longitude
          
          if (mapInstanceRef.current) {
            mapInstanceRef.current.setCenter({ lat, lng })
            mapInstanceRef.current.setZoom(12)
          }
          
          handleLocationSelect(lat, lng)
        },
        (error) => {
          console.error('Geolocation error:', error)
          alert('Unable to get your current location')
        }
      )
    } else {
      alert('Geolocation is not supported by this browser')
    }
  }

  const searchByAddress = async (searchAddress: string) => {
    if (!searchAddress.trim()) return

    try {
      const geocoder = new window.google.maps.Geocoder()
      const response = await geocoder.geocode({ address: searchAddress })
      
      if (response.results[0]) {
        const location = response.results[0].geometry.location
        const lat = location.lat()
        const lng = location.lng()
        
        if (mapInstanceRef.current) {
          mapInstanceRef.current.setCenter({ lat, lng })
          mapInstanceRef.current.setZoom(12)
        }
        
        handleLocationSelect(lat, lng)
      } else {
        alert('Location not found')
      }
    } catch (error) {
      console.error('Geocoding error:', error)
      alert('Error searching for location')
    }
  }

  if (!isMapLoaded) {
    return (
      <div className={`bg-gray-100 rounded-lg flex items-center justify-center ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">Loading map...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg overflow-hidden ${className}`}>
      {/* Map Controls */}
      <div className="p-4 border-b border-gray-200">
        <div className="space-y-3">
          {/* Address Search */}
          <div className="flex space-x-2">
            <input
              type="text"
              placeholder="Search for a location..."
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && searchByAddress(address)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
            <button
              onClick={() => searchByAddress(address)}
              className="px-3 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <MagnifyingGlassIcon className="h-4 w-4" />
            </button>
            <button
              onClick={getCurrentLocation}
              className="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
              title="Use current location"
            >
              <MapPinIcon className="h-4 w-4" />
            </button>
          </div>

          {/* Search Radius */}
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">
              Search radius:
            </label>
            <select
              value={radius}
              onChange={(e) => setRadius(parseInt(e.target.value))}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value={5}>5 km</option>
              <option value={10}>10 km</option>
              <option value={20}>20 km</option>
              <option value={50}>50 km</option>
              <option value={100}>100 km</option>
            </select>

            {selectedLocation && (
              <button
                onClick={handleSearch}
                disabled={isSearching}
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
              >
                {isSearching ? 'Searching...' : 'Search Area'}
              </button>
            )}
          </div>

          {/* Selected Location Info */}
          {selectedLocation && (
            <div className="text-sm text-gray-600">
              <p>Selected: {address || `${selectedLocation.lat.toFixed(6)}, ${selectedLocation.lng.toFixed(6)}`}</p>
            </div>
          )}
        </div>
      </div>

      {/* Map Container */}
      <div 
        ref={mapRef} 
        className="w-full h-96"
        style={{ minHeight: '400px' }}
      />

      {/* Instructions */}
      <div className="p-3 bg-gray-50 text-xs text-gray-600">
        Click on the map to select a location, or search for an address above.
      </div>
    </div>
  )
}
