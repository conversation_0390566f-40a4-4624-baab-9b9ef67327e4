'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'

interface PropertySearchHeroProps {
  className?: string
}

export function PropertySearchHero({ className }: PropertySearchHeroProps) {
  const [searchType, setSearchType] = useState<'rental' | 'sale' | 'airbnb' | 'houses-to-let'>('rental')
  const [location, setLocation] = useState('')
  const [priceRange, setPriceRange] = useState('')

  const handleSearch = () => {
    const params = new URLSearchParams()

    if (searchType === 'houses-to-let') {
      // Redirect to dedicated houses to let page
      if (location) params.set('location', location)
      if (priceRange) params.set('price', priceRange)
      window.location.href = `/houses-to-let?${params.toString()}`
    } else {
      params.set('type', searchType)
      if (location) params.set('location', location)
      if (priceRange) params.set('price', priceRange)
      window.location.href = `/properties?${params.toString()}`
    }
  }

  return (
    <div className={`bg-white/95 backdrop-blur-md rounded-2xl shadow-xl p-8 border border-primary-100 ${className}`}>
      <div className="text-center mb-8">
        <h2 className="text-golden-2xl font-bold text-gray-900 font-serif mb-2">
          Find Your Perfect Property
        </h2>
        <p className="text-gray-600 text-golden-base">
          Search thousands of properties across Kenya
        </p>
      </div>

      {/* Property Type Tabs */}
      <div className="flex flex-wrap gap-2 mb-6 p-1 bg-gray-100 rounded-xl">
        <button
          onClick={() => setSearchType('rental')}
          className={`flex-1 min-w-[100px] px-4 py-3 rounded-lg font-medium text-golden-sm transition-all duration-300 ${
            searchType === 'rental'
              ? 'bg-primary-500 text-white shadow-md'
              : 'text-gray-600 hover:text-primary-600 hover:bg-white'
          }`}
        >
          Rent
        </button>
        <button
          onClick={() => setSearchType('houses-to-let')}
          className={`flex-1 min-w-[100px] px-4 py-3 rounded-lg font-medium text-golden-sm transition-all duration-300 ${
            searchType === 'houses-to-let'
              ? 'bg-primary-600 text-white shadow-md'
              : 'text-gray-600 hover:text-primary-600 hover:bg-white'
          }`}
        >
          Houses to Let
        </button>
        <button
          onClick={() => setSearchType('sale')}
          className={`flex-1 min-w-[100px] px-4 py-3 rounded-lg font-medium text-golden-sm transition-all duration-300 ${
            searchType === 'sale'
              ? 'bg-secondary-500 text-white shadow-md'
              : 'text-gray-600 hover:text-secondary-600 hover:bg-white'
          }`}
        >
          Buy
        </button>
        <button
          onClick={() => setSearchType('airbnb')}
          className={`flex-1 min-w-[100px] px-4 py-3 rounded-lg font-medium text-golden-sm transition-all duration-300 ${
            searchType === 'airbnb'
              ? 'bg-accent-500 text-white shadow-md'
              : 'text-gray-600 hover:text-accent-600 hover:bg-white'
          }`}
        >
          Airbnb
        </button>
      </div>

      {/* Search Form */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {/* Location */}
        <div>
          <label className="block text-golden-sm font-medium text-gray-700 mb-2">
            Location
          </label>
          <select
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-golden-base"
          >
            <option value="">All Locations</option>
            <option value="nairobi">Nairobi</option>
            <option value="mombasa">Mombasa</option>
            <option value="kisumu">Kisumu</option>
            <option value="nakuru">Nakuru</option>
            <option value="eldoret">Eldoret</option>
            <option value="thika">Thika</option>
            <option value="malindi">Malindi</option>
            <option value="diani">Diani Beach</option>
          </select>
        </div>

        {/* Price Range */}
        <div>
          <label className="block text-golden-sm font-medium text-gray-700 mb-2">
            {searchType === 'rental' || searchType === 'houses-to-let' ? 'Monthly Rent' : searchType === 'sale' ? 'Price Range' : 'Nightly Rate'}
          </label>
          <select
            value={priceRange}
            onChange={(e) => setPriceRange(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-golden-base"
          >
            <option value="">Any Price</option>
            {(searchType === 'rental' || searchType === 'houses-to-let') && (
              <>
                <option value="0-50000">Under KES 50,000</option>
                <option value="50000-100000">KES 50,000 - 100,000</option>
                <option value="100000-150000">KES 100,000 - 150,000</option>
                <option value="150000-200000">KES 150,000 - 200,000</option>
                <option value="200000+">Above KES 200,000</option>
              </>
            )}
            {searchType === 'sale' && (
              <>
                <option value="0-5000000">Under KES 5M</option>
                <option value="5000000-10000000">KES 5M - 10M</option>
                <option value="10000000-20000000">KES 10M - 20M</option>
                <option value="20000000-50000000">KES 20M - 50M</option>
                <option value="50000000+">Above KES 50M</option>
              </>
            )}
            {searchType === 'airbnb' && (
              <>
                <option value="0-3000">Under KES 3,000</option>
                <option value="3000-6000">KES 3,000 - 6,000</option>
                <option value="6000-12000">KES 6,000 - 12,000</option>
                <option value="12000-25000">KES 12,000 - 25,000</option>
                <option value="25000+">Above KES 25,000</option>
              </>
            )}
          </select>
        </div>

        {/* Property Type */}
        <div>
          <label className="block text-golden-sm font-medium text-gray-700 mb-2">
            Property Type
          </label>
          <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-golden-base">
            <option value="">All Types</option>
            <option value="apartment">Apartment</option>
            <option value="house">House</option>
            <option value="villa">Villa</option>
            <option value="townhouse">Townhouse</option>
            <option value="studio">Studio</option>
            <option value="penthouse">Penthouse</option>
          </select>
        </div>
      </div>

      {/* Search Button */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Button
          onClick={handleSearch}
          className="flex-1 bg-gradient-to-r from-primary-500 to-accent-500 hover:from-primary-600 hover:to-accent-600 text-white font-semibold py-4 px-8 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          Search Properties
        </Button>
        
        <Link href="/list-your-property">
          <Button
            variant="outline"
            className="w-full sm:w-auto border-2 border-primary-500 text-primary-600 hover:bg-primary-50 font-semibold py-4 px-8 rounded-lg transition-all duration-300"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            List Property
          </Button>
        </Link>
      </div>
    </div>
  )
}
