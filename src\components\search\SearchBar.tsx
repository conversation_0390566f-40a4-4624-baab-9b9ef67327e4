'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Property } from '@/types/property'
import { MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline'
import { AdvancedSearchModal } from './AdvancedSearchModal'
import { AdvancedSearchFilters } from '@/lib/search'

interface SearchBarProps {
  properties?: Property[]
  onSearch?: (filteredProperties: Property[]) => void
  showAdvancedButton?: boolean
  placeholder?: string
  className?: string
}

export function SearchBar({
  properties = [],
  onSearch,
  showAdvancedButton = true,
  placeholder = "Search properties, tours, or locations...",
  className = ""
}: SearchBarProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [searchTerm, setSearchTerm] = useState(searchParams.get('q') || '')
  const [location, setLocation] = useState(searchParams.get('location') || '')
  const [category, setCategory] = useState(searchParams.get('category') || '')
  const [showAdvancedModal, setShowAdvancedModal] = useState(false)
  const [suggestions, setSuggestions] = useState<any[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)

  useEffect(() => {
    if (properties.length > 0) {
      const filteredProperties = properties.filter((property) => {
        const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase())
        const matchesLocation = property.location.toLowerCase().includes(location.toLowerCase())
        const matchesCategory = !category || property.category === category

        return matchesSearch && matchesLocation && matchesCategory
      })

      onSearch?.(filteredProperties)
    }

    // Update URL with search parameters
    const params = new URLSearchParams()
    if (searchTerm) params.set('q', searchTerm)
    if (location) params.set('location', location)
    if (category) params.set('category', category)

    if (params.toString()) {
      router.push(`?${params.toString()}`)
    }
  }, [searchTerm, location, category, properties, onSearch, router])

  // Fetch search suggestions
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (searchTerm.length >= 2) {
        try {
          const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(searchTerm)}`)
          if (response.ok) {
            const data = await response.json()
            setSuggestions([
              ...data.data.properties.map((p: any) => ({ ...p, type: 'property' })),
              ...data.data.tours.map((t: any) => ({ ...t, type: 'tour' })),
              ...data.data.locations.map((l: any) => ({ ...l, type: 'location' }))
            ])
            setShowSuggestions(true)
          }
        } catch (error) {
          console.error('Failed to fetch suggestions:', error)
        }
      } else {
        setSuggestions([])
        setShowSuggestions(false)
      }
    }

    const debounceTimer = setTimeout(fetchSuggestions, 300)
    return () => clearTimeout(debounceTimer)
  }, [searchTerm])

  const handleSearch = () => {
    const params = new URLSearchParams()
    if (searchTerm) params.set('q', searchTerm)
    if (location) params.set('location', location)
    if (category) params.set('category', category)

    router.push(`/search?${params.toString()}`)
  }

  const handleAdvancedSearch = (filters: AdvancedSearchFilters) => {
    const params = new URLSearchParams()

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '' && value !== null) {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','))
        } else if (typeof value === 'object' && value !== null) {
          Object.entries(value).forEach(([subKey, subValue]) => {
            if (subValue !== undefined && subValue !== '' && subValue !== 0) {
              params.set(subKey === 'min' ? 'minPrice' : subKey === 'max' ? 'maxPrice' : subKey, subValue.toString())
            }
          })
        } else {
          params.set(key, value.toString())
        }
      }
    })

    router.push(`/search?${params.toString()}`)
  }

  const selectSuggestion = (suggestion: any) => {
    if (suggestion.type === 'location') {
      setLocation(suggestion.name)
      setSearchTerm('')
    } else {
      setSearchTerm(suggestion.title)
    }
    setShowSuggestions(false)
  }

  return (
    <div className={`bg-white rounded-lg shadow-md ${className}`}>
      <div className="p-6">
        <div className="relative">
          {/* Main Search Input */}
          <div className="flex space-x-2">
            <div className="flex-1 relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onFocus={() => setShowSuggestions(suggestions.length > 0)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                placeholder={placeholder}
                className="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-golden-base"
              />
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />

              {/* Search Suggestions */}
              {showSuggestions && suggestions.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => selectSuggestion(suggestion)}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-2"
                    >
                      <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600">
                        {suggestion.type}
                      </span>
                      <span className="text-sm text-gray-900">
                        {suggestion.title || suggestion.name}
                      </span>
                    </button>
                  ))}
                </div>
              )}
            </div>

            <button
              onClick={handleSearch}
              className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 font-medium"
            >
              Search
            </button>

            {showAdvancedButton && (
              <button
                onClick={() => setShowAdvancedModal(true)}
                className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center"
                title="Advanced Search"
              >
                <FunnelIcon className="h-5 w-5" />
              </button>
            )}
          </div>

          {/* Quick Filters */}
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Location
              </label>
              <input
                type="text"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                placeholder="Enter location..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-golden-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-golden-sm"
              >
                <option value="">All Categories</option>
                <option value="luxury">Luxury</option>
                <option value="budget">Budget</option>
                <option value="family">Family</option>
                <option value="business">Business</option>
                <option value="wildlife">Wildlife Tours</option>
                <option value="cultural">Cultural Tours</option>
                <option value="adventure">Adventure Tours</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Search Modal */}
      <AdvancedSearchModal
        isOpen={showAdvancedModal}
        onClose={() => setShowAdvancedModal(false)}
        onSearch={handleAdvancedSearch}
        initialFilters={{
          query: searchTerm,
          location: location,
          ...(category && { tourCategory: [category] })
        }}
      />
    </div>
  )
}