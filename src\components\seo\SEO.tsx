import { Metadata } from 'next'
import { Settings } from '@/types/settings'

interface SEOProps {
  title?: string
  description?: string
  image?: {
    _type: 'image'
    asset: {
      _ref: string
      _type: 'reference'
    }
  }
  url?: string
  settings: Settings | null
}

export function generateSEO({ title, description, image, url, settings }: SEOProps): Metadata {
  const defaultSiteTitle = 'RiftStays - Kenya\'s Leading Property Platform'
  const defaultDescription = 'Find your perfect stay with RiftStays. From luxury accommodations to unforgettable safari experiences in Kenya.'

  const siteTitle = title
    ? `${title} | ${settings?.title || 'RiftStays'}`
    : settings?.title || defaultSiteTitle

  const siteDescription = description || settings?.description || defaultDescription

  const siteUrl = url || process.env.NEXT_PUBLIC_SITE_URL || 'https://riftstays.com'

  const imageUrl = image?.asset._ref
    ? `https://cdn.sanity.io/images/${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID}/${image.asset._ref.replace('image-', '').replace('-jpg', '.jpg')}`
    : settings?.logo?.asset._ref
    ? `https://cdn.sanity.io/images/${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID}/${settings.logo.asset._ref.replace('image-', '').replace('-jpg', '.jpg')}`
    : `${siteUrl}/logo.png`

  return {
    title: siteTitle,
    description: siteDescription,
    metadataBase: new URL(siteUrl),
    openGraph: {
      title: siteTitle,
      description: siteDescription,
      url: siteUrl,
      siteName: settings?.title || 'RiftStays',
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: siteTitle,
        },
      ],
      locale: 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: siteTitle,
      description: siteDescription,
      images: [imageUrl],
      creator: settings?.twitterHandle,
    },
    alternates: {
      canonical: siteUrl,
    },
  }
} 