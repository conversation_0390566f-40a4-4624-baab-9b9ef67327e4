'use client'

import { ButtonHTMLAttributes, forwardRef } from 'react'
import { cn } from '@/lib/utils'

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', ...props }, ref) => {
    return (
      <button
        className={cn(
          'inline-flex items-center justify-center rounded-full font-semibold btn-text text-crisp transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
          {
            'bg-gradient-to-r from-primary-500 to-accent-500 text-white hover:from-primary-600 hover:to-accent-600 focus-visible:ring-primary-500 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 hover:text-glow': variant === 'primary',
            'bg-gradient-to-r from-secondary-500 to-primary-400 text-white hover:from-secondary-600 hover:to-primary-500 focus-visible:ring-secondary-500 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 hover:text-glow': variant === 'secondary',
            'border-2 border-primary-300 bg-transparent text-primary-700 hover:bg-primary-50 hover:border-primary-500 focus-visible:ring-primary-500 transition-all duration-300 hover:text-pop': variant === 'outline',
            'bg-transparent text-gray-700 hover:bg-primary-50 hover:text-primary-700 focus-visible:ring-primary-500 transition-all duration-300 hover:text-pop': variant === 'ghost',
            'h-8 px-3 text-golden-sm': size === 'sm',
            'h-10 px-4 text-golden-base': size === 'md',
            'h-12 px-6 text-golden-lg': size === 'lg',
          },
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)

Button.displayName = 'Button' 