import { cn } from '@/lib/utils'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  }

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',
        sizeClasses[size],
        className
      )}
    />
  )
}

interface LoadingSkeletonProps {
  className?: string
  lines?: number
}

export function LoadingSkeleton({ className, lines = 1 }: LoadingSkeletonProps) {
  return (
    <div className={cn('animate-pulse', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={cn(
            'bg-gray-200 rounded-sm',
            i === lines - 1 ? 'w-3/4' : 'w-full',
            lines > 1 ? 'h-4 mb-2' : 'h-4'
          )}
        />
      ))}
    </div>
  )
}

interface PropertyCardSkeletonProps {
  className?: string
}

export function PropertyCardSkeleton({ className }: PropertyCardSkeletonProps) {
  return (
    <div className={cn('bg-white rounded-lg shadow-md overflow-hidden', className)}>
      <div className="h-48 bg-gray-200 animate-pulse" />
      <div className="p-4 space-y-3">
        <LoadingSkeleton lines={2} />
        <LoadingSkeleton className="w-1/2" />
      </div>
    </div>
  )
}

interface BlogCardSkeletonProps {
  className?: string
}

export function BlogCardSkeleton({ className }: BlogCardSkeletonProps) {
  return (
    <div className={cn('bg-white rounded-lg shadow-xs overflow-hidden', className)}>
      <div className="aspect-w-16 aspect-h-9 bg-gray-200 animate-pulse" />
      <div className="p-6 space-y-4">
        <LoadingSkeleton lines={3} />
        <div className="flex items-center gap-4">
          <div className="h-10 w-10 rounded-full bg-gray-200 animate-pulse" />
          <div className="space-y-2">
            <LoadingSkeleton className="w-24" />
            <LoadingSkeleton className="w-20" />
          </div>
        </div>
      </div>
    </div>
  )
}
