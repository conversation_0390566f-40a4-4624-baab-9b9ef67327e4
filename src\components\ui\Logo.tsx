'use client'

import Image from 'next/image'
import { cn } from '@/lib/utils'
import { useState } from 'react'

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'white' | 'dark'
  className?: string
  priority?: boolean
  showText?: boolean
  siteTitle?: string
}

export function Logo({
  size = 'md',
  variant = 'default',
  className,
  priority = false,
  showText = true,
  siteTitle = 'RiftStays'
}: LogoProps) {
  const [imageError, setImageError] = useState(false)

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16',
    xl: 'h-20 w-20'
  }

  const textSizeClasses = {
    sm: 'text-golden-lg',
    md: 'text-golden-2xl',
    lg: 'text-golden-3xl',
    xl: 'text-golden-4xl'
  }

  const dimensions = {
    sm: { width: 32, height: 32 },
    md: { width: 48, height: 48 },
    lg: { width: 64, height: 64 },
    xl: { width: 80, height: 80 }
  }

  const logoSrc = variant === 'white' ? '/logo-white.svg' : '/logo.svg'

  // Fallback logo component when image fails to load
  const FallbackLogo = () => (
    <div
      className={cn(
        sizeClasses[size],
        'bg-gradient-to-br from-primary-500 via-warning-500 to-danger-500 rounded-lg flex items-center justify-center text-white font-bold',
        variant === 'white' && 'from-white via-gray-100 to-gray-200 text-gray-800',
        variant === 'dark' && 'from-gray-800 via-gray-700 to-gray-600 text-white'
      )}
    >
      R
    </div>
  )

  return (
    <div className={cn('flex items-center space-x-3', className)}>
      {imageError ? (
        <FallbackLogo />
      ) : (
        <Image
          src={logoSrc}
          alt={siteTitle}
          width={dimensions[size].width}
          height={dimensions[size].height}
          className={cn(
            sizeClasses[size],
            'object-contain filter drop-shadow-md transition-all duration-300 hover:scale-110 hover:drop-shadow-lg',
            variant === 'white' && 'brightness-0 invert'
          )}
          priority={priority}
          quality={100}
          sizes={`${dimensions[size].width}px`}
          onError={() => setImageError(true)}
          unoptimized={true} // Disable Next.js image optimization to prevent hydration issues
        />
      )}
      {showText && (
        <span className={cn(
          textSizeClasses[size],
          'font-bold bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent font-serif text-crisp text-pop transition-all duration-300 hover:text-glow',
          variant === 'white' && 'from-white via-gray-100 to-gray-200',
          variant === 'dark' && 'from-gray-800 via-gray-700 to-gray-600'
        )}>
          {siteTitle}
        </span>
      )}
    </div>
  )
}

// Convenience components for common use cases
export const HeaderLogo = (props: Omit<LogoProps, 'size'>) => (
  <Logo size="md" priority {...props} />
)

export const FooterLogo = (props: Omit<LogoProps, 'size'>) => (
  <Logo size="md" {...props} />
)

export const HeroLogo = (props: Omit<LogoProps, 'size'>) => (
  <Logo size="xl" {...props} />
)
