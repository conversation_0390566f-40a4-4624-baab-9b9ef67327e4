'use client'

import { ReactNode, HTMLAttributes } from 'react'
import { cn } from '@/lib/utils'

// Golden Ratio Typography Component
// Based on φ ≈ 1.618 for optimal visual hierarchy

interface TypographyProps extends HTMLAttributes<HTMLElement> {
  children: ReactNode
  variant?: 'display' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'lead' | 'body' | 'small' | 'caption'
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div'
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold'
  color?: 'primary' | 'secondary' | 'muted' | 'accent' | 'inherit'
  align?: 'left' | 'center' | 'right' | 'justify'
  responsive?: boolean
}

export function Typography({
  children,
  variant = 'body',
  as,
  weight = 'normal',
  color = 'inherit',
  align = 'left',
  responsive = true,
  className,
  ...props
}: TypographyProps) {
  // Determine the HTML element to render
  const getElement = () => {
    if (as) return as
    
    switch (variant) {
      case 'display':
      case 'h1':
        return 'h1'
      case 'h2':
        return 'h2'
      case 'h3':
        return 'h3'
      case 'h4':
        return 'h4'
      case 'h5':
        return 'h5'
      case 'h6':
        return 'h6'
      case 'lead':
      case 'body':
        return 'p'
      case 'small':
      case 'caption':
        return 'span'
      default:
        return 'p'
    }
  }

  // Get typography classes based on variant
  const getVariantClasses = () => {
    const baseClasses = 'text-readable'
    
    switch (variant) {
      case 'display':
        return cn(
          baseClasses,
          'text-golden-6xl font-serif font-bold',
          responsive && 'sm:text-golden-5xl md:text-golden-6xl'
        )
      case 'h1':
        return cn(
          baseClasses,
          'text-golden-4xl font-serif font-bold',
          responsive && 'sm:text-golden-3xl md:text-golden-4xl'
        )
      case 'h2':
        return cn(
          baseClasses,
          'text-golden-3xl font-serif font-semibold',
          responsive && 'sm:text-golden-2xl md:text-golden-3xl'
        )
      case 'h3':
        return cn(
          baseClasses,
          'text-golden-2xl font-serif font-semibold',
          responsive && 'sm:text-golden-xl md:text-golden-2xl'
        )
      case 'h4':
        return cn(
          baseClasses,
          'text-golden-xl font-serif font-medium',
          responsive && 'sm:text-golden-lg md:text-golden-xl'
        )
      case 'h5':
        return cn(
          baseClasses,
          'text-golden-lg font-serif font-medium',
          responsive && 'sm:text-golden-base md:text-golden-lg'
        )
      case 'h6':
        return cn(
          baseClasses,
          'text-golden-base font-serif font-medium'
        )
      case 'lead':
        return cn(
          baseClasses,
          'text-golden-xl font-sans font-normal leading-relaxed',
          responsive && 'sm:text-golden-lg md:text-golden-xl'
        )
      case 'body':
        return cn(
          baseClasses,
          'text-golden-base font-sans font-normal leading-relaxed'
        )
      case 'small':
        return cn(
          baseClasses,
          'text-golden-sm font-sans font-normal'
        )
      case 'caption':
        return cn(
          baseClasses,
          'text-golden-xs font-sans font-medium uppercase tracking-wide'
        )
      default:
        return baseClasses
    }
  }

  // Get weight classes
  const getWeightClasses = () => {
    switch (weight) {
      case 'light':
        return 'font-light'
      case 'normal':
        return 'font-normal'
      case 'medium':
        return 'font-medium'
      case 'semibold':
        return 'font-semibold'
      case 'bold':
        return 'font-bold'
      default:
        return ''
    }
  }

  // Get color classes
  const getColorClasses = () => {
    switch (color) {
      case 'primary':
        return 'text-primary-600'
      case 'secondary':
        return 'text-secondary-600'
      case 'muted':
        return 'text-gray-600'
      case 'accent':
        return 'text-accent-600'
      case 'inherit':
      default:
        return 'text-inherit'
    }
  }

  // Get alignment classes
  const getAlignClasses = () => {
    switch (align) {
      case 'left':
        return 'text-left'
      case 'center':
        return 'text-center'
      case 'right':
        return 'text-right'
      case 'justify':
        return 'text-justify'
      default:
        return 'text-left'
    }
  }

  const Element = getElement()
  
  const combinedClasses = cn(
    getVariantClasses(),
    getWeightClasses(),
    getColorClasses(),
    getAlignClasses(),
    'text-high-contrast text-crisp', // Ensure good contrast and crisp rendering
    variant === 'display' || variant === 'h1' || variant === 'h2' ? 'text-pop' : '',
    className
  )

  return (
    <Element className={combinedClasses} {...props}>
      {children}
    </Element>
  )
}

// Convenience components for common use cases
export const Heading = ({ level = 1, ...props }: { level?: 1 | 2 | 3 | 4 | 5 | 6 } & Omit<TypographyProps, 'variant'>) => (
  <Typography variant={`h${level}` as 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'} {...props} />
)

export const Display = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="display" {...props} />
)

export const Lead = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="lead" {...props} />
)

export const Body = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="body" {...props} />
)

export const Small = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="small" {...props} />
)

export const Caption = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="caption" {...props} />
)
