import { Property } from '@/types/property'
import { Tour, BookingType } from '@/types/booking'

// Sample Properties Data
export const sampleProperties: Property[] = [
  {
    _id: 'luxury-safari-lodge',
    _type: 'property',
    title: 'Luxury Safari Lodge',
    slug: {
      _type: 'slug',
      current: 'luxury-safari-lodge'
    },
    description: 'Experience the ultimate safari adventure in our luxury lodge overlooking the Maasai Mara. Wake up to breathtaking views of the African savanna and enjoy world-class amenities including private game drives, spa services, and gourmet dining. Each suite features a private balcony with panoramic views of the wildlife migration routes.',
    price: 15000,
    location: 'Maasai Mara, Kenya',
    bedrooms: 2,
    bathrooms: 2,
    maxGuests: 4,
    category: 'countryside',
    propertyType: 'villa',
    purpose: 'airbnb',
    amenities: [
      'Private Game Drives',
      'Spa Services',
      'Fine Dining Restaurant',
      'Private Balcony',
      'Wi-Fi',
      'Air Conditioning',
      'Mini Bar',
      'Room Service',
      'Laundry Service',
      'Airport Transfer'
    ],
    rules: [
      'No smoking inside the lodge',
      'Quiet hours from 10 PM to 6 AM',
      'Children must be supervised at all times',
      'No feeding of wild animals',
      'Follow guide instructions during game drives'
    ],
    availability: [
      {
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      }
    ],
    images: [
      {
        _type: 'image',
        asset: {
          _ref: 'luxury-safari-lodge',
          _type: 'reference'
        }
      }
    ],
    featured: true,
    status: 'available' as const,
    verified: true,
    owner: {
      name: 'Safari Lodge Owner',
      email: '<EMAIL>',
      phone: '+254 722 123 456'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    _id: 'coastal-beach-resort',
    _type: 'property',
    title: 'Coastal Beach Resort',
    slug: {
      _type: 'slug',
      current: 'coastal-beach-resort'
    },
    description: 'Relax in paradise at our beachfront resort on the pristine Kenyan coast. Enjoy crystal-clear waters, white sandy beaches, and exceptional hospitality. Our resort features multiple swimming pools, water sports facilities, and world-class dining options. Perfect for families and couples seeking a tropical getaway.',
    price: 12000,
    location: 'Diani Beach, Kenya',
    bedrooms: 3,
    bathrooms: 2,
    maxGuests: 6,
    category: 'beach',
    propertyType: 'villa',
    purpose: 'airbnb',
    amenities: [
      'Direct Beach Access',
      'Swimming Pool',
      'Water Sports Center',
      'Beachfront Restaurant',
      'Pool Bar',
      'Spa Services',
      'Kids Club',
      'Wi-Fi',
      'Air Conditioning',
      'Kitchenette'
    ],
    rules: [
      'No glass containers on the beach',
      'Pool hours: 6 AM to 10 PM',
      'Children must be supervised in pool area',
      'Respect marine life and coral reefs',
      'No loud music after 9 PM'
    ],
    availability: [
      {
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      }
    ],
    images: [
      {
        _type: 'image',
        asset: {
          _ref: 'coastal-beach-resort',
          _type: 'reference'
        }
      }
    ],
    featured: true,
    status: 'available' as const,
    verified: true,
    owner: {
      name: 'City Hotel Owner',
      email: '<EMAIL>',
      phone: '+254 744 567 890'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    _id: 'luxury-city-hotel',
    _type: 'property',
    title: 'Luxury City Hotel',
    slug: {
      _type: 'slug',
      current: 'luxury-city-hotel'
    },
    description: 'Experience urban sophistication in the heart of Nairobi. Our luxury hotel offers modern amenities, exceptional service, and convenient access to the city\'s business district and cultural attractions. Perfect for business travelers and urban explorers.',
    price: 8500,
    location: 'Nairobi, Kenya',
    bedrooms: 1,
    bathrooms: 1,
    maxGuests: 2,
    category: 'city',
    propertyType: 'apartment',
    purpose: 'sale',
    amenities: [
      'City Views',
      'Business Center',
      'Fitness Center',
      'Rooftop Restaurant',
      'Concierge Service',
      'Wi-Fi',
      'Air Conditioning',
      'Room Service',
      'Valet Parking',
      'Airport Shuttle'
    ],
    rules: [
      'No smoking in rooms',
      'Quiet hours from 10 PM to 6 AM',
      'Valid ID required at check-in',
      'No pets allowed',
      'Dress code applies in restaurant'
    ],
    availability: [
      {
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      }
    ],
    images: [
      {
        _type: 'image',
        asset: {
          _ref: 'luxury-city-hotel',
          _type: 'reference'
        }
      }
    ],
    featured: false,
    status: 'available' as const,
    verified: true,
    owner: {
      name: 'Beach Resort Owner',
      email: '<EMAIL>',
      phone: '+254 733 456 789'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    _id: 'modern-family-house-westlands',
    _type: 'property',
    title: 'Modern Family House in Westlands',
    slug: {
      _type: 'slug',
      current: 'modern-family-house-westlands'
    },
    description: 'Beautiful 4-bedroom family house in the prestigious Westlands area. This modern home features an open-plan living area, fitted kitchen, master en-suite, and a lovely garden. Perfect for families looking for a comfortable home in a prime location with easy access to schools, shopping centers, and business districts.',
    price: 120000,
    location: 'Westlands, Nairobi',
    bedrooms: 4,
    bathrooms: 3,
    maxGuests: 8,
    category: 'city',
    propertyType: 'house',
    purpose: 'rental',
    amenities: [
      'Fitted Kitchen',
      'Master En-suite',
      'Private Garden',
      'Parking Space',
      'Security System',
      'Wi-Fi Ready',
      'Water Backup',
      'Generator Backup',
      'DSTV Ready',
      'Close to Schools'
    ],
    rules: [
      'No smoking inside the house',
      'Pets allowed with deposit',
      'Quiet hours from 10 PM to 6 AM',
      'Garden maintenance included',
      'One year minimum lease'
    ],
    availability: [
      {
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      }
    ],
    images: [
      {
        _type: 'image',
        asset: {
          _ref: 'modern-family-house-westlands',
          _type: 'reference'
        }
      }
    ],
    featured: true,
    status: 'available' as const,
    verified: true,
    owner: {
      name: 'Property Management Ltd',
      email: '<EMAIL>',
      phone: '+254 722 345 678'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    _id: 'spacious-house-karen',
    _type: 'property',
    title: 'Spacious House in Karen',
    slug: {
      _type: 'slug',
      current: 'spacious-house-karen'
    },
    description: 'Elegant 5-bedroom house in the leafy suburb of Karen. This property offers spacious living areas, a modern kitchen, beautiful gardens, and a swimming pool. Located in a quiet neighborhood with excellent security, close to international schools and shopping centers.',
    price: 180000,
    location: 'Karen, Nairobi',
    bedrooms: 5,
    bathrooms: 4,
    maxGuests: 10,
    category: 'countryside',
    propertyType: 'house',
    purpose: 'rental',
    amenities: [
      'Swimming Pool',
      'Large Garden',
      'Modern Kitchen',
      'Study Room',
      'Servant Quarter',
      'Double Garage',
      'Borehole Water',
      'Solar Backup',
      'CCTV Security',
      'Electric Fence'
    ],
    rules: [
      'No smoking anywhere on property',
      'Swimming pool maintenance included',
      'Garden maintenance included',
      'Minimum 2-year lease preferred',
      'References required'
    ],
    availability: [
      {
        startDate: '2024-02-01',
        endDate: '2024-12-31'
      }
    ],
    images: ['/images/properties/spacious-house-karen.jpg'],
    featured: true,
    status: 'available' as const,
    verified: true,
    owner: {
      name: 'Karen Properties',
      email: '<EMAIL>',
      phone: '+254 733 456 789'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    _id: 'cozy-house-kileleshwa',
    _type: 'property',
    title: 'Cozy 3-Bedroom House in Kileleshwa',
    slug: {
      _type: 'slug',
      current: 'cozy-house-kileleshwa'
    },
    description: 'Charming 3-bedroom house in the desirable Kileleshwa neighborhood. Features include a well-appointed kitchen, comfortable living spaces, and a small garden. Perfect for small families or professionals seeking a peaceful home close to the city center.',
    price: 85000,
    location: 'Kileleshwa, Nairobi',
    bedrooms: 3,
    bathrooms: 2,
    maxGuests: 6,
    category: 'city',
    propertyType: 'house',
    purpose: 'rental',
    amenities: [
      'Fitted Kitchen',
      'Small Garden',
      'Parking Space',
      'Security Guard',
      'Water Tank',
      'Prepaid Electricity',
      'Close to Matatu Stage',
      'Shopping Centers Nearby',
      'Good Road Access',
      'Quiet Neighborhood'
    ],
    rules: [
      'No loud parties',
      'Pets negotiable',
      'Rent payable monthly in advance',
      'Utilities separate',
      'Minimum 6-month lease'
    ],
    availability: [
      {
        startDate: '2024-01-15',
        endDate: '2024-12-31'
      }
    ],
    images: ['/images/properties/cozy-house-kileleshwa.jpg'],
    featured: false,
    status: 'available' as const,
    verified: true,
    owner: {
      name: 'Kileleshwa Rentals',
      email: '<EMAIL>',
      phone: '+254 744 567 890'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
]

// Sample Tours Data
export const sampleTours: Tour[] = [
  {
    _id: 'kenya-wildlife-safari',
    title: 'Kenya Wildlife Safari',
    slug: { current: 'kenya-wildlife-safari' },
    description: 'Embark on an unforgettable wildlife safari through Kenya\'s most famous national parks. Witness the Big Five in their natural habitat and experience the Great Migration if visiting during the right season. Our expert guides will ensure you have the best wildlife viewing opportunities.',
    shortDescription: 'Experience the Big Five and Great Migration in Kenya\'s premier national parks.',
    images: [
      {
        asset: { url: '/images/kenya-wildlife-safari.jpg' },
        alt: 'Kenya Wildlife Safari'
      }
    ],
    price: 8500,
    duration: 8,
    maxGuests: 8,
    minGuests: 2,
    difficulty: 'easy',
    category: 'Wildlife Safari',
    location: 'Maasai Mara & Amboseli National Parks',
    coordinates: {
      lat: -1.5061,
      lng: 35.1432
    },
    includes: [
      'Professional Safari Guide',
      'Transportation in 4WD Vehicle',
      'Park Entry Fees',
      'Lunch and Refreshments',
      'Bottled Water',
      'Binoculars',
      'Wildlife Spotting Guide'
    ],
    excludes: [
      'Accommodation',
      'Personal Items',
      'Tips for Guide',
      'Travel Insurance',
      'Additional Meals'
    ],
    itinerary: [
      {
        time: '06:00',
        activity: 'Hotel Pickup',
        description: 'Pickup from your hotel in Nairobi or nearby areas'
      },
      {
        time: '08:30',
        activity: 'Maasai Mara Entry',
        description: 'Enter the park and begin game drive'
      },
      {
        time: '12:00',
        activity: 'Lunch Break',
        description: 'Picnic lunch with scenic views'
      },
      {
        time: '13:30',
        activity: 'Afternoon Game Drive',
        description: 'Continue wildlife viewing and photography'
      },
      {
        time: '17:00',
        activity: 'Return Journey',
        description: 'Head back to Nairobi with drop-off at hotel'
      }
    ],
    meetingPoint: 'Hotel lobby or designated pickup point in Nairobi',
    cancellationPolicy: 'Free cancellation up to 24 hours before the tour. 50% refund for cancellations within 24 hours.',
    availability: {
      daysOfWeek: [0, 1, 2, 3, 4, 5, 6], // All days
      times: ['06:00', '07:00'],
      blackoutDates: []
    },
    featured: true,
    active: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    _id: 'cultural-heritage-tour',
    title: 'Cultural Heritage Tour',
    slug: { current: 'cultural-heritage-tour' },
    description: 'Immerse yourself in Kenya\'s rich cultural heritage with this authentic cultural experience. Visit traditional villages, learn about local customs and traditions, participate in craft workshops, and enjoy traditional cuisine. Perfect for travelers seeking meaningful cultural connections.',
    shortDescription: 'Discover Kenya\'s rich cultural heritage through village visits and traditional experiences.',
    images: [
      {
        asset: { url: '/images/kenyan-culture.jpg' },
        alt: 'Kenyan Cultural Heritage'
      }
    ],
    price: 4500,
    duration: 6,
    maxGuests: 12,
    minGuests: 4,
    difficulty: 'easy',
    category: 'Cultural Experience',
    location: 'Central Kenya Villages',
    coordinates: {
      lat: -0.7893,
      lng: 36.8219
    },
    includes: [
      'Cultural Guide',
      'Village Entry Fees',
      'Traditional Lunch',
      'Craft Workshop Participation',
      'Transportation',
      'Cultural Performance',
      'Souvenir Shopping'
    ],
    excludes: [
      'Personal Purchases',
      'Additional Meals',
      'Tips for Guide',
      'Travel Insurance',
      'Hotel Pickup (available for extra cost)'
    ],
    itinerary: [
      {
        time: '09:00',
        activity: 'Cultural Center Arrival',
        description: 'Welcome and introduction to Kenyan cultures'
      },
      {
        time: '10:00',
        activity: 'Village Visit',
        description: 'Tour traditional homesteads and meet locals'
      },
      {
        time: '12:00',
        activity: 'Traditional Lunch',
        description: 'Enjoy authentic Kenyan cuisine'
      },
      {
        time: '13:30',
        activity: 'Craft Workshop',
        description: 'Learn traditional crafts and create souvenirs'
      },
      {
        time: '14:30',
        activity: 'Cultural Performance',
        description: 'Traditional music and dance demonstration'
      },
      {
        time: '15:00',
        activity: 'Tour Conclusion',
        description: 'Farewell and departure'
      }
    ],
    meetingPoint: 'Bomas of Kenya Cultural Center',
    cancellationPolicy: 'Free cancellation up to 48 hours before the tour. No refund for cancellations within 48 hours.',
    availability: {
      daysOfWeek: [1, 2, 3, 4, 5, 6], // Monday to Saturday
      times: ['09:00', '14:00'],
      blackoutDates: ['2024-12-25', '2024-01-01'] // Christmas and New Year
    },
    featured: true,
    active: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
]

// Helper function to get property by slug
export function getPropertyBySlug(slug: string): Property | undefined {
  return sampleProperties.find(property => property.slug.current === slug)
}

// Helper function to get tour by slug
export function getTourBySlug(slug: string): Tour | undefined {
  return sampleTours.find(tour => tour.slug.current === slug)
}

// Helper function to get all featured items
export function getFeaturedItems(): (Property | Tour)[] {
  const featuredProperties = sampleProperties.filter(p => p.featured)
  const featuredTours = sampleTours.filter(t => t.featured)
  return [...featuredProperties, ...featuredTours]
}
