import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'

export interface AnalyticsMetrics {
  totalRevenue: number
  totalBookings: number
  totalProperties: number
  totalUsers: number
  averageBookingValue: number
  occupancyRate: number
  conversionRate: number
  customerRetentionRate: number
}

export interface RevenueData {
  period: string
  revenue: number
  bookings: number
  averageValue: number
}

export interface PropertyPerformance {
  propertyId: string
  title: string
  location: string
  revenue: number
  bookings: number
  occupancyRate: number
  averageRating: number
  views: number
  conversionRate: number
}

export interface UserAnalytics {
  newUsers: number
  activeUsers: number
  returningUsers: number
  usersByLocation: Array<{
    location: string
    count: number
  }>
  usersByAge: Array<{
    ageGroup: string
    count: number
  }>
}

export interface BookingAnalytics {
  totalBookings: number
  confirmedBookings: number
  pendingBookings: number
  cancelledBookings: number
  completedBookings: number
  averageLeadTime: number
  averageStayDuration: number
  seasonalTrends: Array<{
    month: string
    bookings: number
    revenue: number
  }>
}

class AnalyticsService {
  // Get overall platform metrics
  async getOverallMetrics(startDate?: string, endDate?: string): Promise<AnalyticsMetrics> {
    try {
      const dateFilter = startDate && endDate 
        ? ` && createdAt >= "${startDate}" && createdAt <= "${endDate}"`
        : ''

      const [bookings, properties, users] = await Promise.all([
        // Get booking metrics
        client.fetch(groq`
          *[_type == "booking"${dateFilter}] {
            totalPrice,
            status,
            createdAt,
            checkIn,
            checkOut
          }
        `),
        
        // Get property metrics
        client.fetch(groq`
          *[_type == "property"] {
            _id,
            status,
            analytics
          }
        `),
        
        // Get user metrics
        client.fetch(groq`
          *[_type == "user"${dateFilter}] {
            _id,
            createdAt,
            lastLogin
          }
        `)
      ])

      const totalRevenue = bookings
        .filter((b: any) => b.status === 'confirmed' || b.status === 'completed')
        .reduce((sum: number, booking: any) => sum + (booking.totalPrice || 0), 0)

      const totalBookings = bookings.length
      const confirmedBookings = bookings.filter((b: any) => b.status === 'confirmed' || b.status === 'completed').length
      const averageBookingValue = confirmedBookings > 0 ? totalRevenue / confirmedBookings : 0

      // Calculate occupancy rate (simplified)
      const totalProperties = properties.length
      const occupiedDays = bookings
        .filter((b: any) => b.status === 'confirmed' || b.status === 'completed')
        .reduce((sum: number, booking: any) => {
          if (booking.checkIn && booking.checkOut) {
            const days = Math.ceil((new Date(booking.checkOut).getTime() - new Date(booking.checkIn).getTime()) / (1000 * 60 * 60 * 24))
            return sum + days
          }
          return sum
        }, 0)

      const totalAvailableDays = totalProperties * 365 // Simplified calculation
      const occupancyRate = totalAvailableDays > 0 ? (occupiedDays / totalAvailableDays) * 100 : 0

      // Calculate conversion rate (simplified)
      const totalViews = properties.reduce((sum: number, property: any) => 
        sum + (property.analytics?.views || 0), 0)
      const conversionRate = totalViews > 0 ? (confirmedBookings / totalViews) * 100 : 0

      // Calculate customer retention rate (simplified)
      const uniqueCustomers = new Set(bookings.map((b: any) => b.customerId)).size
      const returningCustomers = bookings
        .reduce((acc: any, booking: any) => {
          acc[booking.customerId] = (acc[booking.customerId] || 0) + 1
          return acc
        }, {})
      const returningCustomerCount = Object.values(returningCustomers).filter((count: any) => count > 1).length
      const customerRetentionRate = uniqueCustomers > 0 ? (returningCustomerCount / uniqueCustomers) * 100 : 0

      return {
        totalRevenue,
        totalBookings,
        totalProperties,
        totalUsers: users.length,
        averageBookingValue,
        occupancyRate,
        conversionRate,
        customerRetentionRate
      }
    } catch (error) {
      console.error('Failed to fetch overall metrics:', error)
      throw error
    }
  }

  // Get revenue data over time
  async getRevenueData(period: 'daily' | 'weekly' | 'monthly' | 'yearly', startDate?: string, endDate?: string): Promise<RevenueData[]> {
    try {
      const dateFilter = startDate && endDate 
        ? ` && createdAt >= "${startDate}" && createdAt <= "${endDate}"`
        : ''

      const bookings = await client.fetch(groq`
        *[_type == "booking"${dateFilter} && (status == "confirmed" || status == "completed")] {
          totalPrice,
          createdAt,
          checkIn
        } | order(createdAt asc)
      `)

      // Group bookings by period
      const groupedData: { [key: string]: { revenue: number; bookings: number } } = {}

      bookings.forEach((booking: any) => {
        const date = new Date(booking.createdAt)
        let periodKey: string

        switch (period) {
          case 'daily':
            periodKey = date.toISOString().split('T')[0]
            break
          case 'weekly':
            const weekStart = new Date(date)
            weekStart.setDate(date.getDate() - date.getDay())
            periodKey = weekStart.toISOString().split('T')[0]
            break
          case 'monthly':
            periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
            break
          case 'yearly':
            periodKey = String(date.getFullYear())
            break
          default:
            periodKey = date.toISOString().split('T')[0]
        }

        if (!groupedData[periodKey]) {
          groupedData[periodKey] = { revenue: 0, bookings: 0 }
        }

        groupedData[periodKey].revenue += booking.totalPrice || 0
        groupedData[periodKey].bookings += 1
      })

      return Object.entries(groupedData).map(([period, data]) => ({
        period,
        revenue: data.revenue,
        bookings: data.bookings,
        averageValue: data.bookings > 0 ? data.revenue / data.bookings : 0
      }))
    } catch (error) {
      console.error('Failed to fetch revenue data:', error)
      throw error
    }
  }

  // Get property performance metrics
  async getPropertyPerformance(limit = 10): Promise<PropertyPerformance[]> {
    try {
      const properties = await client.fetch(groq`
        *[_type == "property"] {
          _id,
          title,
          location,
          analytics,
          "bookings": *[_type == "booking" && property._ref == ^._id && (status == "confirmed" || status == "completed")] {
            totalPrice,
            checkIn,
            checkOut
          }
        }
      `)

      const performanceData = properties.map((property: any) => {
        const bookings = property.bookings || []
        const revenue = bookings.reduce((sum: number, booking: any) => sum + (booking.totalPrice || 0), 0)
        const totalBookings = bookings.length

        // Calculate occupancy rate
        const totalDays = bookings.reduce((sum: number, booking: any) => {
          if (booking.checkIn && booking.checkOut) {
            const days = Math.ceil((new Date(booking.checkOut).getTime() - new Date(booking.checkIn).getTime()) / (1000 * 60 * 60 * 24))
            return sum + days
          }
          return sum
        }, 0)
        const occupancyRate = totalDays > 0 ? (totalDays / 365) * 100 : 0

        const views = property.analytics?.views || 0
        const conversionRate = views > 0 ? (totalBookings / views) * 100 : 0

        return {
          propertyId: property._id,
          title: property.title,
          location: property.location,
          revenue,
          bookings: totalBookings,
          occupancyRate,
          averageRating: property.analytics?.averageRating || 0,
          views,
          conversionRate
        }
      })

      // Sort by revenue and return top performers
      return performanceData
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, limit)
    } catch (error) {
      console.error('Failed to fetch property performance:', error)
      throw error
    }
  }

  // Get user analytics
  async getUserAnalytics(startDate?: string, endDate?: string): Promise<UserAnalytics> {
    try {
      const dateFilter = startDate && endDate 
        ? ` && createdAt >= "${startDate}" && createdAt <= "${endDate}"`
        : ''

      const users = await client.fetch(groq`
        *[_type == "user"${dateFilter}] {
          _id,
          createdAt,
          lastLogin,
          profile
        }
      `)

      const now = new Date()
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

      const newUsers = users.filter((user: any) => 
        new Date(user.createdAt) >= thirtyDaysAgo
      ).length

      const activeUsers = users.filter((user: any) => 
        user.lastLogin && new Date(user.lastLogin) >= thirtyDaysAgo
      ).length

      const returningUsers = users.filter((user: any) => 
        user.lastLogin && 
        new Date(user.lastLogin) >= thirtyDaysAgo &&
        new Date(user.createdAt) < thirtyDaysAgo
      ).length

      // Group users by location
      const usersByLocation = users.reduce((acc: any, user: any) => {
        const location = user.profile?.address?.city || 'Unknown'
        acc[location] = (acc[location] || 0) + 1
        return acc
      }, {})

      // Group users by age (simplified)
      const usersByAge = users.reduce((acc: any, user: any) => {
        if (user.profile?.dateOfBirth) {
          const age = new Date().getFullYear() - new Date(user.profile.dateOfBirth).getFullYear()
          let ageGroup = '18-25'
          if (age >= 26 && age <= 35) ageGroup = '26-35'
          else if (age >= 36 && age <= 45) ageGroup = '36-45'
          else if (age >= 46 && age <= 55) ageGroup = '46-55'
          else if (age > 55) ageGroup = '55+'
          
          acc[ageGroup] = (acc[ageGroup] || 0) + 1
        }
        return acc
      }, {})

      return {
        newUsers,
        activeUsers,
        returningUsers,
        usersByLocation: Object.entries(usersByLocation).map(([location, count]) => ({
          location,
          count: count as number
        })),
        usersByAge: Object.entries(usersByAge).map(([ageGroup, count]) => ({
          ageGroup,
          count: count as number
        }))
      }
    } catch (error) {
      console.error('Failed to fetch user analytics:', error)
      throw error
    }
  }

  // Get booking analytics
  async getBookingAnalytics(startDate?: string, endDate?: string): Promise<BookingAnalytics> {
    try {
      const dateFilter = startDate && endDate 
        ? ` && createdAt >= "${startDate}" && createdAt <= "${endDate}"`
        : ''

      const bookings = await client.fetch(groq`
        *[_type == "booking"${dateFilter}] {
          status,
          createdAt,
          checkIn,
          checkOut,
          totalPrice
        }
      `)

      const totalBookings = bookings.length
      const confirmedBookings = bookings.filter((b: any) => b.status === 'confirmed').length
      const pendingBookings = bookings.filter((b: any) => b.status === 'pending').length
      const cancelledBookings = bookings.filter((b: any) => b.status === 'cancelled').length
      const completedBookings = bookings.filter((b: any) => b.status === 'completed').length

      // Calculate average lead time
      const leadTimes = bookings
        .filter((b: any) => b.checkIn && b.createdAt)
        .map((b: any) => {
          const created = new Date(b.createdAt)
          const checkIn = new Date(b.checkIn)
          return Math.ceil((checkIn.getTime() - created.getTime()) / (1000 * 60 * 60 * 24))
        })
      const averageLeadTime = leadTimes.length > 0 
        ? leadTimes.reduce((sum, days) => sum + days, 0) / leadTimes.length 
        : 0

      // Calculate average stay duration
      const stayDurations = bookings
        .filter((b: any) => b.checkIn && b.checkOut)
        .map((b: any) => {
          const checkIn = new Date(b.checkIn)
          const checkOut = new Date(b.checkOut)
          return Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
        })
      const averageStayDuration = stayDurations.length > 0
        ? stayDurations.reduce((sum, days) => sum + days, 0) / stayDurations.length
        : 0

      // Calculate seasonal trends
      const seasonalData: { [key: string]: { bookings: number; revenue: number } } = {}
      bookings.forEach((booking: any) => {
        const month = new Date(booking.createdAt).toLocaleString('default', { month: 'long' })
        if (!seasonalData[month]) {
          seasonalData[month] = { bookings: 0, revenue: 0 }
        }
        seasonalData[month].bookings += 1
        seasonalData[month].revenue += booking.totalPrice || 0
      })

      const seasonalTrends = Object.entries(seasonalData).map(([month, data]) => ({
        month,
        bookings: data.bookings,
        revenue: data.revenue
      }))

      return {
        totalBookings,
        confirmedBookings,
        pendingBookings,
        cancelledBookings,
        completedBookings,
        averageLeadTime,
        averageStayDuration,
        seasonalTrends
      }
    } catch (error) {
      console.error('Failed to fetch booking analytics:', error)
      throw error
    }
  }

  // Generate comprehensive report
  async generateReport(type: 'overview' | 'revenue' | 'properties' | 'users' | 'bookings', startDate?: string, endDate?: string) {
    try {
      const [metrics, revenueData, propertyPerformance, userAnalytics, bookingAnalytics] = await Promise.all([
        this.getOverallMetrics(startDate, endDate),
        this.getRevenueData('monthly', startDate, endDate),
        this.getPropertyPerformance(10),
        this.getUserAnalytics(startDate, endDate),
        this.getBookingAnalytics(startDate, endDate)
      ])

      return {
        type,
        period: { startDate, endDate },
        generatedAt: new Date().toISOString(),
        metrics,
        revenueData,
        propertyPerformance,
        userAnalytics,
        bookingAnalytics
      }
    } catch (error) {
      console.error('Failed to generate report:', error)
      throw error
    }
  }
}

export const analyticsService = new AnalyticsService()
