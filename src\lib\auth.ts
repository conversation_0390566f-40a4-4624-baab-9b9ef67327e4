// Authentication Service
import { User, AuthSession, LoginCredentials, CreateUserRequest } from '@/types/auth'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production'
const JWT_EXPIRES_IN = '7d'
const SALT_ROUNDS = 12

class AuthService {
  // Create user account
  async createUser(userData: CreateUserRequest): Promise<User> {
    // Hash password with bcrypt
    const hashedPassword = await bcrypt.hash(userData.password, SALT_ROUNDS)

    // Create user in Sanity
    const user = await client.create({
      _type: 'user',
      email: userData.email,
      password: hashedPassword,
      firstName: userData.firstName,
      lastName: userData.lastName,
      role: userData.role,
      status: 'active',
      phone: userData.phone,
      department: userData.department,
      permissions: userData.permissions,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    })

    // Remove password from response
    const { password, ...userWithoutPassword } = user
    return userWithoutPassword as User
  }

  // Authenticate user
  async login(credentials: LoginCredentials): Promise<AuthSession> {
    // Find user by email
    const user = await client.fetch(
      groq`*[_type == "user" && email == $email && status == "active"][0]`,
      { email: credentials.email }
    )

    if (!user) {
      throw new Error('Invalid credentials')
    }

    // Verify password with bcrypt
    const isValidPassword = await bcrypt.compare(credentials.password, user.password)
    if (!isValidPassword) {
      throw new Error('Invalid credentials')
    }

    // Update last login
    await client.patch(user._id).set({
      lastLogin: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }).commit()

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user._id,
        email: user.email,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    )

    // Remove password from response
    const { password, ...userWithoutPassword } = user

    return {
      user: userWithoutPassword as User,
      token,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    }
  }

  // Verify JWT token
  async verifyToken(token: string): Promise<User | null> {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as any
      
      // Fetch current user data
      const user = await client.fetch(
        groq`*[_type == "user" && _id == $userId && status == "active"][0]`,
        { userId: decoded.userId }
      )

      if (!user) {
        return null
      }

      // Remove password from response
      const { password, ...userWithoutPassword } = user
      return userWithoutPassword as User
    } catch (error) {
      return null
    }
  }

  // Get user by ID
  async getUserById(userId: string): Promise<User | null> {
    const user = await client.fetch(
      groq`*[_type == "user" && _id == $userId][0]`,
      { userId }
    )

    if (!user) {
      return null
    }

    // Remove password from response
    const { password, ...userWithoutPassword } = user
    return userWithoutPassword as User
  }

  // Update user
  async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    const updatedUser = await client.patch(userId).set({
      ...updates,
      updatedAt: new Date().toISOString(),
    }).commit()

    // Remove password from response
    const { password, ...userWithoutPassword } = updatedUser
    return userWithoutPassword as any
  }

  // Get all users (admin only)
  async getAllUsers(): Promise<User[]> {
    const users = await client.fetch(
      groq`*[_type == "user"] | order(createdAt desc)`
    )

    // Remove passwords from response
    return users.map(({ password, ...user }: any) => user as User)
  }

  // Delete user
  async deleteUser(userId: string): Promise<void> {
    await client.delete(userId)
  }

  // Check user permission
  hasPermission(user: User, permission: string): boolean {
    const [resource, action] = permission.split(':')
    return user.permissions.some(p =>
      p.resource === resource &&
      p.actions.includes(action || '')
    )
  }

  // Check if user has any of the permissions
  hasAnyPermission(user: User, permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(user, permission))
  }

  // Check if user has all permissions
  hasAllPermissions(user: User, permissions: string[]): boolean {
    return permissions.every(permission => this.hasPermission(user, permission))
  }
}

export const authService = new AuthService()

// Middleware helper for API routes
export function withAuth(handler: Function, requiredPermissions?: string[]) {
  return async (req: any, res: any) => {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '')
      
      if (!token) {
        return res.status(401).json({ error: 'No token provided' })
      }

      const user = await authService.verifyToken(token)
      
      if (!user) {
        return res.status(401).json({ error: 'Invalid token' })
      }

      // Check permissions if required
      if (requiredPermissions && requiredPermissions.length > 0) {
        const hasPermission = authService.hasAnyPermission(user, requiredPermissions)
        if (!hasPermission) {
          return res.status(403).json({ error: 'Insufficient permissions' })
        }
      }

      // Add user to request
      req.user = user
      
      return handler(req, res)
    } catch (error) {
      console.error('Auth middleware error:', error)
      return res.status(500).json({ error: 'Authentication failed' })
    }
  }
}

// Client-side auth helpers with safe localStorage access
export function getStoredAuth(): AuthSession | null {
  if (typeof window === 'undefined') return null

  try {
    const stored = window.localStorage.getItem('riftstays_auth')
    return stored ? JSON.parse(stored) : null
  } catch {
    return null
  }
}

export function setStoredAuth(session: AuthSession): void {
  if (typeof window === 'undefined') return

  try {
    window.localStorage.setItem('riftstays_auth', JSON.stringify(session))
  } catch (error) {
    console.warn('Failed to store auth session:', error)
  }
}

export function clearStoredAuth(): void {
  if (typeof window === 'undefined') return

  try {
    window.localStorage.removeItem('riftstays_auth')
  } catch (error) {
    console.warn('Failed to clear auth session:', error)
  }
}

export function isTokenExpired(session: AuthSession): boolean {
  return new Date() > new Date(session.expiresAt)
}
