// Booking Service for RiftStays
import { client } from '@/sanity/lib/client'
import { 
  Booking, 
  BookingRequest, 
  BookingValidation, 
  BookingAvailability,
  BookingPricing,
  Tour
} from '@/types/booking'
import { Property } from '@/types/property'
import { mpesaService } from './mpesa'
import { groq } from 'next-sanity'

class BookingService {
  // Generate unique booking number
  generateBookingNumber(): string {
    const prefix = 'RS'
    const timestamp = Date.now().toString().slice(-8)
    const random = Math.random().toString(36).substring(2, 6).toUpperCase()
    return `${prefix}${timestamp}${random}`
  }

  // Calculate pricing for booking
  async calculatePricing(request: BookingRequest): Promise<BookingPricing> {
    let basePrice = 0
    let nights = 1

    if (request.type === 'tour' && request.tourId) {
      // Get tour pricing
      const tour = await this.getTour(request.tourId)
      if (tour) {
        basePrice = tour.price * (request.guests.adults + request.guests.children)
      }
    } else if (request.propertyId) {
      // Get property pricing
      const property = await this.getProperty(request.propertyId)
      if (property && request.dates) {
        const checkIn = new Date(request.dates.checkIn)
        const checkOut = new Date(request.dates.checkOut)
        nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
        basePrice = property.price * nights
      }
    }

    // Calculate fees and taxes
    const serviceFee = Math.round(basePrice * 0.12) // 12% service fee
    const taxes = Math.round(basePrice * 0.16) // 16% VAT (Kenya)
    
    // Additional services
    let additionalCosts = 0
    if (request.additionalServices?.airportTransfer) {
      additionalCosts += 2500 // KES 2,500 for airport transfer
    }
    if (request.additionalServices?.carRental) {
      additionalCosts += 5000 * nights // KES 5,000 per day
    }
    if (request.additionalServices?.insurance) {
      additionalCosts += Math.round(basePrice * 0.05) // 5% of base price
    }

    const totalPrice = basePrice + serviceFee + taxes + additionalCosts

    return {
      basePrice,
      serviceFee,
      taxes,
      discounts: 0,
      totalPrice,
      currency: 'KES'
    }
  }

  // Validate booking request
  async validateBooking(request: BookingRequest): Promise<BookingValidation> {
    const errors: string[] = []
    const warnings: string[] = []

    // Basic validation
    if (!request.contact.firstName || !request.contact.lastName) {
      errors.push('First name and last name are required')
    }

    if (!request.contact.email || !this.isValidEmail(request.contact.email)) {
      errors.push('Valid email address is required')
    }

    if (!request.contact.phone || !this.isValidPhone(request.contact.phone)) {
      errors.push('Valid phone number is required')
    }

    if (request.guests.adults < 1) {
      errors.push('At least one adult is required')
    }

    // Type-specific validation
    if (request.type === 'tour') {
      if (!request.tourId) {
        errors.push('Tour selection is required')
      }
      if (!request.tourDate) {
        errors.push('Tour date is required')
      }
      if (!request.tourTime) {
        errors.push('Tour time is required')
      }

      // Check tour availability
      if (request.tourId && request.tourDate) {
        const available = await this.checkTourAvailability(request.tourId, request.tourDate, request.tourTime!)
        if (!available) {
          errors.push('Selected tour time is not available')
        }
      }
    } else {
      // Accommodation booking
      if (!request.propertyId) {
        errors.push('Property selection is required')
      }
      if (!request.dates?.checkIn || !request.dates?.checkOut) {
        errors.push('Check-in and check-out dates are required')
      }

      // Check date validity
      if (request.dates?.checkIn && request.dates?.checkOut) {
        const checkIn = new Date(request.dates.checkIn)
        const checkOut = new Date(request.dates.checkOut)
        const today = new Date()
        today.setHours(0, 0, 0, 0)

        if (checkIn < today) {
          errors.push('Check-in date cannot be in the past')
        }

        if (checkOut <= checkIn) {
          errors.push('Check-out date must be after check-in date')
        }

        // Check property availability
        if (request.propertyId) {
          const available = await this.checkPropertyAvailability(
            request.propertyId, 
            request.dates.checkIn, 
            request.dates.checkOut
          )
          if (!available) {
            errors.push('Property is not available for selected dates')
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // Create new booking
  async createBooking(request: BookingRequest): Promise<Booking> {
    // Validate booking
    const validation = await this.validateBooking(request)
    if (!validation.isValid) {
      throw new Error(`Booking validation failed: ${validation.errors.join(', ')}`)
    }

    // Calculate pricing
    const pricing = await this.calculatePricing(request)

    // Create booking object
    const booking: Omit<Booking, '_id'> = {
      bookingNumber: this.generateBookingNumber(),
      type: request.type,
      status: 'pending',
      contact: request.contact,
      pricing,
      payment: {
        method: 'mpesa',
        status: 'pending',
        amount: pricing.totalPrice,
        currency: 'KES'
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...(request.additionalServices && { additionalServices: request.additionalServices }),
      ...(request.specialRequests && { customerNotes: request.specialRequests })
    }

    // Add type-specific details
    if (request.type === 'tour' && request.tourId) {
      const tour = await this.getTour(request.tourId)
      booking.tourBooking = {
        tourId: request.tourId,
        tourName: tour?.title || 'Unknown Tour',
        date: request.tourDate!,
        time: request.tourTime!,
        duration: tour?.duration || 4,
        guests: request.guests,
        ...(request.specialRequests && { specialRequests: request.specialRequests })
      }
    } else if (request.propertyId) {
      const property = await this.getProperty(request.propertyId)
      const checkIn = new Date(request.dates!.checkIn)
      const checkOut = new Date(request.dates!.checkOut)
      const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))

      booking.accommodationBooking = {
        propertyId: request.propertyId,
        propertyName: property?.title || 'Unknown Property',
        propertyType: request.type as 'house' | 'hotel' | 'airbnb',
        dates: {
          checkIn: request.dates!.checkIn,
          checkOut: request.dates!.checkOut,
          nights
        },
        guests: request.guests,
        ...(request.specialRequests && { specialRequests: request.specialRequests })
      }
    }

    // Save to Sanity
    const savedBooking = await client.create({
      _type: 'booking',
      ...booking
    })

    return { ...booking, _id: savedBooking._id } as Booking
  }

  // Initiate payment for booking
  async initiatePayment(bookingId: string, phoneNumber: string): Promise<any> {
    const booking = await this.getBooking(bookingId)
    if (!booking) {
      throw new Error('Booking not found')
    }

    if (booking.payment.status !== 'pending') {
      throw new Error('Payment already processed or booking not in pending state')
    }

    try {
      const stkResponse = await mpesaService.initiateSTKPush({
        phoneNumber,
        amount: booking.pricing.totalPrice,
        accountReference: booking.bookingNumber,
        transactionDesc: `RiftStays ${booking.type} booking - ${booking.bookingNumber}`
      })

      // Update booking with payment request details
      await client.patch(bookingId).set({
        'payment.status': 'processing',
        'payment.mpesaCheckoutRequestId': stkResponse.CheckoutRequestID,
        updatedAt: new Date().toISOString()
      }).commit()

      return stkResponse
    } catch (error) {
      console.error('Payment initiation error:', error)
      throw new Error('Failed to initiate payment')
    }
  }

  // Helper methods
  private async getTour(tourId: string): Promise<Tour | null> {
    return client.fetch(
      groq`*[_type == "tour" && _id == $tourId][0]`,
      { tourId }
    )
  }

  private async getProperty(propertyId: string): Promise<Property | null> {
    return client.fetch(
      groq`*[_type == "property" && _id == $propertyId][0]`,
      { propertyId }
    )
  }

  private async getBooking(bookingId: string): Promise<Booking | null> {
    return client.fetch(
      groq`*[_type == "booking" && _id == $bookingId][0]`,
      { bookingId }
    )
  }

  private async checkTourAvailability(tourId: string, date: string, time: string): Promise<boolean> {
    // Check if tour exists and is active
    const tour = await this.getTour(tourId)
    if (!tour || !tour.active) return false

    // Check if date is not in blackout dates
    if (tour.availability.blackoutDates.includes(date)) return false

    // Check if time is available
    if (!tour.availability.times.includes(time)) return false

    // Check day of week
    const dayOfWeek = new Date(date).getDay()
    if (!tour.availability.daysOfWeek.includes(dayOfWeek)) return false

    // Check existing bookings (simplified - in production, check actual capacity)
    const existingBookings = await client.fetch(
      groq`count(*[_type == "booking" && tourBooking.tourId == $tourId && tourBooking.date == $date && tourBooking.time == $time && status in ["confirmed", "pending"]])`,
      { tourId, date, time }
    )

    return existingBookings < tour.maxGuests
  }

  private async checkPropertyAvailability(propertyId: string, checkIn: string, checkOut: string): Promise<boolean> {
    // Check if property exists
    const property = await this.getProperty(propertyId)
    if (!property) return false

    // Check for overlapping bookings
    const overlappingBookings = await client.fetch(
      groq`count(*[_type == "booking" && accommodationBooking.propertyId == $propertyId && status in ["confirmed", "pending"] && (
        (accommodationBooking.dates.checkIn <= $checkIn && accommodationBooking.dates.checkOut > $checkIn) ||
        (accommodationBooking.dates.checkIn < $checkOut && accommodationBooking.dates.checkOut >= $checkOut) ||
        (accommodationBooking.dates.checkIn >= $checkIn && accommodationBooking.dates.checkOut <= $checkOut)
      )])`,
      { propertyId, checkIn, checkOut }
    )

    return overlappingBookings === 0
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  private isValidPhone(phone: string): boolean {
    // Kenyan phone number validation
    const phoneRegex = /^(\+254|254|0)?[17]\d{8}$/
    return phoneRegex.test(phone.replace(/\s/g, ''))
  }
}

export const bookingService = new BookingService()
