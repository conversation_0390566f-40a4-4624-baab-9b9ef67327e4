import sgMail from '@sendgrid/mail'
import { Resend } from 'resend'
import nodemailer from 'nodemailer'

// Email service configuration
const EMAIL_PROVIDER = process.env.EMAIL_PROVIDER || 'sendgrid' // sendgrid, resend, smtp
const FROM_EMAIL = process.env.FROM_EMAIL || '<EMAIL>'
const FROM_NAME = process.env.FROM_NAME || 'RiftStays'

// Initialize email services
if (EMAIL_PROVIDER === 'sendgrid' && process.env.SENDGRID_API_KEY) {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY)
}

const resend = EMAIL_PROVIDER === 'resend' && process.env.RESEND_API_KEY 
  ? new Resend(process.env.RESEND_API_KEY)
  : null

const smtpTransporter = EMAIL_PROVIDER === 'smtp' && process.env.SMTP_HOST
  ? nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    })
  : null

export interface EmailOptions {
  to: string | string[]
  subject: string
  html?: string
  text?: string
  attachments?: Array<{
    filename: string
    content: Buffer | string
    contentType?: string
  }>
  replyTo?: string
  cc?: string | string[]
  bcc?: string | string[]
}

export interface EmailTemplate {
  subject: string
  html: string
  text?: string
}

// Send email using configured provider
export async function sendEmail(options: EmailOptions): Promise<boolean> {
  try {
    const emailData = {
      from: `${FROM_NAME} <${FROM_EMAIL}>`,
      to: Array.isArray(options.to) ? options.to : [options.to],
      subject: options.subject,
      html: options.html,
      text: options.text,
      replyTo: options.replyTo,
      cc: options.cc,
      bcc: options.bcc
    }

    switch (EMAIL_PROVIDER) {
      case 'sendgrid':
        if (!process.env.SENDGRID_API_KEY) {
          throw new Error('SendGrid API key not configured')
        }
        await sgMail.send({
          ...emailData,
          to: emailData.to[0], // SendGrid expects single recipient
          attachments: options.attachments?.map(att => ({
            filename: att.filename,
            content: att.content.toString('base64'),
            type: att.contentType,
            disposition: 'attachment'
          }))
        })
        break

      case 'resend':
        if (!resend) {
          throw new Error('Resend not configured')
        }
        await resend.emails.send({
          ...emailData,
          attachments: options.attachments?.map(att => ({
            filename: att.filename,
            content: att.content
          }))
        })
        break

      case 'smtp':
        if (!smtpTransporter) {
          throw new Error('SMTP not configured')
        }
        await smtpTransporter.sendMail({
          ...emailData,
          attachments: options.attachments
        })
        break

      default:
        console.log('Email would be sent:', emailData)
        return true // Mock success for development
    }

    return true
  } catch (error) {
    console.error('Email sending failed:', error)
    return false
  }
}

// Email templates
export const emailTemplates = {
  // Welcome email for new users
  welcome: (data: { firstName: string, email: string }): EmailTemplate => ({
    subject: 'Welcome to RiftStays!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #2AAA8A 0%, #1E40AF 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to RiftStays!</h1>
        </div>
        <div style="padding: 40px 20px; background: #f9fafb;">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Hello ${data.firstName}!</h2>
          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
            Thank you for joining RiftStays, Kenya's leading property platform. We're excited to help you find your perfect home or list your property.
          </p>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1f2937; margin-top: 0;">What you can do with RiftStays:</h3>
            <ul style="color: #4b5563; line-height: 1.6;">
              <li>Search thousands of verified properties</li>
              <li>List your property for rent or sale</li>
              <li>Book vacation stays and tours</li>
              <li>Connect with property owners directly</li>
            </ul>
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/properties" 
               style="background: #2AAA8A; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Start Exploring Properties
            </a>
          </div>
          <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
            If you have any questions, feel free to contact our support team.
          </p>
        </div>
      </div>
    `,
    text: `Welcome to RiftStays, ${data.firstName}! Thank you for joining Kenya's leading property platform.`
  }),

  // Booking confirmation
  bookingConfirmation: (data: {
    firstName: string
    bookingId: string
    propertyTitle: string
    checkIn: string
    checkOut: string
    totalAmount: number
  }): EmailTemplate => ({
    subject: `Booking Confirmed - ${data.propertyTitle}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #10b981; padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">Booking Confirmed!</h1>
        </div>
        <div style="padding: 40px 20px; background: #f9fafb;">
          <h2 style="color: #1f2937;">Hello ${data.firstName}!</h2>
          <p style="color: #4b5563; line-height: 1.6;">
            Your booking has been confirmed. Here are the details:
          </p>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #1f2937;">Booking Details</h3>
            <p><strong>Booking ID:</strong> ${data.bookingId}</p>
            <p><strong>Property:</strong> ${data.propertyTitle}</p>
            <p><strong>Check-in:</strong> ${data.checkIn}</p>
            <p><strong>Check-out:</strong> ${data.checkOut}</p>
            <p><strong>Total Amount:</strong> KES ${data.totalAmount.toLocaleString()}</p>
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/profile/bookings" 
               style="background: #2AAA8A; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              View Booking Details
            </a>
          </div>
        </div>
      </div>
    `,
    text: `Booking confirmed for ${data.propertyTitle}. Booking ID: ${data.bookingId}`
  }),

  // Property approval notification
  propertyApproved: (data: {
    firstName: string
    propertyTitle: string
    propertyId: string
  }): EmailTemplate => ({
    subject: 'Property Approved - Now Live on RiftStays',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #10b981; padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">Property Approved!</h1>
        </div>
        <div style="padding: 40px 20px; background: #f9fafb;">
          <h2 style="color: #1f2937;">Great news, ${data.firstName}!</h2>
          <p style="color: #4b5563; line-height: 1.6;">
            Your property "${data.propertyTitle}" has been approved and is now live on RiftStays.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/properties/${data.propertyId}" 
               style="background: #2AAA8A; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              View Your Property
            </a>
          </div>
        </div>
      </div>
    `,
    text: `Your property "${data.propertyTitle}" has been approved and is now live on RiftStays.`
  }),

  // Password reset
  passwordReset: (data: {
    firstName: string
    resetLink: string
  }): EmailTemplate => ({
    subject: 'Reset Your RiftStays Password',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #1E40AF; padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">Password Reset</h1>
        </div>
        <div style="padding: 40px 20px; background: #f9fafb;">
          <h2 style="color: #1f2937;">Hello ${data.firstName}!</h2>
          <p style="color: #4b5563; line-height: 1.6;">
            You requested to reset your password. Click the button below to create a new password:
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.resetLink}"
               style="background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Reset Password
            </a>
          </div>
          <p style="color: #6b7280; font-size: 14px;">
            This link will expire in 1 hour. If you didn't request this, please ignore this email.
          </p>
        </div>
      </div>
    `,
    text: `Reset your password: ${data.resetLink}`
  }),

  // Generic notification
  notification: (data: {
    firstName: string
    title: string
    message: string
    type: string
  }): EmailTemplate => ({
    subject: data.title,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #2AAA8A; padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">${data.title}</h1>
        </div>
        <div style="padding: 40px 20px; background: #f9fafb;">
          <h2 style="color: #1f2937;">Hello ${data.firstName}!</h2>
          <p style="color: #4b5563; line-height: 1.6;">
            ${data.message}
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/profile"
               style="background: #2AAA8A; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              View in Dashboard
            </a>
          </div>
        </div>
      </div>
    `,
    text: `${data.title}: ${data.message}`
  })
}

// Send templated email
export async function sendTemplatedEmail(
  template: keyof typeof emailTemplates,
  to: string | string[],
  data: any
): Promise<boolean> {
  const emailTemplate = emailTemplates[template](data)
  
  return sendEmail({
    to,
    subject: emailTemplate.subject,
    html: emailTemplate.html,
    text: emailTemplate.text
  })
}

// Bulk email sending
export async function sendBulkEmail(
  recipients: string[],
  subject: string,
  html: string,
  text?: string
): Promise<{ sent: number; failed: number }> {
  let sent = 0
  let failed = 0
  
  // Send in batches to avoid rate limits
  const batchSize = 10
  for (let i = 0; i < recipients.length; i += batchSize) {
    const batch = recipients.slice(i, i + batchSize)
    
    const promises = batch.map(async (email) => {
      const success = await sendEmail({ to: email, subject, html, text })
      return success ? 'sent' : 'failed'
    })
    
    const results = await Promise.all(promises)
    sent += results.filter(r => r === 'sent').length
    failed += results.filter(r => r === 'failed').length
    
    // Add delay between batches
    if (i + batchSize < recipients.length) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }
  
  return { sent, failed }
}
