// Safe formatting utilities to prevent hydration mismatches
// These utilities ensure consistent formatting between server and client

/**
 * Safe currency formatter that prevents hydration mismatches
 * Uses a consistent locale and handles SSR gracefully
 */
export function formatCurrency(amount: number, currency: string = 'KES'): string {
  // Use a consistent locale to prevent hydration mismatches
  try {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  } catch (error) {
    // Fallback for environments where Intl is not available
    return `${currency} ${amount.toLocaleString('en-US')}`
  }
}

/**
 * Safe number formatter that prevents hydration mismatches
 */
export function formatNumber(num: number): string {
  try {
    return new Intl.NumberFormat('en-KE').format(num)
  } catch (error) {
    return num.toLocaleString('en-US')
  }
}

/**
 * Safe percentage formatter that prevents hydration mismatches
 */
export function formatPercentage(value: number): string {
  try {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    }).format(value)
  } catch (error) {
    return `${(value * 100).toFixed(1)}%`
  }
}

/**
 * Safe date formatter that prevents hydration mismatches
 * Uses consistent formatting regardless of user's locale
 */
export function formatDate(dateString: string | Date): string {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  } catch (error) {
    return 'Invalid date'
  }
}

/**
 * Safe date and time formatter
 */
export function formatDateTime(dateString: string | Date): string {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  } catch (error) {
    return 'Invalid date'
  }
}

/**
 * Safe time formatter
 */
export function formatTime(timeString: string): string {
  try {
    const date = new Date(`2000-01-01T${timeString}`)
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  } catch (error) {
    return timeString
  }
}

/**
 * Safe relative time formatter (e.g., "2 hours ago")
 */
export function formatRelativeTime(dateString: string | Date): string {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString
    const now = new Date()
    const diffInMs = now.getTime() - date.getTime()
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60))
    const diffInHours = Math.floor(diffInMinutes / 60)
    const diffInDays = Math.floor(diffInHours / 24)

    if (diffInMinutes < 1) {
      return 'Just now'
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`
    } else if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`
    } else {
      return formatDate(date)
    }
  } catch (error) {
    return 'Unknown time'
  }
}

/**
 * Safe price formatter for display (without currency symbol)
 */
export function formatPrice(price: number): string {
  try {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price)
  } catch (error) {
    return price.toString()
  }
}
