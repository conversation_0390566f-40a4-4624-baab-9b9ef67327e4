// M-Pesa Integration for RiftStays
// Using Safaricom Daraja API (Sandbox for development)

interface MpesaConfig {
  consumerKey: string
  consumerSecret: string
  environment: 'sandbox' | 'production'
  shortCode: string
  passkey: string
  callbackUrl: string
}

interface STKPushRequest {
  phoneNumber: string
  amount: number
  accountReference: string
  transactionDesc: string
}

interface STKPushResponse {
  MerchantRequestID: string
  CheckoutRequestID: string
  ResponseCode: string
  ResponseDescription: string
  CustomerMessage: string
}

interface PaymentStatus {
  success: boolean
  transactionId?: string
  message: string
  amount?: number
  phoneNumber?: string
}

class MpesaService {
  private config: MpesaConfig
  private accessToken: string | null = null
  private tokenExpiry: Date | null = null

  constructor() {
    this.config = {
      consumerKey: process.env.MPESA_CONSUMER_KEY || '',
      consumerSecret: process.env.MPESA_CONSUMER_SECRET || '',
      environment: (process.env.MPESA_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
      shortCode: process.env.MPESA_SHORT_CODE || '174379',
      passkey: process.env.MPESA_PASSKEY || '',
      callbackUrl: process.env.MPESA_CALLBACK_URL || `${process.env.NEXT_PUBLIC_SITE_URL}/api/payments/mpesa/callback`,
    }
  }

  private getBaseUrl(): string {
    return this.config.environment === 'sandbox'
      ? 'https://sandbox.safaricom.co.ke'
      : 'https://api.safaricom.co.ke'
  }

  private async getAccessToken(): Promise<string> {
    // Return cached token if still valid
    if (this.accessToken && this.tokenExpiry && new Date() < this.tokenExpiry) {
      return this.accessToken
    }

    const auth = Buffer.from(`${this.config.consumerKey}:${this.config.consumerSecret}`).toString('base64')
    
    try {
      const response = await fetch(`${this.getBaseUrl()}/oauth/v1/generate?grant_type=client_credentials`, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to get access token: ${response.statusText}`)
      }

      const data = await response.json()
      this.accessToken = data.access_token
      // Token expires in 1 hour, cache for 50 minutes
      this.tokenExpiry = new Date(Date.now() + 50 * 60 * 1000)

      return this.accessToken!
    } catch (error) {
      console.error('M-Pesa access token error:', error)
      throw new Error('Failed to authenticate with M-Pesa')
    }
  }

  private generateTimestamp(): string {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hour = String(now.getHours()).padStart(2, '0')
    const minute = String(now.getMinutes()).padStart(2, '0')
    const second = String(now.getSeconds()).padStart(2, '0')
    
    return `${year}${month}${day}${hour}${minute}${second}`
  }

  private generatePassword(timestamp: string): string {
    const data = `${this.config.shortCode}${this.config.passkey}${timestamp}`
    return Buffer.from(data).toString('base64')
  }

  async initiateSTKPush(request: STKPushRequest): Promise<STKPushResponse> {
    const accessToken = await this.getAccessToken()
    const timestamp = this.generateTimestamp()
    const password = this.generatePassword(timestamp)

    // Format phone number (remove + and ensure it starts with 254)
    let phoneNumber = request.phoneNumber.replace(/\D/g, '')
    if (phoneNumber.startsWith('0')) {
      phoneNumber = '254' + phoneNumber.substring(1)
    } else if (phoneNumber.startsWith('7') || phoneNumber.startsWith('1')) {
      phoneNumber = '254' + phoneNumber
    }

    const payload = {
      BusinessShortCode: this.config.shortCode,
      Password: password,
      Timestamp: timestamp,
      TransactionType: 'CustomerPayBillOnline',
      Amount: Math.round(request.amount),
      PartyA: phoneNumber,
      PartyB: this.config.shortCode,
      PhoneNumber: phoneNumber,
      CallBackURL: this.config.callbackUrl,
      AccountReference: request.accountReference,
      TransactionDesc: request.transactionDesc,
    }

    try {
      const response = await fetch(`${this.getBaseUrl()}/mpesa/stkpush/v1/processrequest`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        throw new Error(`STK Push failed: ${response.statusText}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('STK Push error:', error)
      throw new Error('Failed to initiate M-Pesa payment')
    }
  }

  async querySTKStatus(checkoutRequestId: string): Promise<PaymentStatus> {
    const accessToken = await this.getAccessToken()
    const timestamp = this.generateTimestamp()
    const password = this.generatePassword(timestamp)

    const payload = {
      BusinessShortCode: this.config.shortCode,
      Password: password,
      Timestamp: timestamp,
      CheckoutRequestID: checkoutRequestId,
    }

    try {
      const response = await fetch(`${this.getBaseUrl()}/mpesa/stkpushquery/v1/query`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        throw new Error(`STK Query failed: ${response.statusText}`)
      }

      const data = await response.json()
      
      return {
        success: data.ResultCode === '0',
        message: data.ResultDesc || 'Payment status unknown',
        transactionId: data.MpesaReceiptNumber,
      }
    } catch (error) {
      console.error('STK Query error:', error)
      return {
        success: false,
        message: 'Failed to check payment status',
      }
    }
  }

  // Validate M-Pesa callback
  validateCallback(callbackData: any): PaymentStatus {
    try {
      const { Body } = callbackData
      const { stkCallback } = Body

      if (stkCallback.ResultCode === 0) {
        // Payment successful
        const callbackMetadata = stkCallback.CallbackMetadata?.Item || []
        const amount = callbackMetadata.find((item: any) => item.Name === 'Amount')?.Value
        const transactionId = callbackMetadata.find((item: any) => item.Name === 'MpesaReceiptNumber')?.Value
        const phoneNumber = callbackMetadata.find((item: any) => item.Name === 'PhoneNumber')?.Value

        return {
          success: true,
          transactionId,
          amount,
          phoneNumber,
          message: 'Payment completed successfully',
        }
      } else {
        // Payment failed
        return {
          success: false,
          message: stkCallback.ResultDesc || 'Payment failed',
        }
      }
    } catch (error) {
      console.error('Callback validation error:', error)
      return {
        success: false,
        message: 'Invalid callback data',
      }
    }
  }
}

export const mpesaService = new MpesaService()
export type { STKPushRequest, STKPushResponse, PaymentStatus }
