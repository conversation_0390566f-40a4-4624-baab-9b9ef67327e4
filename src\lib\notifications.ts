import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'

export interface Notification {
  _id: string
  type: 'info' | 'warning' | 'success' | 'error'
  title: string
  message: string
  category: 'booking' | 'payment' | 'property' | 'user' | 'system' | 'tour'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  read: boolean
  actionRequired: boolean
  userId?: string // If notification is for specific user
  adminOnly: boolean
  data?: any // Additional data for the notification
  createdAt: string
  readAt?: string
  expiresAt?: string
}

export interface NotificationPreferences {
  email: boolean
  sms: boolean
  push: boolean
  inApp: boolean
  categories: {
    booking: boolean
    payment: boolean
    property: boolean
    user: boolean
    system: boolean
    tour: boolean
  }
}

class NotificationService {
  // Create a new notification
  async createNotification(notification: Omit<Notification, '_id' | 'createdAt' | 'read'>): Promise<Notification> {
    try {
      const newNotification = {
        _type: 'notification',
        ...notification,
        read: false,
        createdAt: new Date().toISOString()
      }

      const result = await client.create(newNotification)
      
      // Send real-time notification if needed
      await this.sendRealTimeNotification(result)
      
      return result
    } catch (error) {
      console.error('Failed to create notification:', error)
      throw error
    }
  }

  // Get notifications for a user or admin
  async getNotifications(
    userId?: string, 
    adminOnly = false, 
    filters?: {
      category?: string
      priority?: string
      read?: boolean
      limit?: number
      offset?: number
    }
  ): Promise<Notification[]> {
    try {
      let query = `*[_type == "notification"`
      const params: any = {}

      // Filter by user or admin
      if (userId) {
        query += ` && (userId == $userId || adminOnly == true)`
        params.userId = userId
      } else if (adminOnly) {
        query += ` && adminOnly == true`
      }

      // Apply filters
      if (filters?.category) {
        query += ` && category == $category`
        params.category = filters.category
      }

      if (filters?.priority) {
        query += ` && priority == $priority`
        params.priority = filters.priority
      }

      if (filters?.read !== undefined) {
        query += ` && read == $read`
        params.read = filters.read
      }

      // Check expiration
      query += ` && (!defined(expiresAt) || expiresAt > now())`

      query += `] | order(createdAt desc)`

      // Apply pagination
      if (filters?.limit) {
        const offset = filters.offset || 0
        query += `[${offset}...${offset + filters.limit}]`
      }

      return await client.fetch(groq`${query}`, params)
    } catch (error) {
      console.error('Failed to fetch notifications:', error)
      throw error
    }
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    try {
      await client
        .patch(notificationId)
        .set({ 
          read: true, 
          readAt: new Date().toISOString() 
        })
        .commit()
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
      throw error
    }
  }

  // Mark all notifications as read for a user
  async markAllAsRead(userId?: string, adminOnly = false): Promise<void> {
    try {
      let query = `*[_type == "notification" && read == false`
      const params: any = {}

      if (userId) {
        query += ` && (userId == $userId || adminOnly == true)`
        params.userId = userId
      } else if (adminOnly) {
        query += ` && adminOnly == true`
      }

      query += `]._id`

      const notificationIds = await client.fetch(groq`${query}`, params)

      if (notificationIds.length > 0) {
        const transaction = client.transaction()
        notificationIds.forEach((id: string) => {
          transaction.patch(id).set({ 
            read: true, 
            readAt: new Date().toISOString() 
          })
        })
        await transaction.commit()
      }
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
      throw error
    }
  }

  // Delete notification
  async deleteNotification(notificationId: string): Promise<void> {
    try {
      await client.delete(notificationId)
    } catch (error) {
      console.error('Failed to delete notification:', error)
      throw error
    }
  }

  // Get notification count
  async getNotificationCount(userId?: string, adminOnly = false, unreadOnly = false): Promise<number> {
    try {
      let query = `count(*[_type == "notification"`
      const params: any = {}

      if (userId) {
        query += ` && (userId == $userId || adminOnly == true)`
        params.userId = userId
      } else if (adminOnly) {
        query += ` && adminOnly == true`
      }

      if (unreadOnly) {
        query += ` && read == false`
      }

      query += ` && (!defined(expiresAt) || expiresAt > now())])`

      return await client.fetch(groq`${query}`, params)
    } catch (error) {
      console.error('Failed to get notification count:', error)
      throw error
    }
  }

  // Send real-time notification (WebSocket, SSE, etc.)
  private async sendRealTimeNotification(notification: Notification): Promise<void> {
    try {
      // Implement real-time notification sending
      // This could be WebSocket, Server-Sent Events, or push notifications
      console.log('Sending real-time notification:', notification.title)
      
      // Example: Send to WebSocket clients
      // websocketService.broadcast('notification', notification)
      
      // Example: Send push notification
      // if (notification.userId) {
      //   await pushNotificationService.send(notification.userId, notification)
      // }
    } catch (error) {
      console.error('Failed to send real-time notification:', error)
    }
  }

  // Create booking-related notifications
  async createBookingNotification(
    type: 'created' | 'confirmed' | 'cancelled' | 'completed' | 'payment_failed',
    bookingId: string,
    userId: string,
    bookingData: any
  ): Promise<void> {
    const notifications: Array<Omit<Notification, '_id' | 'createdAt' | 'read'>> = []

    switch (type) {
      case 'created':
        notifications.push({
          type: 'info',
          title: 'New Booking Request',
          message: `New booking request received for ${bookingData.propertyTitle}`,
          category: 'booking',
          priority: 'medium',
          adminOnly: true,
          actionRequired: true,
          data: { bookingId, ...bookingData }
        })
        
        notifications.push({
          type: 'success',
          title: 'Booking Request Submitted',
          message: `Your booking request for ${bookingData.propertyTitle} has been submitted`,
          category: 'booking',
          priority: 'medium',
          userId,
          adminOnly: false,
          actionRequired: false,
          data: { bookingId, ...bookingData }
        })
        break

      case 'confirmed':
        notifications.push({
          type: 'success',
          title: 'Booking Confirmed',
          message: `Booking #${bookingData.bookingNumber} has been confirmed`,
          category: 'booking',
          priority: 'high',
          userId,
          adminOnly: false,
          actionRequired: false,
          data: { bookingId, ...bookingData }
        })
        break

      case 'payment_failed':
        notifications.push({
          type: 'error',
          title: 'Payment Failed',
          message: `Payment for booking #${bookingData.bookingNumber} has failed`,
          category: 'payment',
          priority: 'high',
          userId,
          adminOnly: false,
          actionRequired: true,
          data: { bookingId, ...bookingData }
        })
        
        notifications.push({
          type: 'warning',
          title: 'Payment Failed - Action Required',
          message: `Payment failed for booking #${bookingData.bookingNumber}. Customer needs assistance.`,
          category: 'payment',
          priority: 'high',
          adminOnly: true,
          actionRequired: true,
          data: { bookingId, ...bookingData }
        })
        break
    }

    // Create all notifications
    for (const notification of notifications) {
      await this.createNotification(notification)
    }
  }

  // Create property-related notifications
  async createPropertyNotification(
    type: 'listed' | 'updated' | 'deactivated' | 'review_received',
    propertyId: string,
    propertyData: any,
    userId?: string
  ): Promise<void> {
    const notifications: Array<Omit<Notification, '_id' | 'createdAt' | 'read'>> = []

    switch (type) {
      case 'listed':
        notifications.push({
          type: 'success',
          title: 'Property Listed Successfully',
          message: `${propertyData.title} has been listed and is now available for booking`,
          category: 'property',
          priority: 'medium',
          adminOnly: true,
          actionRequired: false,
          data: { propertyId, ...propertyData }
        })
        break

      case 'review_received':
        notifications.push({
          type: 'info',
          title: 'New Review Received',
          message: `${propertyData.title} received a new ${propertyData.rating}-star review`,
          category: 'property',
          priority: 'low',
          adminOnly: true,
          actionRequired: false,
          data: { propertyId, ...propertyData }
        })
        
        if (userId) {
          notifications.push({
            type: 'info',
            title: 'Review Posted',
            message: `Your review for ${propertyData.title} has been posted`,
            category: 'property',
            priority: 'low',
            userId,
            adminOnly: false,
            actionRequired: false,
            data: { propertyId, ...propertyData }
          })
        }
        break
    }

    for (const notification of notifications) {
      await this.createNotification(notification)
    }
  }

  // Create system notifications
  async createSystemNotification(
    type: 'maintenance' | 'update' | 'alert' | 'announcement',
    title: string,
    message: string,
    priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium',
    data?: any
  ): Promise<void> {
    await this.createNotification({
      type: type === 'alert' ? 'warning' : 'info',
      title,
      message,
      category: 'system',
      priority,
      adminOnly: true,
      actionRequired: type === 'alert',
      data
    })
  }

  // Clean up expired notifications
  async cleanupExpiredNotifications(): Promise<void> {
    try {
      const expiredNotifications = await client.fetch(groq`
        *[_type == "notification" && defined(expiresAt) && expiresAt < now()]._id
      `)

      if (expiredNotifications.length > 0) {
        const transaction = client.transaction()
        expiredNotifications.forEach((id: string) => {
          transaction.delete(id)
        })
        await transaction.commit()
        
        console.log(`Cleaned up ${expiredNotifications.length} expired notifications`)
      }
    } catch (error) {
      console.error('Failed to cleanup expired notifications:', error)
    }
  }

  // Get notification preferences for a user
  async getNotificationPreferences(userId: string): Promise<NotificationPreferences> {
    try {
      const user = await client.fetch(groq`
        *[_type == "user" && _id == $userId][0] {
          notificationPreferences
        }
      `, { userId })

      return user?.notificationPreferences || {
        email: true,
        sms: false,
        push: true,
        inApp: true,
        categories: {
          booking: true,
          payment: true,
          property: true,
          user: false,
          system: true,
          tour: true
        }
      }
    } catch (error) {
      console.error('Failed to get notification preferences:', error)
      throw error
    }
  }

  // Update notification preferences
  async updateNotificationPreferences(userId: string, preferences: NotificationPreferences): Promise<void> {
    try {
      await client
        .patch(userId)
        .set({ notificationPreferences: preferences })
        .commit()
    } catch (error) {
      console.error('Failed to update notification preferences:', error)
      throw error
    }
  }
}

export const notificationService = new NotificationService()
