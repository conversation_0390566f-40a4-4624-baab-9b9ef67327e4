import Stripe from 'stripe'
import axios from 'axios'
import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20'
})

// M-Pesa configuration
const MPESA_CONSUMER_KEY = process.env.MPESA_CONSUMER_KEY!
const MPESA_CONSUMER_SECRET = process.env.MPESA_CONSUMER_SECRET!
const MPESA_SHORTCODE = process.env.MPESA_SHORTCODE!
const MPESA_PASSKEY = process.env.MPESA_PASSKEY!
const MPESA_ENVIRONMENT = process.env.MPESA_ENVIRONMENT || 'sandbox' // sandbox or production

const MPESA_BASE_URL = MPESA_ENVIRONMENT === 'production' 
  ? 'https://api.safaricom.co.ke' 
  : 'https://sandbox.safaricom.co.ke'

export interface PaymentIntent {
  id: string
  amount: number
  currency: string
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'canceled'
  paymentMethod: 'stripe' | 'mpesa'
  metadata?: Record<string, any>
  clientSecret?: string
}

export interface MPesaPayment {
  phoneNumber: string
  amount: number
  accountReference: string
  transactionDesc: string
  callbackUrl?: string
}

export interface PaymentResult {
  success: boolean
  paymentId: string
  status: string
  message?: string
  data?: any
}

// Stripe Payment Methods
export class StripePaymentService {
  // Create payment intent
  static async createPaymentIntent(
    amount: number,
    currency: string = 'kes',
    metadata: Record<string, any> = {}
  ): Promise<PaymentIntent> {
    try {
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency: currency.toLowerCase(),
        metadata,
        automatic_payment_methods: {
          enabled: true
        }
      })

      return {
        id: paymentIntent.id,
        amount: amount,
        currency,
        status: paymentIntent.status as any,
        paymentMethod: 'stripe',
        metadata,
        clientSecret: paymentIntent.client_secret!
      }
    } catch (error) {
      console.error('Stripe payment intent creation failed:', error)
      throw new Error('Failed to create payment intent')
    }
  }

  // Confirm payment
  static async confirmPayment(paymentIntentId: string): Promise<PaymentResult> {
    try {
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)
      
      return {
        success: paymentIntent.status === 'succeeded',
        paymentId: paymentIntent.id,
        status: paymentIntent.status,
        data: paymentIntent
      }
    } catch (error) {
      console.error('Stripe payment confirmation failed:', error)
      return {
        success: false,
        paymentId: paymentIntentId,
        status: 'failed',
        message: 'Payment confirmation failed'
      }
    }
  }

  // Create refund
  static async createRefund(
    paymentIntentId: string,
    amount?: number,
    reason?: string
  ): Promise<PaymentResult> {
    try {
      const refund = await stripe.refunds.create({
        payment_intent: paymentIntentId,
        amount: amount ? Math.round(amount * 100) : undefined,
        reason: reason as any
      })

      return {
        success: refund.status === 'succeeded',
        paymentId: refund.id,
        status: refund.status,
        data: refund
      }
    } catch (error) {
      console.error('Stripe refund failed:', error)
      return {
        success: false,
        paymentId: paymentIntentId,
        status: 'failed',
        message: 'Refund failed'
      }
    }
  }
}

// M-Pesa Payment Methods
export class MPesaPaymentService {
  // Get OAuth token
  static async getAccessToken(): Promise<string> {
    try {
      const auth = Buffer.from(`${MPESA_CONSUMER_KEY}:${MPESA_CONSUMER_SECRET}`).toString('base64')
      
      const response = await axios.get(`${MPESA_BASE_URL}/oauth/v1/generate?grant_type=client_credentials`, {
        headers: {
          'Authorization': `Basic ${auth}`
        }
      })

      return response.data.access_token
    } catch (error) {
      console.error('M-Pesa token generation failed:', error)
      throw new Error('Failed to get M-Pesa access token')
    }
  }

  // Generate timestamp
  static generateTimestamp(): string {
    const now = new Date()
    return now.getFullYear().toString() +
           (now.getMonth() + 1).toString().padStart(2, '0') +
           now.getDate().toString().padStart(2, '0') +
           now.getHours().toString().padStart(2, '0') +
           now.getMinutes().toString().padStart(2, '0') +
           now.getSeconds().toString().padStart(2, '0')
  }

  // Generate password
  static generatePassword(timestamp: string): string {
    const data = MPESA_SHORTCODE + MPESA_PASSKEY + timestamp
    return Buffer.from(data).toString('base64')
  }

  // Initiate STK Push
  static async initiateSTKPush(payment: MPesaPayment): Promise<PaymentResult> {
    try {
      const accessToken = await this.getAccessToken()
      const timestamp = this.generateTimestamp()
      const password = this.generatePassword(timestamp)

      // Format phone number (remove + and ensure it starts with 254)
      let phoneNumber = payment.phoneNumber.replace(/\D/g, '')
      if (phoneNumber.startsWith('0')) {
        phoneNumber = '254' + phoneNumber.substring(1)
      } else if (phoneNumber.startsWith('7') || phoneNumber.startsWith('1')) {
        phoneNumber = '254' + phoneNumber
      }

      const requestData = {
        BusinessShortCode: MPESA_SHORTCODE,
        Password: password,
        Timestamp: timestamp,
        TransactionType: 'CustomerPayBillOnline',
        Amount: Math.round(payment.amount),
        PartyA: phoneNumber,
        PartyB: MPESA_SHORTCODE,
        PhoneNumber: phoneNumber,
        CallBackURL: payment.callbackUrl || `${process.env.NEXT_PUBLIC_APP_URL}/api/payments/mpesa/callback`,
        AccountReference: payment.accountReference,
        TransactionDesc: payment.transactionDesc
      }

      const response = await axios.post(
        `${MPESA_BASE_URL}/mpesa/stkpush/v1/processrequest`,
        requestData,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      )

      const responseData = response.data

      if (responseData.ResponseCode === '0') {
        return {
          success: true,
          paymentId: responseData.CheckoutRequestID,
          status: 'pending',
          message: responseData.ResponseDescription,
          data: responseData
        }
      } else {
        return {
          success: false,
          paymentId: '',
          status: 'failed',
          message: responseData.ResponseDescription || 'STK Push failed'
        }
      }
    } catch (error) {
      console.error('M-Pesa STK Push failed:', error)
      return {
        success: false,
        paymentId: '',
        status: 'failed',
        message: 'STK Push initiation failed'
      }
    }
  }

  // Query STK Push status
  static async querySTKPushStatus(checkoutRequestId: string): Promise<PaymentResult> {
    try {
      const accessToken = await this.getAccessToken()
      const timestamp = this.generateTimestamp()
      const password = this.generatePassword(timestamp)

      const requestData = {
        BusinessShortCode: MPESA_SHORTCODE,
        Password: password,
        Timestamp: timestamp,
        CheckoutRequestID: checkoutRequestId
      }

      const response = await axios.post(
        `${MPESA_BASE_URL}/mpesa/stkpushquery/v1/query`,
        requestData,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      )

      const responseData = response.data

      return {
        success: responseData.ResponseCode === '0',
        paymentId: checkoutRequestId,
        status: responseData.ResultCode === '0' ? 'succeeded' : 'failed',
        message: responseData.ResponseDescription,
        data: responseData
      }
    } catch (error) {
      console.error('M-Pesa status query failed:', error)
      return {
        success: false,
        paymentId: checkoutRequestId,
        status: 'failed',
        message: 'Status query failed'
      }
    }
  }
}

// Unified Payment Service
export class PaymentService {
  // Create payment based on method
  static async createPayment(
    method: 'stripe' | 'mpesa',
    amount: number,
    data: any
  ): Promise<PaymentResult> {
    try {
      if (method === 'stripe') {
        const paymentIntent = await StripePaymentService.createPaymentIntent(
          amount,
          'kes',
          data.metadata || {}
        )
        
        return {
          success: true,
          paymentId: paymentIntent.id,
          status: paymentIntent.status,
          data: paymentIntent
        }
      } else if (method === 'mpesa') {
        return await MPesaPaymentService.initiateSTKPush(data)
      } else {
        throw new Error('Unsupported payment method')
      }
    } catch (error) {
      console.error('Payment creation failed:', error)
      return {
        success: false,
        paymentId: '',
        status: 'failed',
        message: error instanceof Error ? error.message : 'Payment failed'
      }
    }
  }

  // Save payment record to database
  static async savePaymentRecord(paymentData: {
    paymentId: string
    bookingId: string
    userId: string
    amount: number
    currency: string
    method: string
    status: string
    metadata?: any
  }): Promise<void> {
    try {
      await client.create({
        _type: 'payment',
        paymentId: paymentData.paymentId,
        booking: { _type: 'reference', _ref: paymentData.bookingId },
        user: { _type: 'reference', _ref: paymentData.userId },
        amount: paymentData.amount,
        currency: paymentData.currency,
        method: paymentData.method,
        status: paymentData.status,
        metadata: paymentData.metadata,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })
    } catch (error) {
      console.error('Failed to save payment record:', error)
      throw error
    }
  }

  // Update payment status
  static async updatePaymentStatus(
    paymentId: string,
    status: string,
    metadata?: any
  ): Promise<void> {
    try {
      const payment = await client.fetch(
        groq`*[_type == "payment" && paymentId == $paymentId][0]`,
        { paymentId }
      )

      if (payment) {
        await client.patch(payment._id).set({
          status,
          metadata: { ...payment.metadata, ...metadata },
          updatedAt: new Date().toISOString()
        }).commit()
      }
    } catch (error) {
      console.error('Failed to update payment status:', error)
      throw error
    }
  }
}
