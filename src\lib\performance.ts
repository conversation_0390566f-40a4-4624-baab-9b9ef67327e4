// Performance monitoring utilities for RiftStays

export interface PerformanceMetric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  timestamp: number
}

// Performance thresholds based on Core Web Vitals
export const PERFORMANCE_THRESHOLDS = {
  LCP: { good: 2500, poor: 4000 }, // Largest Contentful Paint
  FID: { good: 100, poor: 300 },   // First Input Delay
  CLS: { good: 0.1, poor: 0.25 },  // Cumulative Layout Shift
  FCP: { good: 1800, poor: 3000 }, // First Contentful Paint
  TTFB: { good: 800, poor: 1800 }, // Time to First Byte
}

// Get performance rating based on thresholds
export function getPerformanceRating(
  metric: string,
  value: number
): 'good' | 'needs-improvement' | 'poor' {
  const threshold = PERFORMANCE_THRESHOLDS[metric as keyof typeof PERFORMANCE_THRESHOLDS]
  if (!threshold) return 'good'
  
  if (value <= threshold.good) return 'good'
  if (value <= threshold.poor) return 'needs-improvement'
  return 'poor'
}

// Measure page load performance
export function measurePageLoad(): Promise<PerformanceMetric[]> {
  return new Promise((resolve) => {
    if (typeof window === 'undefined') {
      resolve([])
      return
    }

    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        const metrics: PerformanceMetric[] = []

        if (navigation) {
          // Time to First Byte
          const ttfb = navigation.responseStart - navigation.requestStart
          metrics.push({
            name: 'TTFB',
            value: ttfb,
            rating: getPerformanceRating('TTFB', ttfb),
            timestamp: Date.now(),
          })

          // First Contentful Paint
          const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0]
          if (fcpEntry) {
            metrics.push({
              name: 'FCP',
              value: fcpEntry.startTime,
              rating: getPerformanceRating('FCP', fcpEntry.startTime),
              timestamp: Date.now(),
            })
          }
        }

        resolve(metrics)
      }, 0)
    })
  })
}

// Measure resource loading performance
export function measureResourceLoading(): PerformanceMetric[] {
  if (typeof window === 'undefined') return []

  const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
  const metrics: PerformanceMetric[] = []

  // Group resources by type
  const resourcesByType = resources.reduce((acc, resource) => {
    const type = getResourceType(resource.name)
    if (!acc[type]) acc[type] = []
    acc[type].push(resource)
    return acc
  }, {} as Record<string, PerformanceResourceTiming[]>)

  // Calculate average load time for each resource type
  Object.entries(resourcesByType).forEach(([type, typeResources]) => {
    const avgLoadTime = typeResources.reduce((sum, resource) => {
      return sum + (resource.responseEnd - resource.requestStart)
    }, 0) / typeResources.length

    metrics.push({
      name: `${type}_load_time`,
      value: avgLoadTime,
      rating: avgLoadTime < 1000 ? 'good' : avgLoadTime < 3000 ? 'needs-improvement' : 'poor',
      timestamp: Date.now(),
    })
  })

  return metrics
}

// Get resource type from URL
function getResourceType(url: string): string {
  if (url.includes('.js')) return 'script'
  if (url.includes('.css')) return 'stylesheet'
  if (url.match(/\.(jpg|jpeg|png|gif|webp|avif|svg)$/)) return 'image'
  if (url.match(/\.(woff|woff2|ttf|otf)$/)) return 'font'
  return 'other'
}

// Monitor long tasks
export function monitorLongTasks(callback: (duration: number) => void): () => void {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
    return () => {}
  }

  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.duration > 50) {
        callback(entry.duration)
      }
    }
  })

  observer.observe({ entryTypes: ['longtask'] })

  return () => observer.disconnect()
}

// Monitor layout shifts
export function monitorLayoutShifts(callback: (value: number) => void): () => void {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
    return () => {}
  }

  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      const layoutShift = entry as any
      if (!layoutShift.hadRecentInput) {
        callback(layoutShift.value)
      }
    }
  })

  observer.observe({ entryTypes: ['layout-shift'] })

  return () => observer.disconnect()
}

// Log performance metrics to console (development) or analytics (production)
export function logPerformanceMetric(metric: PerformanceMetric): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Performance] ${metric.name}:`, {
      value: `${metric.value.toFixed(2)}ms`,
      rating: metric.rating,
    })
  } else {
    // Send to analytics service in production
    // Example: Google Analytics, DataDog, etc.
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'performance_metric', {
        event_category: 'Performance',
        event_label: metric.name,
        value: Math.round(metric.value),
        custom_map: {
          rating: metric.rating,
        },
      })
    }
  }
}

// Debounce function for performance optimization
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Throttle function for performance optimization
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}
