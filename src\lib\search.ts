import { client } from '@/sanity/lib/client'
import { groq } from 'next-sanity'
import { Property, PropertySearchFilters } from '@/types/property'
import { Tour, TourSearchFilters } from '@/types/tour'

export interface AdvancedSearchFilters {
  query?: string
  location?: string
  propertyType?: string[]
  tourCategory?: string[]
  priceRange?: {
    min: number
    max: number
  }
  dateRange?: {
    checkIn: string
    checkOut: string
  }
  guests?: {
    adults: number
    children: number
    infants: number
  }
  amenities?: string[]
  difficulty?: string[]
  duration?: {
    min: number
    max: number
  }
  coordinates?: {
    lat: number
    lng: number
    radius: number // in kilometers
  }
  featured?: boolean
  verified?: boolean
  sortBy?: 'price_asc' | 'price_desc' | 'rating' | 'distance' | 'newest'
}

export interface SearchResult {
  properties: Property[]
  tours: Tour[]
  totalProperties: number
  totalTours: number
  facets: {
    locations: Array<{ name: string; count: number }>
    propertyTypes: Array<{ name: string; count: number }>
    tourCategories: Array<{ name: string; count: number }>
    priceRanges: Array<{ range: string; count: number }>
    amenities: Array<{ name: string; count: number }>
  }
}

class SearchService {
  // Advanced property search
  async searchProperties(filters: AdvancedSearchFilters, page = 1, limit = 12): Promise<{
    properties: Property[]
    total: number
    hasMore: boolean
  }> {
    let query = `*[_type == "property" && status == "available"`
    const params: any = {}

    // Text search
    if (filters.query) {
      query += ` && (title match $query || description match $query || location match $query)`
      params.query = `*${filters.query}*`
    }

    // Location filter
    if (filters.location) {
      query += ` && location match $location`
      params.location = `*${filters.location}*`
    }

    // Property type filter
    if (filters.propertyType && filters.propertyType.length > 0) {
      query += ` && propertyType in $propertyTypes`
      params.propertyTypes = filters.propertyType
    }

    // Price range filter
    if (filters.priceRange) {
      if (filters.priceRange.min > 0) {
        query += ` && price >= $minPrice`
        params.minPrice = filters.priceRange.min
      }
      if (filters.priceRange.max > 0) {
        query += ` && price <= $maxPrice`
        params.maxPrice = filters.priceRange.max
      }
    }

    // Guest capacity filter
    if (filters.guests) {
      const totalGuests = filters.guests.adults + filters.guests.children + filters.guests.infants
      if (totalGuests > 0) {
        query += ` && maxGuests >= $totalGuests`
        params.totalGuests = totalGuests
      }
    }

    // Amenities filter
    if (filters.amenities && filters.amenities.length > 0) {
      query += ` && count(amenities[@ in $amenities]) > 0`
      params.amenities = filters.amenities
    }

    // Featured filter
    if (filters.featured !== undefined) {
      query += ` && featured == $featured`
      params.featured = filters.featured
    }

    // Verified filter
    if (filters.verified !== undefined) {
      query += ` && verified == $verified`
      params.verified = filters.verified
    }

    query += `]`

    // Sorting
    let sortClause = ''
    switch (filters.sortBy) {
      case 'price_asc':
        sortClause = ' | order(price asc)'
        break
      case 'price_desc':
        sortClause = ' | order(price desc)'
        break
      case 'rating':
        sortClause = ' | order(analytics.averageRating desc)'
        break
      case 'newest':
        sortClause = ' | order(createdAt desc)'
        break
      default:
        sortClause = ' | order(featured desc, createdAt desc)'
    }

    query += sortClause

    // Pagination
    const offset = (page - 1) * limit
    query += `[${offset}...${offset + limit}]`

    // Add field selection
    query += ` {
      _id,
      _type,
      title,
      slug,
      description,
      images[] {
        _type,
        asset,
        alt
      },
      price,
      location,
      coordinates,
      bedrooms,
      bathrooms,
      maxGuests,
      category,
      propertyType,
      purpose,
      amenities,
      featured,
      verified,
      status,
      analytics,
      createdAt,
      updatedAt
    }`

    const properties = await client.fetch(groq`${query}`, params)

    // Get total count
    let countQuery = query.split(']')[0] + ']'
    const total = await client.fetch(groq`count(${countQuery})`, params)

    return {
      properties,
      total,
      hasMore: total > offset + limit
    }
  }

  // Advanced tour search
  async searchTours(filters: AdvancedSearchFilters, page = 1, limit = 12): Promise<{
    tours: Tour[]
    total: number
    hasMore: boolean
  }> {
    let query = `*[_type == "tour" && active == true`
    const params: any = {}

    // Text search
    if (filters.query) {
      query += ` && (title match $query || description match $query || location match $query)`
      params.query = `*${filters.query}*`
    }

    // Location filter
    if (filters.location) {
      query += ` && location match $location`
      params.location = `*${filters.location}*`
    }

    // Category filter
    if (filters.tourCategory && filters.tourCategory.length > 0) {
      query += ` && category in $categories`
      params.categories = filters.tourCategory
    }

    // Price range filter
    if (filters.priceRange) {
      if (filters.priceRange.min > 0) {
        query += ` && price >= $minPrice`
        params.minPrice = filters.priceRange.min
      }
      if (filters.priceRange.max > 0) {
        query += ` && price <= $maxPrice`
        params.maxPrice = filters.priceRange.max
      }
    }

    // Duration filter
    if (filters.duration) {
      if (filters.duration.min > 0) {
        query += ` && duration >= $minDuration`
        params.minDuration = filters.duration.min
      }
      if (filters.duration.max > 0) {
        query += ` && duration <= $maxDuration`
        params.maxDuration = filters.duration.max
      }
    }

    // Difficulty filter
    if (filters.difficulty && filters.difficulty.length > 0) {
      query += ` && difficulty in $difficulties`
      params.difficulties = filters.difficulty
    }

    // Guest capacity filter
    if (filters.guests) {
      const totalGuests = filters.guests.adults + filters.guests.children + filters.guests.infants
      if (totalGuests > 0) {
        query += ` && maxGuests >= $totalGuests`
        params.totalGuests = totalGuests
      }
    }

    // Featured filter
    if (filters.featured !== undefined) {
      query += ` && featured == $featured`
      params.featured = filters.featured
    }

    query += `]`

    // Sorting
    let sortClause = ''
    switch (filters.sortBy) {
      case 'price_asc':
        sortClause = ' | order(price asc)'
        break
      case 'price_desc':
        sortClause = ' | order(price desc)'
        break
      case 'rating':
        sortClause = ' | order(analytics.averageRating desc)'
        break
      case 'newest':
        sortClause = ' | order(createdAt desc)'
        break
      default:
        sortClause = ' | order(featured desc, createdAt desc)'
    }

    query += sortClause

    // Pagination
    const offset = (page - 1) * limit
    query += `[${offset}...${offset + limit}]`

    // Add field selection
    query += ` {
      _id,
      _type,
      title,
      slug,
      description,
      shortDescription,
      images[] {
        _type,
        asset,
        alt
      },
      price,
      duration,
      maxGuests,
      minGuests,
      difficulty,
      category,
      location,
      coordinates,
      includes,
      excludes,
      featured,
      active,
      analytics,
      createdAt,
      updatedAt
    }`

    const tours = await client.fetch(groq`${query}`, params)

    // Get total count
    let countQuery = query.split(']')[0] + ']'
    const total = await client.fetch(groq`count(${countQuery})`, params)

    return {
      tours,
      total,
      hasMore: total > offset + limit
    }
  }

  // Combined search for both properties and tours
  async search(filters: AdvancedSearchFilters, page = 1, limit = 12): Promise<SearchResult> {
    const [propertiesResult, toursResult, facets] = await Promise.all([
      this.searchProperties(filters, page, Math.ceil(limit / 2)),
      this.searchTours(filters, page, Math.ceil(limit / 2)),
      this.getFacets(filters)
    ])

    return {
      properties: propertiesResult.properties,
      tours: toursResult.tours,
      totalProperties: propertiesResult.total,
      totalTours: toursResult.total,
      facets
    }
  }

  // Get search facets for filtering
  async getFacets(filters: AdvancedSearchFilters): Promise<SearchResult['facets']> {
    const [locations, propertyTypes, tourCategories, amenities] = await Promise.all([
      // Get unique locations
      client.fetch(groq`
        array::unique(*[_type in ["property", "tour"] && defined(location)].location) | order(@)
      `),

      // Get property types with counts
      client.fetch(groq`
        *[_type == "property"] | group(propertyType) {
          "name": propertyType,
          "count": count(*)
        } | order(name)
      `),

      // Get tour categories with counts
      client.fetch(groq`
        *[_type == "tour"] | group(category) {
          "name": category,
          "count": count(*)
        } | order(name)
      `),

      // Get amenities with counts
      client.fetch(groq`
        *[_type == "property" && defined(amenities)] {
          amenities
        }
      `)
    ])

    // Process amenities to get unique values with counts
    const amenityMap = new Map<string, number>()
    amenities.forEach((property: any) => {
      if (property.amenities) {
        property.amenities.forEach((amenity: string) => {
          amenityMap.set(amenity, (amenityMap.get(amenity) || 0) + 1)
        })
      }
    })

    const amenitiesList = Array.from(amenityMap.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => a.name.localeCompare(b.name))

    // Generate price ranges
    const priceRanges = [
      { range: '0-5000', count: 0 },
      { range: '5000-15000', count: 0 },
      { range: '15000-30000', count: 0 },
      { range: '30000-50000', count: 0 },
      { range: '50000+', count: 0 }
    ]

    return {
      locations: locations.map((location: string) => ({ name: location, count: 0 })),
      propertyTypes: propertyTypes || [],
      tourCategories: tourCategories || [],
      priceRanges,
      amenities: amenitiesList
    }
  }

  // Get search suggestions
  async getSearchSuggestions(query: string, limit = 5): Promise<{
    properties: Array<{ id: string; title: string; type: 'property' }>
    tours: Array<{ id: string; title: string; type: 'tour' }>
    locations: Array<{ name: string; type: 'location' }>
  }> {
    if (!query || query.length < 2) {
      return { properties: [], tours: [], locations: [] }
    }

    const [properties, tours, locations] = await Promise.all([
      client.fetch(groq`
        *[_type == "property" && title match $query][0...${limit}] {
          _id,
          title
        }
      `, { query: `*${query}*` }),

      client.fetch(groq`
        *[_type == "tour" && title match $query][0...${limit}] {
          _id,
          title
        }
      `, { query: `*${query}*` }),

      client.fetch(groq`
        array::unique(*[_type in ["property", "tour"] && location match $query].location)[0...${limit}]
      `, { query: `*${query}*` })
    ])

    return {
      properties: properties.map((p: any) => ({ id: p._id, title: p.title, type: 'property' as const })),
      tours: tours.map((t: any) => ({ id: t._id, title: t.title, type: 'tour' as const })),
      locations: locations.map((location: string) => ({ name: location, type: 'location' as const }))
    }
  }

  // Calculate distance between two coordinates
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371 // Radius of the Earth in kilometers
    const dLat = this.deg2rad(lat2 - lat1)
    const dLon = this.deg2rad(lon2 - lon1)
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    const d = R * c // Distance in kilometers
    return d
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI/180)
  }

  // Search by location coordinates
  async searchByLocation(lat: number, lng: number, radius: number, type?: 'property' | 'tour'): Promise<{
    properties: Property[]
    tours: Tour[]
  }> {
    const typeFilter = type ? ` && _type == "${type}"` : ''

    const results = await client.fetch(groq`
      *[_type in ["property", "tour"] && defined(coordinates)${typeFilter}] {
        _id,
        _type,
        title,
        coordinates,
        price,
        location,
        images[0] {
          asset,
          alt
        }
      }
    `)

    // Filter by distance
    const filtered = results.filter((item: any) => {
      if (!item.coordinates) return false
      const distance = this.calculateDistance(
        lat, lng,
        item.coordinates.lat, item.coordinates.lng
      )
      return distance <= radius
    })

    return {
      properties: filtered.filter((item: any) => item._type === 'property'),
      tours: filtered.filter((item: any) => item._type === 'tour')
    }
  }
}

export const searchService = new SearchService()
