import { createClient } from '@sanity/client'
import { v4 as uuidv4 } from 'uuid'

// Sanity client for file uploads
const sanityClient = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION!,
  token: process.env.SANITY_API_TOKEN!,
  useCdn: false
})

export interface UploadResult {
  _id: string
  url: string
  originalFilename: string
  size: number
  mimeType: string
  metadata?: {
    dimensions?: {
      width: number
      height: number
    }
  }
}

export interface UploadOptions {
  folder?: string
  maxSize?: number // in bytes
  allowedTypes?: string[]
  generateThumbnail?: boolean
}

// Default upload options
const DEFAULT_OPTIONS: UploadOptions = {
  folder: 'uploads',
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: [
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ],
  generateThumbnail: true
}

// File validation
export function validateFile(file: File, options: UploadOptions = {}): string | null {
  const opts = { ...DEFAULT_OPTIONS, ...options }
  
  // Check file size
  if (opts.maxSize && file.size > opts.maxSize) {
    return `File size exceeds ${formatFileSize(opts.maxSize)} limit`
  }
  
  // Check file type
  if (opts.allowedTypes && !opts.allowedTypes.includes(file.type)) {
    return `File type ${file.type} is not allowed`
  }
  
  return null
}

// Upload file to Sanity
export async function uploadFile(
  file: File | Buffer,
  filename: string,
  options: UploadOptions = {}
): Promise<UploadResult> {
  const opts = { ...DEFAULT_OPTIONS, ...options }
  
  try {
    // Convert File to Buffer if needed
    let buffer: Buffer
    let originalFilename: string
    let mimeType: string
    
    if (file instanceof File) {
      buffer = Buffer.from(await file.arrayBuffer())
      originalFilename = file.name
      mimeType = file.type
      
      // Validate file
      const validationError = validateFile(file, options)
      if (validationError) {
        throw new Error(validationError)
      }
    } else {
      buffer = file
      originalFilename = filename
      mimeType = getMimeTypeFromFilename(filename)
    }
    
    // Generate unique filename
    const extension = originalFilename.split('.').pop()
    const uniqueFilename = `${uuidv4()}.${extension}`
    
    // Upload to Sanity
    const asset = await sanityClient.assets.upload('file', buffer, {
      filename: uniqueFilename,
      contentType: mimeType
    })
    
    return {
      _id: asset._id,
      url: asset.url,
      originalFilename,
      size: buffer.length,
      mimeType,
      metadata: asset.metadata
    }
  } catch (error) {
    console.error('File upload error:', error)
    throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Upload image with automatic optimization
export async function uploadImage(
  file: File | Buffer,
  filename: string,
  options: UploadOptions = {}
): Promise<UploadResult> {
  const opts = { 
    ...DEFAULT_OPTIONS, 
    ...options,
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
  }
  
  try {
    let buffer: Buffer
    let originalFilename: string
    let mimeType: string
    
    if (file instanceof File) {
      buffer = Buffer.from(await file.arrayBuffer())
      originalFilename = file.name
      mimeType = file.type
      
      // Validate image
      const validationError = validateFile(file, opts)
      if (validationError) {
        throw new Error(validationError)
      }
    } else {
      buffer = file
      originalFilename = filename
      mimeType = getMimeTypeFromFilename(filename)
    }
    
    // Generate unique filename
    const extension = originalFilename.split('.').pop()
    const uniqueFilename = `${uuidv4()}.${extension}`
    
    // Upload to Sanity as image asset
    const asset = await sanityClient.assets.upload('image', buffer, {
      filename: uniqueFilename,
      contentType: mimeType
    })
    
    return {
      _id: asset._id,
      url: asset.url,
      originalFilename,
      size: buffer.length,
      mimeType,
      metadata: asset.metadata
    }
  } catch (error) {
    console.error('Image upload error:', error)
    throw new Error(`Image upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Delete file from Sanity
export async function deleteFile(assetId: string): Promise<void> {
  try {
    await sanityClient.delete(assetId)
  } catch (error) {
    console.error('File deletion error:', error)
    throw new Error(`Delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Get file info from Sanity
export async function getFileInfo(assetId: string): Promise<UploadResult | null> {
  try {
    const asset = await sanityClient.fetch(
      `*[_type == "sanity.fileAsset" && _id == $assetId][0]`,
      { assetId }
    )
    
    if (!asset) return null
    
    return {
      _id: asset._id,
      url: asset.url,
      originalFilename: asset.originalFilename,
      size: asset.size,
      mimeType: asset.mimeType,
      metadata: asset.metadata
    }
  } catch (error) {
    console.error('Get file info error:', error)
    return null
  }
}

// Utility functions
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function getMimeTypeFromFilename(filename: string): string {
  const extension = filename.split('.').pop()?.toLowerCase()
  
  const mimeTypes: { [key: string]: string } = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'txt': 'text/plain'
  }
  
  return mimeTypes[extension || ''] || 'application/octet-stream'
}

export function isImageFile(mimeType: string): boolean {
  return mimeType.startsWith('image/')
}

export function getImageUrl(assetId: string, options?: {
  width?: number
  height?: number
  quality?: number
  format?: 'jpg' | 'png' | 'webp'
}): string {
  const baseUrl = `https://cdn.sanity.io/images/${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID}/${process.env.NEXT_PUBLIC_SANITY_DATASET}/${assetId}`
  
  if (!options) return baseUrl
  
  const params = new URLSearchParams()
  if (options.width) params.set('w', options.width.toString())
  if (options.height) params.set('h', options.height.toString())
  if (options.quality) params.set('q', options.quality.toString())
  if (options.format) params.set('fm', options.format)
  
  return `${baseUrl}?${params.toString()}`
}

// Batch upload multiple files
export async function uploadMultipleFiles(
  files: File[],
  options: UploadOptions = {}
): Promise<UploadResult[]> {
  const results: UploadResult[] = []
  const errors: string[] = []
  
  for (const file of files) {
    try {
      const result = isImageFile(file.type) 
        ? await uploadImage(file, file.name, options)
        : await uploadFile(file, file.name, options)
      results.push(result)
    } catch (error) {
      errors.push(`${file.name}: ${error instanceof Error ? error.message : 'Upload failed'}`)
    }
  }
  
  if (errors.length > 0) {
    console.warn('Some uploads failed:', errors)
  }
  
  return results
}
