import { z } from 'zod'

// Property validation schemas
export const PropertyCategorySchema = z.enum(['beach', 'mountain', 'city', 'countryside'])
export const PropertyTypeSchema = z.enum(['house', 'apartment', 'villa', 'cabin'])
export const PropertyPurposeSchema = z.enum(['rental', 'sale', 'airbnb'])
export const PropertyStatusSchema = z.enum(['available', 'booked', 'maintenance', 'inactive'])

export const PropertyCoordinatesSchema = z.object({
  lat: z.number().min(-90).max(90),
  lng: z.number().min(-180).max(180)
})

export const PropertyImageSchema = z.object({
  _type: z.literal('image'),
  asset: z.object({
    _ref: z.string(),
    _type: z.literal('reference')
  }),
  alt: z.string().optional(),
  caption: z.string().optional()
})

export const PropertyOwnerSchema = z.object({
  name: z.string().min(1, 'Owner name is required'),
  email: z.string().email('Valid email is required'),
  phone: z.string().min(1, 'Phone number is required'),
  userId: z.object({
    _ref: z.string(),
    _type: z.literal('reference')
  }).optional()
})

export const PropertySchema = z.object({
  _id: z.string(),
  _type: z.literal('property'),
  title: z.string().min(1, 'Title is required'),
  slug: z.object({
    _type: z.literal('slug'),
    current: z.string()
  }),
  description: z.string().min(1, 'Description is required'),
  price: z.number().positive('Price must be positive'),
  location: z.string().min(1, 'Location is required'),
  address: z.string().optional(),
  coordinates: PropertyCoordinatesSchema.optional(),
  bedrooms: z.number().min(0),
  bathrooms: z.number().min(0),
  maxGuests: z.number().min(1),
  category: PropertyCategorySchema,
  propertyType: PropertyTypeSchema,
  amenities: z.array(z.string()),
  rules: z.array(z.string()),
  images: z.array(PropertyImageSchema).min(1, 'At least one image is required'),
  featured: z.boolean(),
  status: PropertyStatusSchema,
  verified: z.boolean(),
  owner: PropertyOwnerSchema,
  createdAt: z.string(),
  updatedAt: z.string()
})

// Tour validation schemas
export const TourDifficultySchema = z.enum(['easy', 'moderate', 'challenging', 'expert'])
export const TourCategorySchema = z.enum([
  'adventure', 'cultural', 'nature', 'wildlife', 'historical', 
  'food_drink', 'photography', 'hiking', 'water_sports', 'city_tours'
])

export const TourItineraryItemSchema = z.object({
  time: z.string(),
  activity: z.string(),
  description: z.string()
})

export const TourGuideSchema = z.object({
  name: z.string(),
  bio: z.string().optional(),
  languages: z.array(z.string()),
  experience: z.number().min(0).optional(),
  photo: PropertyImageSchema.optional()
})

export const TourSchema = z.object({
  _id: z.string(),
  _type: z.literal('tour'),
  title: z.string().min(1, 'Title is required'),
  slug: z.object({
    _type: z.literal('slug'),
    current: z.string()
  }),
  description: z.string().min(1, 'Description is required'),
  shortDescription: z.string().optional(),
  images: z.array(PropertyImageSchema).min(1, 'At least one image is required'),
  price: z.number().positive('Price must be positive'),
  duration: z.number().min(1, 'Duration must be at least 1 hour'),
  maxGuests: z.number().min(1),
  minGuests: z.number().min(1),
  difficulty: TourDifficultySchema,
  category: TourCategorySchema,
  location: z.string().min(1, 'Location is required'),
  coordinates: PropertyCoordinatesSchema.optional(),
  includes: z.array(z.string()),
  excludes: z.array(z.string()),
  itinerary: z.array(TourItineraryItemSchema),
  meetingPoint: z.string().min(1, 'Meeting point is required'),
  guide: TourGuideSchema.optional(),
  featured: z.boolean(),
  active: z.boolean(),
  createdAt: z.string(),
  updatedAt: z.string()
})

// Booking validation schemas
export const BookingTypeSchema = z.enum(['tour', 'house', 'hotel', 'airbnb'])
export const BookingStatusSchema = z.enum(['pending', 'confirmed', 'cancelled', 'completed', 'refunded'])
export const PaymentStatusSchema = z.enum(['pending', 'processing', 'completed', 'failed', 'refunded'])

export const BookingGuestSchema = z.object({
  adults: z.number().min(1, 'At least one adult is required'),
  children: z.number().min(0),
  infants: z.number().min(0)
})

export const BookingContactSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Valid email is required'),
  phone: z.string().min(1, 'Phone number is required'),
  country: z.string().min(1, 'Country is required')
})

export const BookingDatesSchema = z.object({
  checkIn: z.string(),
  checkOut: z.string(),
  nights: z.number().min(1).optional()
}).refine(data => {
  const checkIn = new Date(data.checkIn)
  const checkOut = new Date(data.checkOut)
  return checkOut > checkIn
}, {
  message: 'Check-out date must be after check-in date'
})

export const BookingPricingSchema = z.object({
  basePrice: z.number().min(0),
  serviceFee: z.number().min(0),
  taxes: z.number().min(0),
  discounts: z.number().min(0),
  totalPrice: z.number().min(0),
  currency: z.string().default('KES')
})

export const PaymentDetailsSchema = z.object({
  method: z.enum(['mpesa', 'card', 'bank']),
  status: PaymentStatusSchema,
  transactionId: z.string().optional(),
  mpesaReceiptNumber: z.string().optional(),
  mpesaCheckoutRequestId: z.string().optional(),
  amount: z.number().min(0),
  currency: z.string().default('KES'),
  paidAt: z.string().optional()
})

export const BookingRequestSchema = z.object({
  type: BookingTypeSchema,
  propertyId: z.string().optional(),
  tourId: z.string().optional(),
  dates: BookingDatesSchema.optional(),
  tourDate: z.string().optional(),
  tourTime: z.string().optional(),
  guests: BookingGuestSchema,
  contact: BookingContactSchema,
  specialRequests: z.string().optional(),
  additionalServices: z.object({
    airportTransfer: z.boolean().optional(),
    carRental: z.boolean().optional(),
    insurance: z.boolean().optional(),
    mealPlan: z.string().optional()
  }).optional()
}).refine(data => {
  if (data.type === 'tour') {
    return data.tourId && data.tourDate && data.tourTime
  } else {
    return data.propertyId && data.dates
  }
}, {
  message: 'Required fields missing for booking type'
})

// User validation schemas
export const UserRoleSchema = z.enum(['admin', 'employee', 'customer'])
export const UserStatusSchema = z.enum(['active', 'inactive', 'suspended'])

export const UserSchema = z.object({
  _id: z.string(),
  email: z.string().email(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  role: UserRoleSchema,
  status: UserStatusSchema,
  phone: z.string().optional(),
  department: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string()
})

// Settings validation schema
export const SettingsSchema = z.object({
  _id: z.string(),
  _type: z.literal('settings'),
  title: z.string().min(1, 'Site title is required'),
  description: z.string().min(1, 'Site description is required'),
  logo: PropertyImageSchema.optional(),
  twitterHandle: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string()
})

// Form validation helpers
export const validatePropertyForm = (data: unknown) => {
  return PropertySchema.safeParse(data)
}

export const validateTourForm = (data: unknown) => {
  return TourSchema.safeParse(data)
}

export const validateBookingRequest = (data: unknown) => {
  return BookingRequestSchema.safeParse(data)
}

export const validateUserForm = (data: unknown) => {
  return UserSchema.safeParse(data)
}

export const validateSettingsForm = (data: unknown) => {
  return SettingsSchema.safeParse(data)
}

// All schemas are already exported above
