import { formatPrice } from '@/lib/utils'

interface PropertyPreviewProps {
  title: string
  media: any
  price: number
  category: string
  propertyType: string
  featured: boolean
  location: string
}

export function PropertyPreview({
  title,
  media,
  price,
  category,
  propertyType,
  featured,
  location,
}: PropertyPreviewProps) {
  return (
    <div className="flex items-center gap-4 p-2">
      {media && (
        <div className="h-16 w-16 flex-shrink-0 overflow-hidden rounded-md">
          <img
            src={media.asset.url}
            alt={title}
            className="h-full w-full object-cover"
          />
        </div>
      )}
      <div className="flex-1 min-w-0">
        <p className="truncate font-medium text-gray-900">{title}</p>
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <span>{location}</span>
          <span>•</span>
          <span>{formatPrice(price)}/night</span>
          <span>•</span>
          <span className="capitalize">{propertyType}</span>
          <span>•</span>
          <span className="capitalize">{category}</span>
          {featured && (
            <>
              <span>•</span>
              <span className="text-indigo-600">Featured</span>
            </>
          )}
        </div>
      </div>
    </div>
  )
} 