import createImageUrlBuilder from '@sanity/image-url'
import type { Image } from 'sanity'

import { dataset, projectId } from '../env'

const imageBuilder = createImageUrlBuilder({
  projectId: projectId || '',
  dataset: dataset || '',
})

export const urlForImage = (source: Image) => {
  return imageBuilder?.image(source).auto('format').fit('max').quality(85)
}

// Optimized image URL with specific dimensions and quality
export const urlForImageWithDimensions = (
  source: Image,
  width: number,
  height?: number,
  quality: number = 85
) => {
  let builder = imageBuilder?.image(source).auto('format').quality(quality).width(width)

  if (height) {
    builder = builder.height(height).fit('crop').crop('center')
  }

  return builder
}

// Generate responsive image URLs
export const generateResponsiveImageUrls = (source: Image, quality: number = 85) => {
  const sizes = [320, 640, 768, 1024, 1280, 1920]

  return sizes.map(size => ({
    width: size,
    url: urlForImageWithDimensions(source, size, undefined, quality)?.url() || '',
  }))
}

// Generate blur placeholder
export const generateBlurDataURL = (source: Image) => {
  return urlForImageWithDimensions(source, 20, 20, 20)?.blur(50).url() || ''
}