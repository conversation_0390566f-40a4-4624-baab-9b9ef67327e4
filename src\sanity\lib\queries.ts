import { client } from './client'
import { Settings } from '@/types/settings'
import { Property, PropertyListItem } from '@/types/property'
import { Tour } from '@/types/tour'
import { Booking } from '@/types/booking'
import { User } from '@/types/auth'
import { BlogPost } from '@/types/blog'
import { groq } from 'next-sanity'

export async function getSettings(): Promise<Settings> {
  return client.fetch(
    groq`*[_type == "settings"][0] {
      _id,
      _type,
      title,
      description,
      logo,
      twitterHandle,
      seo,
      contactInfo,
      paymentSettings,
      bookingSettings,
      mainNavigation,
      footerNavigation,
      socialLinks,
      copyright,
      createdAt,
      updatedAt
    }`
  )
}

export async function getProperties(): Promise<Property[]> {
  return client.fetch(
    groq`*[_type == "property"] | order(featured desc, createdAt desc) {
      _id,
      _type,
      title,
      slug,
      description,
      price,
      location,
      address,
      coordinates,
      bedrooms,
      bathrooms,
      maxGuests,
      category,
      propertyType,
      amenities,
      rules,
      availability,
      images[] {
        _type,
        asset,
        alt,
        caption
      },
      featured,
      status,
      verified,
      owner,
      contact,
      pricing,
      seo,
      analytics,
      createdAt,
      updatedAt
    }`
  )
}

export async function getPropertiesForListing(): Promise<PropertyListItem[]> {
  return client.fetch(
    groq`*[_type == "property" && status == "available"] | order(featured desc, createdAt desc) {
      _id,
      title,
      slug,
      description,
      price,
      location,
      bedrooms,
      bathrooms,
      maxGuests,
      category,
      propertyType,
      images[] {
        _type,
        asset,
        alt,
        caption
      },
      featured,
      status,
      verified
    }`
  )
}

export async function getPropertyBySlug(slug: string): Promise<Property> {
  return client.fetch(
    groq`*[_type == "property" && slug.current == $slug][0] {
      _id,
      _type,
      title,
      slug,
      description,
      price,
      location,
      address,
      coordinates,
      bedrooms,
      bathrooms,
      maxGuests,
      category,
      propertyType,
      amenities,
      rules,
      availability,
      images[] {
        _type,
        asset,
        alt,
        caption
      },
      featured,
      status,
      verified,
      owner,
      contact,
      pricing,
      seo,
      analytics,
      createdAt,
      updatedAt
    }`,
    { slug }
  )
}

// Tour Queries
export async function getTours(): Promise<Tour[]> {
  return client.fetch(
    groq`*[_type == "tour" && active == true] | order(featured desc, createdAt desc) {
      _id,
      _type,
      title,
      slug,
      description,
      shortDescription,
      images[] {
        _type,
        asset,
        alt
      },
      price,
      duration,
      maxGuests,
      minGuests,
      difficulty,
      category,
      location,
      coordinates,
      includes,
      excludes,
      itinerary,
      meetingPoint,
      cancellationPolicy,
      availability,
      guide,
      safety,
      booking,
      seo,
      featured,
      active,
      createdAt,
      updatedAt
    }`
  )
}

export async function getTourBySlug(slug: string): Promise<Tour> {
  return client.fetch(
    groq`*[_type == "tour" && slug.current == $slug][0] {
      _id,
      _type,
      title,
      slug,
      description,
      shortDescription,
      images[] {
        _type,
        asset,
        alt
      },
      price,
      duration,
      maxGuests,
      minGuests,
      difficulty,
      category,
      location,
      coordinates,
      includes,
      excludes,
      itinerary,
      meetingPoint,
      cancellationPolicy,
      availability,
      guide,
      safety,
      booking,
      seo,
      featured,
      active,
      createdAt,
      updatedAt
    }`,
    { slug }
  )
}

// Booking Queries
export async function getBookings(): Promise<Booking[]> {
  return client.fetch(
    groq`*[_type == "booking"] | order(createdAt desc) {
      _id,
      bookingNumber,
      type,
      status,
      contact,
      guests,
      tourBooking,
      accommodationBooking,
      pricing,
      payment,
      additionalServices,
      customerNotes,
      internalNotes,
      createdAt,
      updatedAt,
      confirmedAt,
      cancelledAt,
      cancellationReason
    }`
  )
}

export async function getBookingByNumber(bookingNumber: string): Promise<Booking> {
  return client.fetch(
    groq`*[_type == "booking" && bookingNumber == $bookingNumber][0] {
      _id,
      bookingNumber,
      type,
      status,
      contact,
      guests,
      tourBooking {
        ...,
        tourId-> {
          _id,
          title,
          slug,
          price,
          duration,
          location
        }
      },
      accommodationBooking {
        ...,
        propertyId-> {
          _id,
          title,
          slug,
          price,
          location,
          propertyType
        }
      },
      pricing,
      payment,
      additionalServices,
      customerNotes,
      internalNotes,
      createdAt,
      updatedAt,
      confirmedAt,
      cancelledAt,
      cancellationReason
    }`,
    { bookingNumber }
  )
}

// User Queries
export async function getUsers(): Promise<User[]> {
  return client.fetch(
    groq`*[_type == "user"] | order(createdAt desc) {
      _id,
      email,
      firstName,
      lastName,
      role,
      status,
      avatar,
      phone,
      department,
      permissions,
      profile,
      preferences,
      verification,
      lastLogin,
      createdAt,
      updatedAt
    }`
  )
}

export async function getUserByEmail(email: string): Promise<User> {
  return client.fetch(
    groq`*[_type == "user" && email == $email][0] {
      _id,
      email,
      firstName,
      lastName,
      role,
      status,
      avatar,
      phone,
      department,
      permissions,
      profile,
      preferences,
      verification,
      lastLogin,
      createdAt,
      updatedAt
    }`,
    { email }
  )
}

export async function getBlogPosts(): Promise<BlogPost[]> {
  return client.fetch(
    groq`*[_type == "blogPost"] | order(publishedAt desc) {
      _id,
      title,
      slug,
      excerpt,
      mainImage {
        asset-> {
          url
        }
      },
      publishedAt,
      author-> {
        name,
        image {
          asset-> {
            url
          }
        }
      }
    }`
  )
}

export async function getBlogPost(slug: string): Promise<BlogPost> {
  return client.fetch(
    groq`*[_type == "blogPost" && slug.current == $slug][0] {
      _id,
      title,
      slug,
      excerpt,
      mainImage {
        asset-> {
          url
        }
      },
      body,
      publishedAt,
      author-> {
        name,
        bio,
        image {
          asset-> {
            url
          }
        }
      },
      categories[]-> {
        title,
        slug
      }
    }`,
    { slug }
  )
}