export const structure = (S: any) =>
  S.list()
    .title('Content')
    .items([
      S.listItem()
        .title('Properties')
        .child(
          S.list()
            .title('Properties')
            .items([
              S.listItem()
                .title('All Properties')
                .child(S.documentList().title('All Properties').filter('_type == "property"')),
              S.listItem()
                .title('Featured Properties')
                .child(
                  S.documentList()
                    .title('Featured Properties')
                    .filter('_type == "property" && featured == true')
                ),
              S.listItem()
                .title('Pending Properties')
                .child(S.documentList().title('Pending Properties').filter('_type == "pendingProperty"')),
              S.listItem()
                .title('By Type')
                .child(
                  S.list()
                    .title('By Type')
                    .items([
                      S.listItem()
                        .title('Houses')
                        .child(
                          S.documentList()
                            .title('Houses')
                            .filter('_type == "property" && propertyType == "house"')
                        ),
                      S.listItem()
                        .title('Apartments')
                        .child(
                          S.documentList()
                            .title('Apartments')
                            .filter('_type == "property" && propertyType == "apartment"')
                        ),
                      S.listItem()
                        .title('Villas')
                        .child(
                          S.documentList()
                            .title('Villas')
                            .filter('_type == "property" && propertyType == "villa"')
                        ),
                      S.listItem()
                        .title('Cabins')
                        .child(
                          S.documentList()
                            .title('Cabins')
                            .filter('_type == "property" && propertyType == "cabin"')
                        ),
                    ])
                ),
            ])
        ),
      S.listItem()
        .title('Tours')
        .child(
          S.list()
            .title('Tours')
            .items([
              S.listItem()
                .title('All Tours')
                .child(S.documentList().title('All Tours').filter('_type == "tour"')),
              S.listItem()
                .title('Featured Tours')
                .child(
                  S.documentList()
                    .title('Featured Tours')
                    .filter('_type == "tour" && featured == true')
                ),
              S.listItem()
                .title('By Difficulty')
                .child(
                  S.list()
                    .title('By Difficulty')
                    .items([
                      S.listItem()
                        .title('Easy')
                        .child(
                          S.documentList()
                            .title('Easy Tours')
                            .filter('_type == "tour" && difficulty == "easy"')
                        ),
                      S.listItem()
                        .title('Moderate')
                        .child(
                          S.documentList()
                            .title('Moderate Tours')
                            .filter('_type == "tour" && difficulty == "moderate"')
                        ),
                      S.listItem()
                        .title('Challenging')
                        .child(
                          S.documentList()
                            .title('Challenging Tours')
                            .filter('_type == "tour" && difficulty == "challenging"')
                        ),
                    ])
                ),
            ])
        ),
      S.listItem()
        .title('Bookings')
        .child(
          S.list()
            .title('Bookings')
            .items([
              S.listItem()
                .title('All Bookings')
                .child(S.documentList().title('All Bookings').filter('_type == "booking"')),
              S.listItem()
                .title('Pending Bookings')
                .child(
                  S.documentList()
                    .title('Pending Bookings')
                    .filter('_type == "booking" && status == "pending"')
                ),
              S.listItem()
                .title('Confirmed Bookings')
                .child(
                  S.documentList()
                    .title('Confirmed Bookings')
                    .filter('_type == "booking" && status == "confirmed"')
                ),
              S.listItem()
                .title('Completed Bookings')
                .child(
                  S.documentList()
                    .title('Completed Bookings')
                    .filter('_type == "booking" && status == "completed"')
                ),
            ])
        ),
      S.listItem()
        .title('Users')
        .child(
          S.list()
            .title('Users')
            .items([
              S.listItem()
                .title('All Users')
                .child(S.documentList().title('All Users').filter('_type == "user"')),
              S.listItem()
                .title('Admins')
                .child(
                  S.documentList()
                    .title('Admins')
                    .filter('_type == "user" && role == "admin"')
                ),
              S.listItem()
                .title('Employees')
                .child(
                  S.documentList()
                    .title('Employees')
                    .filter('_type == "user" && role == "employee"')
                ),
              S.listItem()
                .title('Customers')
                .child(
                  S.documentList()
                    .title('Customers')
                    .filter('_type == "user" && role == "customer"')
                ),
            ])
        ),
      S.listItem()
        .title('Blog')
        .child(
          S.list()
            .title('Blog')
            .items([
              S.listItem()
                .title('All Posts')
                .child(S.documentList().title('All Posts').filter('_type == "blogPost"')),
              S.listItem()
                .title('Authors')
                .child(S.documentList().title('Authors').filter('_type == "author"')),
              S.listItem()
                .title('Categories')
                .child(S.documentList().title('Categories').filter('_type == "category"')),
            ])
        ),
      S.divider(),
      S.listItem()
        .title('Settings')
        .child(
          S.list()
            .title('Settings')
            .items([
              S.listItem()
                .title('Site Settings')
                .child(
                  S.document()
                    .schemaType('settings')
                    .documentId('settings')
                ),
            ])
        ),
    ])