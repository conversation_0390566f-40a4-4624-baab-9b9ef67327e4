export default {
  name: 'booking',
  title: 'Booking',
  type: 'document',
  fields: [
    {
      name: 'bookingNumber',
      title: 'Booking Number',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'type',
      title: 'Booking Type',
      type: 'string',
      options: {
        list: [
          { title: 'Tour', value: 'tour' },
          { title: 'House', value: 'house' },
          { title: 'Hotel', value: 'hotel' },
          { title: 'Airbnb', value: 'airbnb' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          { title: 'Pending', value: 'pending' },
          { title: 'Confirmed', value: 'confirmed' },
          { title: 'Cancelled', value: 'cancelled' },
          { title: 'Completed', value: 'completed' },
          { title: 'Refunded', value: 'refunded' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'contact',
      title: 'Contact Information',
      type: 'object',
      fields: [
        {
          name: 'firstName',
          title: 'First Name',
          type: 'string',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'lastName',
          title: 'Last Name',
          type: 'string',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'email',
          title: 'Email',
          type: 'string',
          validation: (Rule: any) => Rule.required().email(),
        },
        {
          name: 'phone',
          title: 'Phone',
          type: 'string',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'country',
          title: 'Country',
          type: 'string',
        },
      ],
    },
    {
      name: 'guests',
      title: 'Guests',
      type: 'object',
      fields: [
        {
          name: 'adults',
          title: 'Adults',
          type: 'number',
          validation: (Rule: any) => Rule.required().min(1),
        },
        {
          name: 'children',
          title: 'Children',
          type: 'number',
          initialValue: 0,
        },
        {
          name: 'infants',
          title: 'Infants',
          type: 'number',
          initialValue: 0,
        },
      ],
    },
    {
      name: 'tourBooking',
      title: 'Tour Booking Details',
      type: 'object',
      hidden: ({ document }: any) => document?.type !== 'tour',
      fields: [
        {
          name: 'tourId',
          title: 'Tour',
          type: 'reference',
          to: [{ type: 'tour' }],
        },
        {
          name: 'date',
          title: 'Tour Date',
          type: 'date',
        },
        {
          name: 'time',
          title: 'Tour Time',
          type: 'string',
        },
        {
          name: 'duration',
          title: 'Duration (hours)',
          type: 'number',
        },
        {
          name: 'specialRequests',
          title: 'Special Requests',
          type: 'text',
        },
      ],
    },
    {
      name: 'accommodationBooking',
      title: 'Accommodation Booking Details',
      type: 'object',
      hidden: ({ document }: any) => document?.type === 'tour',
      fields: [
        {
          name: 'propertyId',
          title: 'Property',
          type: 'reference',
          to: [{ type: 'property' }],
        },
        {
          name: 'checkIn',
          title: 'Check-in Date',
          type: 'date',
        },
        {
          name: 'checkOut',
          title: 'Check-out Date',
          type: 'date',
        },
        {
          name: 'nights',
          title: 'Number of Nights',
          type: 'number',
        },
        {
          name: 'rooms',
          title: 'Number of Rooms',
          type: 'number',
          initialValue: 1,
        },
        {
          name: 'specialRequests',
          title: 'Special Requests',
          type: 'text',
        },
      ],
    },
    {
      name: 'pricing',
      title: 'Pricing',
      type: 'object',
      fields: [
        {
          name: 'basePrice',
          title: 'Base Price',
          type: 'number',
          validation: (Rule: any) => Rule.required().min(0),
        },
        {
          name: 'serviceFee',
          title: 'Service Fee',
          type: 'number',
          initialValue: 0,
        },
        {
          name: 'taxes',
          title: 'Taxes',
          type: 'number',
          initialValue: 0,
        },
        {
          name: 'discounts',
          title: 'Discounts',
          type: 'number',
          initialValue: 0,
        },
        {
          name: 'totalPrice',
          title: 'Total Price',
          type: 'number',
          validation: (Rule: any) => Rule.required().min(0),
        },
        {
          name: 'currency',
          title: 'Currency',
          type: 'string',
          initialValue: 'KES',
        },
      ],
    },
    {
      name: 'payment',
      title: 'Payment Details',
      type: 'object',
      fields: [
        {
          name: 'method',
          title: 'Payment Method',
          type: 'string',
          options: {
            list: [
              { title: 'M-Pesa', value: 'mpesa' },
              { title: 'Card', value: 'card' },
              { title: 'Bank Transfer', value: 'bank' },
            ],
          },
        },
        {
          name: 'status',
          title: 'Payment Status',
          type: 'string',
          options: {
            list: [
              { title: 'Pending', value: 'pending' },
              { title: 'Processing', value: 'processing' },
              { title: 'Completed', value: 'completed' },
              { title: 'Failed', value: 'failed' },
              { title: 'Refunded', value: 'refunded' },
            ],
          },
        },
        {
          name: 'transactionId',
          title: 'Transaction ID',
          type: 'string',
        },
        {
          name: 'mpesaReceiptNumber',
          title: 'M-Pesa Receipt Number',
          type: 'string',
        },
        {
          name: 'mpesaCheckoutRequestId',
          title: 'M-Pesa Checkout Request ID',
          type: 'string',
        },
        {
          name: 'amount',
          title: 'Amount Paid',
          type: 'number',
        },
        {
          name: 'paidAt',
          title: 'Paid At',
          type: 'datetime',
        },
      ],
    },
    {
      name: 'additionalServices',
      title: 'Additional Services',
      type: 'object',
      fields: [
        {
          name: 'airportTransfer',
          title: 'Airport Transfer',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'carRental',
          title: 'Car Rental',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'insurance',
          title: 'Travel Insurance',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'mealPlan',
          title: 'Meal Plan',
          type: 'string',
          options: {
            list: [
              { title: 'None', value: 'none' },
              { title: 'Breakfast Only', value: 'breakfast' },
              { title: 'Half Board', value: 'half-board' },
              { title: 'Full Board', value: 'full-board' },
              { title: 'All Inclusive', value: 'all-inclusive' },
            ],
          },
        },
      ],
    },
    {
      name: 'customerNotes',
      title: 'Customer Notes',
      type: 'text',
    },
    {
      name: 'internalNotes',
      title: 'Internal Notes',
      type: 'text',
    },
    {
      name: 'createdAt',
      title: 'Created At',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
    },
    {
      name: 'updatedAt',
      title: 'Updated At',
      type: 'datetime',
    },
    {
      name: 'confirmedAt',
      title: 'Confirmed At',
      type: 'datetime',
    },
    {
      name: 'cancelledAt',
      title: 'Cancelled At',
      type: 'datetime',
    },
    {
      name: 'cancellationReason',
      title: 'Cancellation Reason',
      type: 'text',
    },
  ],
  preview: {
    select: {
      bookingNumber: 'bookingNumber',
      type: 'type',
      status: 'status',
      customerName: 'contact.firstName',
      totalPrice: 'pricing.totalPrice',
    },
    prepare(selection: any) {
      const { bookingNumber, type, status, customerName, totalPrice } = selection
      return {
        title: `${bookingNumber} - ${customerName}`,
        subtitle: `${type} | ${status} | KES ${totalPrice}`,
      }
    },
  },
}
