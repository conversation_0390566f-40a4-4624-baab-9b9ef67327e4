export default {
  name: 'pendingProperty',
  title: 'Pending Property',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'price',
      title: 'Price per Night',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'location',
      title: 'Location',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'bedrooms',
      title: 'Bedrooms',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'bathrooms',
      title: 'Bathrooms',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'maxGuests',
      title: 'Maximum Guests',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(1),
    },
    {
      name: 'category',
      title: 'Category',
      type: 'string',
      options: {
        list: [
          { title: 'Beach', value: 'beach' },
          { title: 'Mountain', value: 'mountain' },
          { title: 'City', value: 'city' },
          { title: 'Countryside', value: 'countryside' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'propertyType',
      title: 'Property Type',
      type: 'string',
      options: {
        list: [
          { title: 'House', value: 'house' },
          { title: 'Apartment', value: 'apartment' },
          { title: 'Villa', value: 'villa' },
          { title: 'Cabin', value: 'cabin' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'purpose',
      title: 'Purpose',
      type: 'string',
      options: {
        list: [
          { title: 'For Rent', value: 'rental' },
          { title: 'For Sale', value: 'sale' },
          { title: 'Airbnb/Short-term', value: 'airbnb' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'amenities',
      title: 'Amenities',
      type: 'array',
      of: [{ type: 'string' }],
      options: {
        list: [
          { title: 'WiFi', value: 'WiFi' },
          { title: 'Pool', value: 'Pool' },
          { title: 'Kitchen', value: 'Kitchen' },
          { title: 'Parking', value: 'Parking' },
          { title: 'Air Conditioning', value: 'Air Conditioning' },
          { title: 'TV', value: 'TV' },
        ],
      },
    },
    {
      name: 'rules',
      title: 'House Rules',
      type: 'array',
      of: [{ type: 'string' }],
    },
    {
      name: 'availability',
      title: 'Availability',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'startDate',
              title: 'Start Date',
              type: 'date',
              validation: (Rule: any) => Rule.required(),
            },
            {
              name: 'endDate',
              title: 'End Date',
              type: 'date',
              validation: (Rule: any) => Rule.required(),
            },
          ],
        },
      ],
    },
    {
      name: 'images',
      title: 'Images',
      type: 'array',
      of: [{ type: 'image' }],
      validation: (Rule: any) => Rule.required().min(1),
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          { title: 'Pending', value: 'pending' },
          { title: 'Approved', value: 'approved' },
          { title: 'Rejected', value: 'rejected' },
        ],
      },
      initialValue: 'pending',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'submittedAt',
      title: 'Submitted At',
      type: 'datetime',
      validation: (Rule: any) => Rule.required(),
    },
  ],
  preview: {
    select: {
      title: 'title',
      location: 'location',
      status: 'status',
    },
    prepare({ title, location, status }: { title: string; location: string; status: string }) {
      return {
        title,
        subtitle: `${location} - ${status}`,
      }
    },
  },
} 