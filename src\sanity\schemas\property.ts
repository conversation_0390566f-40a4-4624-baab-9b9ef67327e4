import { PropertyPreview } from '../components/PropertyPreview'

export default {
  name: 'property',
  title: 'Property',
  type: 'document',
  preview: {
    select: {
      title: 'title',
      media: 'images.0',
      price: 'price',
      category: 'category',
      propertyType: 'propertyType',
      featured: 'featured',
      location: 'location',
    },
    component: PropertyPreview,
  },
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'price',
      title: 'Price per Night',
      type: 'number',
      validation: (Rule: any) => Rule.required().positive(),
    },
    {
      name: 'location',
      title: 'Location',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'address',
      title: 'Full Address',
      type: 'text',
      rows: 3,
      description: 'Complete address including street, city, and postal code',
    },
    {
      name: 'coordinates',
      title: 'Coordinates',
      type: 'object',
      fields: [
        {
          name: 'lat',
          title: 'Latitude',
          type: 'number',
          validation: (Rule: any) => Rule.min(-90).max(90),
        },
        {
          name: 'lng',
          title: 'Longitude',
          type: 'number',
          validation: (Rule: any) => Rule.min(-180).max(180),
        },
      ],
    },
    {
      name: 'bedrooms',
      title: 'Bedrooms',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'bathrooms',
      title: 'Bathrooms',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'maxGuests',
      title: 'Maximum Guests',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(1),
    },
    {
      name: 'category',
      title: 'Category',
      type: 'string',
      options: {
        list: [
          { title: 'Beach', value: 'beach' },
          { title: 'Mountain', value: 'mountain' },
          { title: 'City', value: 'city' },
          { title: 'Countryside', value: 'countryside' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'propertyType',
      title: 'Property Type',
      type: 'string',
      options: {
        list: [
          { title: 'House', value: 'house' },
          { title: 'Apartment', value: 'apartment' },
          { title: 'Villa', value: 'villa' },
          { title: 'Cabin', value: 'cabin' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'purpose',
      title: 'Purpose',
      type: 'string',
      options: {
        list: [
          { title: 'For Rent', value: 'rental' },
          { title: 'For Sale', value: 'sale' },
          { title: 'Airbnb/Short-term', value: 'airbnb' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'amenities',
      title: 'Amenities',
      type: 'array',
      of: [{ type: 'string' }],
      options: {
        list: [
          { title: 'WiFi', value: 'wifi' },
          { title: 'Swimming Pool', value: 'pool' },
          { title: 'Full Kitchen', value: 'kitchen' },
          { title: 'Parking', value: 'parking' },
          { title: 'Air Conditioning', value: 'ac' },
          { title: 'TV/Cable', value: 'tv' },
          { title: 'Washing Machine', value: 'washing_machine' },
          { title: 'Dryer', value: 'dryer' },
          { title: 'Dishwasher', value: 'dishwasher' },
          { title: 'Microwave', value: 'microwave' },
          { title: 'Refrigerator', value: 'refrigerator' },
          { title: 'Balcony/Patio', value: 'balcony' },
          { title: 'Garden', value: 'garden' },
          { title: 'Gym/Fitness Center', value: 'gym' },
          { title: 'Hot Tub/Jacuzzi', value: 'hot_tub' },
          { title: 'Fireplace', value: 'fireplace' },
          { title: 'BBQ Grill', value: 'bbq' },
          { title: 'Security System', value: 'security' },
          { title: 'Elevator', value: 'elevator' },
          { title: 'Pet Friendly', value: 'pet_friendly' },
          { title: 'Wheelchair Accessible', value: 'wheelchair_accessible' },
          { title: 'Beach Access', value: 'beach_access' },
          { title: 'Mountain View', value: 'mountain_view' },
          { title: 'Ocean View', value: 'ocean_view' },
          { title: 'City View', value: 'city_view' },
        ],
      },
    },
    {
      name: 'rules',
      title: 'House Rules',
      type: 'array',
      of: [{ type: 'string' }],
    },
    {
      name: 'availability',
      title: 'Availability',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'startDate',
              title: 'Start Date',
              type: 'date',
              validation: (Rule: any) => Rule.required(),
            },
            {
              name: 'endDate',
              title: 'End Date',
              type: 'date',
              validation: (Rule: any) => Rule.required(),
            },
          ],
        },
      ],
    },
    {
      name: 'images',
      title: 'Images',
      type: 'array',
      of: [
        {
          type: 'image',
          options: { hotspot: true },
          fields: [
            {
              name: 'alt',
              title: 'Alt Text',
              type: 'string',
              description: 'Important for SEO and accessibility',
            },
            {
              name: 'caption',
              title: 'Caption',
              type: 'string',
            },
          ],
        },
      ],
      validation: (Rule: any) => Rule.required().min(1).max(20),
    },
    {
      name: 'featured',
      title: 'Featured Property',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'status',
      title: 'Property Status',
      type: 'string',
      options: {
        list: [
          { title: 'Available', value: 'available' },
          { title: 'Booked', value: 'booked' },
          { title: 'Maintenance', value: 'maintenance' },
          { title: 'Inactive', value: 'inactive' },
        ],
      },
      initialValue: 'available',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'verified',
      title: 'Verified Property',
      type: 'boolean',
      initialValue: false,
      description: 'Has this property been verified by our team?',
    },
    {
      name: 'owner',
      title: 'Property Owner',
      type: 'object',
      fields: [
        {
          name: 'name',
          title: 'Owner Name',
          type: 'string',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'email',
          title: 'Owner Email',
          type: 'string',
          validation: (Rule: any) => Rule.required().email(),
        },
        {
          name: 'phone',
          title: 'Owner Phone',
          type: 'string',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'userId',
          title: 'User Account',
          type: 'reference',
          to: [{ type: 'user' }],
          description: 'Link to the owner\'s user account',
        },
      ],
    },
    {
      name: 'contact',
      title: 'Contact Information',
      type: 'object',
      fields: [
        {
          name: 'checkInTime',
          title: 'Check-in Time',
          type: 'string',
          placeholder: 'e.g., 3:00 PM',
        },
        {
          name: 'checkOutTime',
          title: 'Check-out Time',
          type: 'string',
          placeholder: 'e.g., 11:00 AM',
        },
        {
          name: 'emergencyContact',
          title: 'Emergency Contact',
          type: 'string',
        },
        {
          name: 'localContact',
          title: 'Local Contact/Caretaker',
          type: 'string',
        },
      ],
    },
    {
      name: 'pricing',
      title: 'Pricing Details',
      type: 'object',
      fields: [
        {
          name: 'weeklyDiscount',
          title: 'Weekly Discount (%)',
          type: 'number',
          validation: (Rule: any) => Rule.min(0).max(50),
        },
        {
          name: 'monthlyDiscount',
          title: 'Monthly Discount (%)',
          type: 'number',
          validation: (Rule: any) => Rule.min(0).max(50),
        },
        {
          name: 'cleaningFee',
          title: 'Cleaning Fee',
          type: 'number',
          validation: (Rule: any) => Rule.min(0),
        },
        {
          name: 'securityDeposit',
          title: 'Security Deposit',
          type: 'number',
          validation: (Rule: any) => Rule.min(0),
        },
        {
          name: 'extraGuestFee',
          title: 'Extra Guest Fee (per person)',
          type: 'number',
          validation: (Rule: any) => Rule.min(0),
        },
      ],
    },
    {
      name: 'seo',
      title: 'SEO Settings',
      type: 'object',
      fields: [
        {
          name: 'metaTitle',
          title: 'Meta Title',
          type: 'string',
          validation: (Rule: any) => Rule.max(60),
          description: 'Recommended: 50-60 characters',
        },
        {
          name: 'metaDescription',
          title: 'Meta Description',
          type: 'text',
          rows: 3,
          validation: (Rule: any) => Rule.max(160),
          description: 'Recommended: 150-160 characters',
        },
        {
          name: 'keywords',
          title: 'Keywords',
          type: 'array',
          of: [{ type: 'string' }],
          description: 'SEO keywords for this property',
        },
      ],
    },
    {
      name: 'analytics',
      title: 'Analytics',
      type: 'object',
      readOnly: true,
      fields: [
        {
          name: 'views',
          title: 'Total Views',
          type: 'number',
          initialValue: 0,
        },
        {
          name: 'bookings',
          title: 'Total Bookings',
          type: 'number',
          initialValue: 0,
        },
        {
          name: 'rating',
          title: 'Average Rating',
          type: 'number',
          validation: (Rule: any) => Rule.min(0).max(5),
        },
        {
          name: 'reviewCount',
          title: 'Number of Reviews',
          type: 'number',
          initialValue: 0,
        },
      ],
    },
  ],
}