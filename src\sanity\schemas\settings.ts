export default {
  name: 'settings',
  title: 'Settings',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Site Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Site Description',
      type: 'text',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'logo',
      title: 'Logo',
      type: 'image',
      options: {
        hotspot: true,
      },
    },
    {
      name: 'twitterHandle',
      title: 'Twitter Handle',
      type: 'string',
    },
    {
      name: 'contactInfo',
      title: 'Contact Information',
      type: 'object',
      fields: [
        {
          name: 'email',
          title: 'Email',
          type: 'string',
          validation: (Rule: any) => Rule.email(),
        },
        {
          name: 'phone',
          title: 'Phone',
          type: 'string',
        },
        {
          name: 'address',
          title: 'Address',
          type: 'text',
          rows: 3,
        },
        {
          name: 'businessHours',
          title: 'Business Hours',
          type: 'string',
        },
        {
          name: 'emergencyContact',
          title: 'Emergency Contact',
          type: 'string',
        },
      ],
    },
    {
      name: 'seo',
      title: 'SEO Settings',
      type: 'object',
      fields: [
        {
          name: 'defaultTitle',
          title: 'Default Title',
          type: 'string',
          validation: (Rule: any) => Rule.max(60),
        },
        {
          name: 'defaultDescription',
          title: 'Default Description',
          type: 'text',
          validation: (Rule: any) => Rule.max(160),
        },
        {
          name: 'defaultImage',
          title: 'Default Image',
          type: 'image',
          options: {
            hotspot: true,
          },
        },
        {
          name: 'keywords',
          title: 'Default Keywords',
          type: 'array',
          of: [{ type: 'string' }],
        },
        {
          name: 'googleAnalyticsId',
          title: 'Google Analytics ID',
          type: 'string',
        },
        {
          name: 'googleTagManagerId',
          title: 'Google Tag Manager ID',
          type: 'string',
        },
        {
          name: 'facebookPixelId',
          title: 'Facebook Pixel ID',
          type: 'string',
        },
      ],
    },
    {
      name: 'paymentSettings',
      title: 'Payment Settings',
      type: 'object',
      fields: [
        {
          name: 'mpesaEnabled',
          title: 'Enable M-Pesa',
          type: 'boolean',
          initialValue: true,
        },
        {
          name: 'mpesaShortcode',
          title: 'M-Pesa Shortcode',
          type: 'string',
        },
        {
          name: 'mpesaPasskey',
          title: 'M-Pesa Passkey',
          type: 'string',
          description: 'Keep this secure - used for API authentication',
        },
        {
          name: 'stripeEnabled',
          title: 'Enable Stripe',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'stripePublishableKey',
          title: 'Stripe Publishable Key',
          type: 'string',
        },
        {
          name: 'paypalEnabled',
          title: 'Enable PayPal',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'bankTransferEnabled',
          title: 'Enable Bank Transfer',
          type: 'boolean',
          initialValue: true,
        },
        {
          name: 'bankDetails',
          title: 'Bank Details',
          type: 'object',
          fields: [
            {
              name: 'bankName',
              title: 'Bank Name',
              type: 'string',
            },
            {
              name: 'accountName',
              title: 'Account Name',
              type: 'string',
            },
            {
              name: 'accountNumber',
              title: 'Account Number',
              type: 'string',
            },
            {
              name: 'swiftCode',
              title: 'SWIFT Code',
              type: 'string',
            },
          ],
        },
      ],
    },
    {
      name: 'bookingSettings',
      title: 'Booking Settings',
      type: 'object',
      fields: [
        {
          name: 'defaultCancellationPolicy',
          title: 'Default Cancellation Policy',
          type: 'text',
          rows: 4,
        },
        {
          name: 'advanceBookingHours',
          title: 'Advance Booking Required (hours)',
          type: 'number',
          initialValue: 24,
          validation: (Rule: any) => Rule.min(1),
        },
        {
          name: 'maxBookingDays',
          title: 'Maximum Booking Days in Advance',
          type: 'number',
          initialValue: 365,
          validation: (Rule: any) => Rule.min(1),
        },
        {
          name: 'requireDeposit',
          title: 'Require Deposit',
          type: 'boolean',
          initialValue: true,
        },
        {
          name: 'depositPercentage',
          title: 'Deposit Percentage',
          type: 'number',
          initialValue: 30,
          validation: (Rule: any) => Rule.min(0).max(100),
        },
        {
          name: 'autoConfirmBookings',
          title: 'Auto-confirm Bookings',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'sendConfirmationEmails',
          title: 'Send Confirmation Emails',
          type: 'boolean',
          initialValue: true,
        },
        {
          name: 'sendReminderEmails',
          title: 'Send Reminder Emails',
          type: 'boolean',
          initialValue: true,
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'title',
    },
  },
} 