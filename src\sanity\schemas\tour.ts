export default {
  name: 'tour',
  title: 'Tour',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Tour Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 4,
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'shortDescription',
      title: 'Short Description',
      type: 'text',
      rows: 2,
      description: 'Brief description for listings',
    },
    {
      name: 'images',
      title: 'Images',
      type: 'array',
      of: [
        {
          type: 'image',
          options: {
            hotspot: true,
          },
          fields: [
            {
              name: 'alt',
              title: 'Alt Text',
              type: 'string',
            },
          ],
        },
      ],
      validation: (Rule: any) => Rule.min(1).required(),
    },
    {
      name: 'price',
      title: 'Price (KES)',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'duration',
      title: 'Duration (hours)',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(1),
    },
    {
      name: 'maxGuests',
      title: 'Maximum Guests',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(1),
    },
    {
      name: 'minGuests',
      title: 'Minimum Guests',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(1),
    },
    {
      name: 'difficulty',
      title: 'Difficulty Level',
      type: 'string',
      options: {
        list: [
          { title: 'Easy', value: 'easy' },
          { title: 'Moderate', value: 'moderate' },
          { title: 'Challenging', value: 'challenging' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'category',
      title: 'Category',
      type: 'string',
      options: {
        list: [
          { title: 'Wildlife Safari', value: 'wildlife' },
          { title: 'Cultural Tour', value: 'cultural' },
          { title: 'Adventure', value: 'adventure' },
          { title: 'City Tour', value: 'city' },
          { title: 'Nature Walk', value: 'nature' },
          { title: 'Beach Activity', value: 'beach' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'location',
      title: 'Location',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'coordinates',
      title: 'Coordinates',
      type: 'object',
      fields: [
        {
          name: 'lat',
          title: 'Latitude',
          type: 'number',
        },
        {
          name: 'lng',
          title: 'Longitude',
          type: 'number',
        },
      ],
    },
    {
      name: 'includes',
      title: 'What\'s Included',
      type: 'array',
      of: [{ type: 'string' }],
    },
    {
      name: 'excludes',
      title: 'What\'s Not Included',
      type: 'array',
      of: [{ type: 'string' }],
    },
    {
      name: 'itinerary',
      title: 'Itinerary',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'time',
              title: 'Time',
              type: 'string',
            },
            {
              name: 'activity',
              title: 'Activity',
              type: 'string',
            },
            {
              name: 'description',
              title: 'Description',
              type: 'text',
            },
          ],
        },
      ],
    },
    {
      name: 'meetingPoint',
      title: 'Meeting Point',
      type: 'text',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'cancellationPolicy',
      title: 'Cancellation Policy',
      type: 'text',
    },
    {
      name: 'availability',
      title: 'Availability',
      type: 'object',
      fields: [
        {
          name: 'daysOfWeek',
          title: 'Available Days',
          type: 'array',
          of: [
            {
              type: 'string',
              options: {
                list: [
                  { title: 'Sunday', value: '0' },
                  { title: 'Monday', value: '1' },
                  { title: 'Tuesday', value: '2' },
                  { title: 'Wednesday', value: '3' },
                  { title: 'Thursday', value: '4' },
                  { title: 'Friday', value: '5' },
                  { title: 'Saturday', value: '6' },
                ],
              },
            },
          ],
        },
        {
          name: 'times',
          title: 'Available Times',
          type: 'array',
          of: [{ type: 'string' }],
          description: 'Available start times (e.g., 08:00, 14:00)',
        },
        {
          name: 'blackoutDates',
          title: 'Blackout Dates',
          type: 'array',
          of: [{ type: 'date' }],
          description: 'Dates when tour is not available',
        },
      ],
    },
    {
      name: 'guide',
      title: 'Tour Guide Information',
      type: 'object',
      fields: [
        {
          name: 'name',
          title: 'Guide Name',
          type: 'string',
        },
        {
          name: 'bio',
          title: 'Guide Bio',
          type: 'text',
          rows: 3,
        },
        {
          name: 'languages',
          title: 'Languages Spoken',
          type: 'array',
          of: [{ type: 'string' }],
          options: {
            list: [
              { title: 'English', value: 'english' },
              { title: 'Swahili', value: 'swahili' },
              { title: 'French', value: 'french' },
              { title: 'German', value: 'german' },
              { title: 'Spanish', value: 'spanish' },
              { title: 'Italian', value: 'italian' },
            ],
          },
        },
        {
          name: 'experience',
          title: 'Years of Experience',
          type: 'number',
          validation: (Rule: any) => Rule.min(0),
        },
        {
          name: 'photo',
          title: 'Guide Photo',
          type: 'image',
          options: { hotspot: true },
        },
      ],
    },
    {
      name: 'safety',
      title: 'Safety Information',
      type: 'object',
      fields: [
        {
          name: 'requirements',
          title: 'Safety Requirements',
          type: 'array',
          of: [{ type: 'string' }],
          description: 'List safety requirements for participants',
        },
        {
          name: 'equipment',
          title: 'Safety Equipment Provided',
          type: 'array',
          of: [{ type: 'string' }],
        },
        {
          name: 'restrictions',
          title: 'Age/Health Restrictions',
          type: 'text',
          rows: 3,
        },
        {
          name: 'insurance',
          title: 'Insurance Coverage',
          type: 'text',
          rows: 2,
        },
      ],
    },
    {
      name: 'booking',
      title: 'Booking Settings',
      type: 'object',
      fields: [
        {
          name: 'advanceBooking',
          title: 'Advance Booking Required (hours)',
          type: 'number',
          initialValue: 24,
          validation: (Rule: any) => Rule.min(1),
        },
        {
          name: 'instantBooking',
          title: 'Allow Instant Booking',
          type: 'boolean',
          initialValue: true,
        },
        {
          name: 'groupDiscounts',
          title: 'Group Discounts',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                {
                  name: 'minGuests',
                  title: 'Minimum Guests',
                  type: 'number',
                },
                {
                  name: 'discount',
                  title: 'Discount (%)',
                  type: 'number',
                  validation: (Rule: any) => Rule.min(0).max(50),
                },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'seo',
      title: 'SEO Settings',
      type: 'object',
      fields: [
        {
          name: 'metaTitle',
          title: 'Meta Title',
          type: 'string',
          validation: (Rule: any) => Rule.max(60),
        },
        {
          name: 'metaDescription',
          title: 'Meta Description',
          type: 'text',
          rows: 3,
          validation: (Rule: any) => Rule.max(160),
        },
        {
          name: 'keywords',
          title: 'Keywords',
          type: 'array',
          of: [{ type: 'string' }],
        },
      ],
    },
    {
      name: 'featured',
      title: 'Featured Tour',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'active',
      title: 'Active',
      type: 'boolean',
      initialValue: true,
    },
  ],
  preview: {
    select: {
      title: 'title',
      media: 'images.0',
      location: 'location',
      price: 'price',
    },
    prepare(selection: any) {
      const { title, media, location, price } = selection
      return {
        title,
        subtitle: `${location} - KES ${price}`,
        media,
      }
    },
  },
}
