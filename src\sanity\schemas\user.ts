export default {
  name: 'user',
  title: 'User',
  type: 'document',
  fields: [
    {
      name: 'email',
      title: 'Email',
      type: 'string',
      validation: (Rule: any) => Rule.required().email(),
    },
    {
      name: 'password',
      title: 'Password',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
      hidden: true, // Hide in Sanity Studio for security
    },
    {
      name: 'firstName',
      title: 'First Name',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'lastName',
      title: 'Last Name',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'role',
      title: 'Role',
      type: 'string',
      options: {
        list: [
          { title: 'Admin', value: 'admin' },
          { title: 'Employee', value: 'employee' },
          { title: 'Customer', value: 'customer' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          { title: 'Active', value: 'active' },
          { title: 'Inactive', value: 'inactive' },
          { title: 'Suspended', value: 'suspended' },
        ],
      },
      initialValue: 'active',
    },
    {
      name: 'avatar',
      title: 'Avatar',
      type: 'image',
      options: {
        hotspot: true,
      },
    },
    {
      name: 'phone',
      title: 'Phone Number',
      type: 'string',
    },
    {
      name: 'department',
      title: 'Department',
      type: 'string',
      options: {
        list: [
          { title: 'Operations', value: 'operations' },
          { title: 'Sales', value: 'sales' },
          { title: 'Marketing', value: 'marketing' },
          { title: 'Customer Service', value: 'customer_service' },
          { title: 'Finance', value: 'finance' },
          { title: 'IT', value: 'it' },
        ],
      },
    },
    {
      name: 'permissions',
      title: 'Permissions',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'resource',
              title: 'Resource',
              type: 'string',
              options: {
                list: [
                  { title: 'Bookings', value: 'bookings' },
                  { title: 'Properties', value: 'properties' },
                  { title: 'Tours', value: 'tours' },
                  { title: 'Users', value: 'users' },
                  { title: 'Content', value: 'content' },
                  { title: 'Analytics', value: 'analytics' },
                  { title: 'Settings', value: 'settings' },
                  { title: 'Financial', value: 'financial' },
                ],
              },
            },
            {
              name: 'actions',
              title: 'Actions',
              type: 'array',
              of: [
                {
                  type: 'string',
                  options: {
                    list: [
                      { title: 'View', value: 'view' },
                      { title: 'Create', value: 'create' },
                      { title: 'Update', value: 'update' },
                      { title: 'Delete', value: 'delete' },
                      { title: 'Approve', value: 'approve' },
                      { title: 'Manage', value: 'manage' },
                      { title: 'Export', value: 'export' },
                      { title: 'Publish', value: 'publish' },
                    ],
                  },
                },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'profile',
      title: 'Profile Information',
      type: 'object',
      fields: [
        {
          name: 'dateOfBirth',
          title: 'Date of Birth',
          type: 'date',
        },
        {
          name: 'nationality',
          title: 'Nationality',
          type: 'string',
        },
        {
          name: 'address',
          title: 'Address',
          type: 'object',
          fields: [
            {
              name: 'street',
              title: 'Street Address',
              type: 'string',
            },
            {
              name: 'city',
              title: 'City',
              type: 'string',
            },
            {
              name: 'state',
              title: 'State/Province',
              type: 'string',
            },
            {
              name: 'postalCode',
              title: 'Postal Code',
              type: 'string',
            },
            {
              name: 'country',
              title: 'Country',
              type: 'string',
              initialValue: 'Kenya',
            },
          ],
        },
        {
          name: 'emergencyContact',
          title: 'Emergency Contact',
          type: 'object',
          fields: [
            {
              name: 'name',
              title: 'Contact Name',
              type: 'string',
            },
            {
              name: 'relationship',
              title: 'Relationship',
              type: 'string',
            },
            {
              name: 'phone',
              title: 'Phone Number',
              type: 'string',
            },
          ],
        },
      ],
    },
    {
      name: 'preferences',
      title: 'User Preferences',
      type: 'object',
      fields: [
        {
          name: 'language',
          title: 'Preferred Language',
          type: 'string',
          options: {
            list: [
              { title: 'English', value: 'en' },
              { title: 'Swahili', value: 'sw' },
              { title: 'French', value: 'fr' },
            ],
          },
          initialValue: 'en',
        },
        {
          name: 'currency',
          title: 'Preferred Currency',
          type: 'string',
          options: {
            list: [
              { title: 'Kenyan Shilling (KES)', value: 'KES' },
              { title: 'US Dollar (USD)', value: 'USD' },
              { title: 'Euro (EUR)', value: 'EUR' },
              { title: 'British Pound (GBP)', value: 'GBP' },
            ],
          },
          initialValue: 'KES',
        },
        {
          name: 'notifications',
          title: 'Notification Preferences',
          type: 'object',
          fields: [
            {
              name: 'email',
              title: 'Email Notifications',
              type: 'boolean',
              initialValue: true,
            },
            {
              name: 'sms',
              title: 'SMS Notifications',
              type: 'boolean',
              initialValue: false,
            },
            {
              name: 'marketing',
              title: 'Marketing Communications',
              type: 'boolean',
              initialValue: false,
            },
          ],
        },
      ],
    },
    {
      name: 'verification',
      title: 'Verification Status',
      type: 'object',
      fields: [
        {
          name: 'emailVerified',
          title: 'Email Verified',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'phoneVerified',
          title: 'Phone Verified',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'identityVerified',
          title: 'Identity Verified',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'verificationDocuments',
          title: 'Verification Documents',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                {
                  name: 'type',
                  title: 'Document Type',
                  type: 'string',
                  options: {
                    list: [
                      { title: 'National ID', value: 'national_id' },
                      { title: 'Passport', value: 'passport' },
                      { title: 'Driver\'s License', value: 'drivers_license' },
                    ],
                  },
                },
                {
                  name: 'document',
                  title: 'Document',
                  type: 'file',
                },
                {
                  name: 'verified',
                  title: 'Verified',
                  type: 'boolean',
                  initialValue: false,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'lastLogin',
      title: 'Last Login',
      type: 'datetime',
      readOnly: true,
    },
    {
      name: 'createdAt',
      title: 'Created At',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
      readOnly: true,
    },
    {
      name: 'updatedAt',
      title: 'Updated At',
      type: 'datetime',
      readOnly: true,
    },
  ],
  preview: {
    select: {
      title: 'firstName',
      subtitle: 'email',
      role: 'role',
      status: 'status',
      media: 'avatar',
    },
    prepare(selection: any) {
      const { title, subtitle, role, status, media } = selection
      return {
        title: `${title} (${role})`,
        subtitle: `${subtitle} - ${status}`,
        media,
      }
    },
  },
}
