// Authentication and User Management Types

export type UserRole = 'admin' | 'employee' | 'customer'
export type UserStatus = 'active' | 'inactive' | 'suspended'

export interface UserAddress {
  street?: string
  city?: string
  state?: string
  postalCode?: string
  country: string
}

export interface UserEmergencyContact {
  name?: string
  relationship?: string
  phone?: string
}

export interface UserProfile {
  dateOfBirth?: string
  nationality?: string
  address?: UserAddress
  emergencyContact?: UserEmergencyContact
}

export interface UserNotificationPreferences {
  email: boolean
  sms: boolean
  marketing: boolean
}

export interface UserPreferences {
  language: string
  currency: string
  notifications: UserNotificationPreferences
}

export interface UserVerificationDocument {
  type: 'national_id' | 'passport' | 'drivers_license'
  document: {
    asset: {
      _ref: string
      _type: 'reference'
    }
  }
  verified: boolean
}

export interface UserVerification {
  emailVerified: boolean
  phoneVerified: boolean
  identityVerified: boolean
  verificationDocuments: UserVerificationDocument[]
}

export interface User {
  _id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  status: UserStatus
  avatar?: {
    asset: {
      url: string
    }
  }
  phone?: string
  department?: string
  permissions: Permission[]
  profile?: UserProfile
  preferences?: UserPreferences
  verification?: UserVerification
  lastLogin?: string
  createdAt: string
  updatedAt: string
}

export interface Permission {
  resource: string
  actions: string[]
}

export interface AuthSession {
  user: User
  token: string
  expiresAt: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface CreateUserRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  role: UserRole
  phone?: string
  department?: string
  permissions: Permission[]
}

// Dashboard Analytics Types
export interface DashboardStats {
  totalBookings: number
  totalRevenue: number
  totalProperties: number
  totalTours: number
  totalUsers: number
  pendingBookings: number
  todayBookings: number
  monthlyRevenue: number
  conversionRate: number
  averageBookingValue: number
}

export interface RevenueData {
  date: string
  amount: number
  bookings: number
}

export interface BookingTrend {
  month: string
  tours: number
  properties: number
  total: number
}

export interface TopPerformer {
  id: string
  name: string
  type: 'tour' | 'property'
  bookings: number
  revenue: number
}

// Permission Constants
export const PERMISSIONS = {
  // Bookings
  BOOKINGS_VIEW: 'bookings:view',
  BOOKINGS_CREATE: 'bookings:create',
  BOOKINGS_UPDATE: 'bookings:update',
  BOOKINGS_DELETE: 'bookings:delete',
  BOOKINGS_MANAGE_PAYMENTS: 'bookings:manage_payments',

  // Properties
  PROPERTIES_VIEW: 'properties:view',
  PROPERTIES_CREATE: 'properties:create',
  PROPERTIES_UPDATE: 'properties:update',
  PROPERTIES_DELETE: 'properties:delete',
  PROPERTIES_APPROVE: 'properties:approve',

  // Tours
  TOURS_VIEW: 'tours:view',
  TOURS_CREATE: 'tours:create',
  TOURS_UPDATE: 'tours:update',
  TOURS_DELETE: 'tours:delete',
  TOURS_MANAGE_SCHEDULE: 'tours:manage_schedule',

  // Users
  USERS_VIEW: 'users:view',
  USERS_CREATE: 'users:create',
  USERS_UPDATE: 'users:update',
  USERS_DELETE: 'users:delete',
  USERS_MANAGE_ROLES: 'users:manage_roles',

  // Content
  CONTENT_VIEW: 'content:view',
  CONTENT_CREATE: 'content:create',
  CONTENT_UPDATE: 'content:update',
  CONTENT_DELETE: 'content:delete',
  CONTENT_PUBLISH: 'content:publish',

  // Analytics
  ANALYTICS_VIEW: 'analytics:view',
  ANALYTICS_EXPORT: 'analytics:export',

  // Settings
  SETTINGS_VIEW: 'settings:view',
  SETTINGS_UPDATE: 'settings:update',
  SETTINGS_MANAGE_INTEGRATIONS: 'settings:manage_integrations',

  // Financial
  FINANCIAL_VIEW: 'financial:view',
  FINANCIAL_MANAGE: 'financial:manage',
  FINANCIAL_REPORTS: 'financial:reports',
} as const

// Default Role Permissions
export const DEFAULT_PERMISSIONS = {
  admin: [
    PERMISSIONS.BOOKINGS_VIEW,
    PERMISSIONS.BOOKINGS_CREATE,
    PERMISSIONS.BOOKINGS_UPDATE,
    PERMISSIONS.BOOKINGS_DELETE,
    PERMISSIONS.BOOKINGS_MANAGE_PAYMENTS,
    PERMISSIONS.PROPERTIES_VIEW,
    PERMISSIONS.PROPERTIES_CREATE,
    PERMISSIONS.PROPERTIES_UPDATE,
    PERMISSIONS.PROPERTIES_DELETE,
    PERMISSIONS.PROPERTIES_APPROVE,
    PERMISSIONS.TOURS_VIEW,
    PERMISSIONS.TOURS_CREATE,
    PERMISSIONS.TOURS_UPDATE,
    PERMISSIONS.TOURS_DELETE,
    PERMISSIONS.TOURS_MANAGE_SCHEDULE,
    PERMISSIONS.USERS_VIEW,
    PERMISSIONS.USERS_CREATE,
    PERMISSIONS.USERS_UPDATE,
    PERMISSIONS.USERS_DELETE,
    PERMISSIONS.USERS_MANAGE_ROLES,
    PERMISSIONS.CONTENT_VIEW,
    PERMISSIONS.CONTENT_CREATE,
    PERMISSIONS.CONTENT_UPDATE,
    PERMISSIONS.CONTENT_DELETE,
    PERMISSIONS.CONTENT_PUBLISH,
    PERMISSIONS.ANALYTICS_VIEW,
    PERMISSIONS.ANALYTICS_EXPORT,
    PERMISSIONS.SETTINGS_VIEW,
    PERMISSIONS.SETTINGS_UPDATE,
    PERMISSIONS.SETTINGS_MANAGE_INTEGRATIONS,
    PERMISSIONS.FINANCIAL_VIEW,
    PERMISSIONS.FINANCIAL_MANAGE,
    PERMISSIONS.FINANCIAL_REPORTS,
  ],
  employee: [
    PERMISSIONS.BOOKINGS_VIEW,
    PERMISSIONS.BOOKINGS_CREATE,
    PERMISSIONS.BOOKINGS_UPDATE,
    PERMISSIONS.PROPERTIES_VIEW,
    PERMISSIONS.PROPERTIES_CREATE,
    PERMISSIONS.PROPERTIES_UPDATE,
    PERMISSIONS.TOURS_VIEW,
    PERMISSIONS.TOURS_CREATE,
    PERMISSIONS.TOURS_UPDATE,
    PERMISSIONS.TOURS_MANAGE_SCHEDULE,
    PERMISSIONS.CONTENT_VIEW,
    PERMISSIONS.CONTENT_CREATE,
    PERMISSIONS.CONTENT_UPDATE,
    PERMISSIONS.ANALYTICS_VIEW,
    PERMISSIONS.SETTINGS_VIEW,
    PERMISSIONS.FINANCIAL_VIEW,
  ],
  customer: [
    PERMISSIONS.BOOKINGS_VIEW,
  ],
}
