import { PortableTextBlock } from '@portabletext/types'

export interface BlogPost {
  _id: string
  title: string
  slug: {
    current: string
  }
  excerpt?: string
  mainImage?: {
    asset: {
      url: string
    }
  }
  body?: PortableTextBlock[]
  publishedAt?: string
  author?: {
    name: string
    bio?: string
    image?: {
      asset: {
        url: string
      }
    }
  }
  categories?: {
    title: string
    slug: {
      current: string
    }
  }[]
}