// Booking System Types for RiftStays

export type BookingType = 'tour' | 'house' | 'hotel' | 'airbnb'
export type BookingStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'refunded'
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded'

export interface BookingGuest {
  adults: number
  children: number
  infants: number
}

export interface BookingDates {
  checkIn: string // ISO date string
  checkOut: string // ISO date string
  nights?: number
}

export interface TourBooking {
  tourId: string
  tourName: string
  date: string // ISO date string
  time: string // HH:MM format
  duration: number // hours
  guests: BookingGuest
  specialRequests?: string
}

export interface AccommodationBooking {
  propertyId: string
  propertyName: string
  propertyType: 'house' | 'hotel' | 'airbnb'
  dates: BookingDates
  guests: BookingGuest
  rooms?: number
  specialRequests?: string
}

export interface BookingPricing {
  basePrice: number
  serviceFee: number
  taxes: number
  discounts: number
  totalPrice: number
  currency: string
}

export interface BookingContact {
  firstName: string
  lastName: string
  email: string
  phone: string
  country: string
}

export interface PaymentDetails {
  method: 'mpesa' | 'card' | 'bank'
  status: PaymentStatus
  transactionId?: string
  mpesaReceiptNumber?: string
  amount: number
  currency: string
  paidAt?: string
}

export interface Booking {
  _id: string
  bookingNumber: string
  type: BookingType
  status: BookingStatus
  
  // Booking details (union type based on booking type)
  tourBooking?: TourBooking
  accommodationBooking?: AccommodationBooking
  
  // Common fields
  contact: BookingContact
  pricing: BookingPricing
  payment: PaymentDetails
  
  // Metadata
  createdAt: string
  updatedAt: string
  confirmedAt?: string
  cancelledAt?: string
  cancellationReason?: string
  
  // Additional services
  additionalServices?: {
    airportTransfer?: boolean
    carRental?: boolean
    insurance?: boolean
    mealPlan?: string
  }
  
  // Internal notes
  internalNotes?: string
  customerNotes?: string
}

export interface BookingRequest {
  type: BookingType
  propertyId?: string
  tourId?: string
  dates?: BookingDates
  tourDate?: string
  tourTime?: string
  guests: BookingGuest
  contact: BookingContact
  specialRequests?: string
  additionalServices?: {
    airportTransfer?: boolean
    carRental?: boolean
    insurance?: boolean
    mealPlan?: string
  }
}

export interface BookingAvailability {
  available: boolean
  date: string
  price?: number
  minStay?: number
  maxStay?: number
  restrictions?: string[]
}

export interface BookingCalendar {
  month: number
  year: number
  availability: BookingAvailability[]
}

// Tour specific types
export interface Tour {
  _id: string
  title: string
  slug: { current: string }
  description: string
  shortDescription: string
  images: Array<{
    asset: { url: string }
    alt?: string
  }>
  price: number
  duration: number // hours
  maxGuests: number
  minGuests: number
  difficulty: 'easy' | 'moderate' | 'challenging'
  category: string
  location: string
  coordinates?: {
    lat: number
    lng: number
  }
  includes: string[]
  excludes: string[]
  itinerary: Array<{
    time: string
    activity: string
    description: string
  }>
  meetingPoint: string
  cancellationPolicy: string
  availability: {
    daysOfWeek: number[] // 0-6 (Sunday-Saturday)
    times: string[] // Available start times
    blackoutDates: string[] // ISO date strings
  }
  featured: boolean
  active: boolean
  createdAt: string
  updatedAt: string
}

// Booking validation
export interface BookingValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Booking search filters
export interface BookingSearchFilters {
  type?: BookingType
  location?: string
  dateRange?: {
    start: string
    end: string
  }
  guests?: BookingGuest
  priceRange?: {
    min: number
    max: number
  }
  amenities?: string[]
  propertyType?: string
  tourCategory?: string
  difficulty?: string
}
