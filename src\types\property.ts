import { SanityImageSource } from '@sanity/image-url/lib/types/types'
import type { Image } from 'sanity'

export type PropertyCategory = 'beach' | 'mountain' | 'city' | 'countryside'
export type PropertyType = 'house' | 'apartment' | 'villa' | 'cabin'
export type PropertyPurpose = 'rental' | 'sale' | 'airbnb'
export type PropertyStatus = 'available' | 'booked' | 'maintenance' | 'inactive'

export interface PropertyImage extends Image {
  alt?: string
  caption?: string
}

// Union type to support both Sanity images and string URLs (for sample data)
export type PropertyImageSource = PropertyImage | string

export interface PropertyCoordinates {
  lat: number
  lng: number
}

export interface PropertyAvailability {
  startDate: string
  endDate: string
}

export interface PropertyOwner {
  name: string
  email: string
  phone: string
  userId?: {
    _ref: string
    _type: 'reference'
  }
}

export interface PropertyContact {
  checkInTime?: string
  checkOutTime?: string
  emergencyContact?: string
  localContact?: string
}

export interface PropertyPricing {
  weeklyDiscount?: number
  monthlyDiscount?: number
  cleaningFee?: number
  securityDeposit?: number
  extraGuestFee?: number
}

export interface PropertySEO {
  metaTitle?: string
  metaDescription?: string
  keywords: string[]
}

export interface PropertyAnalytics {
  views: number
  bookings: number
  rating?: number
  reviewCount: number
}

export interface Property {
  _id: string
  _type: 'property'
  title: string
  slug: {
    _type: 'slug'
    current: string
  }
  description: string
  price: number
  location: string
  address?: string
  coordinates?: PropertyCoordinates
  bedrooms: number
  bathrooms: number
  maxGuests: number
  category: PropertyCategory
  propertyType: PropertyType
  purpose: PropertyPurpose
  amenities: string[]
  rules: string[]
  availability: PropertyAvailability[]
  images: PropertyImageSource[]
  featured: boolean
  status: PropertyStatus
  verified: boolean
  owner: PropertyOwner
  contact?: PropertyContact
  pricing?: PropertyPricing
  seo?: PropertySEO
  analytics?: PropertyAnalytics
  createdAt: string
  updatedAt: string
}

export interface PropertyListItem {
  _id: string
  title: string
  slug: {
    current: string
  }
  description: string
  price: number
  location: string
  bedrooms: number
  bathrooms: number
  maxGuests: number
  category: PropertyCategory
  propertyType: PropertyType
  purpose: PropertyPurpose
  images: PropertyImageSource[]
  featured: boolean
  status: PropertyStatus
  verified: boolean
}

export interface PropertySearchFilters {
  category?: PropertyCategory[]
  propertyType?: PropertyType[]
  purpose?: PropertyPurpose[]
  priceRange?: {
    min: number
    max: number
  }
  bedrooms?: number[]
  bathrooms?: number[]
  maxGuests?: number
  amenities?: string[]
  location?: string
  featured?: boolean
  verified?: boolean
}

export interface PropertyBookingRequest {
  propertyId: string
  checkIn: string
  checkOut: string
  nights: number
  rooms: number
  guests: {
    adults: number
    children: number
    infants: number
  }
  specialRequests?: string
  contact: {
    firstName: string
    lastName: string
    email: string
    phone: string
    country: string
  }
}

export interface PendingProperty extends Omit<Property, '_type' | 'status' | 'verified' | 'analytics'> {
  _type: 'pendingProperty'
  status: 'pending' | 'approved' | 'rejected'
  submittedAt: string
}