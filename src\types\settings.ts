export interface NavLink {
  title: string
  url: string
}

export interface SocialLink {
  platform: string
  url: string
  icon?: string
}

export interface ContactInfo {
  email: string
  phone: string
  address: string
  businessHours?: string
  emergencyContact?: string
}

export interface SEOSettings {
  defaultTitle: string
  defaultDescription: string
  defaultImage: {
    _type: 'image'
    asset: {
      _ref: string
      _type: 'reference'
    }
  }
  keywords?: string[]
  googleAnalyticsId?: string
  googleTagManagerId?: string
  facebookPixelId?: string
}

export interface PaymentSettings {
  mpesaEnabled: boolean
  mpesaShortcode?: string
  mpesaPasskey?: string
  stripeEnabled: boolean
  stripePublishableKey?: string
  paypalEnabled: boolean
  bankTransferEnabled: boolean
  bankDetails?: {
    bankName: string
    accountName: string
    accountNumber: string
    swiftCode?: string
  }
}

export interface BookingSettings {
  defaultCancellationPolicy: string
  advanceBookingHours: number
  maxBookingDays: number
  requireDeposit: boolean
  depositPercentage?: number
  autoConfirmBookings: boolean
  sendConfirmationEmails: boolean
  sendReminderEmails: boolean
}

export interface Settings {
  _id: string
  _type: 'settings'
  title: string
  description: string
  logo?: {
    _type: 'image'
    asset: {
      _ref: string
      _type: 'reference'
    }
  }
  twitterHandle?: string
  seo: SEOSettings
  contactInfo?: ContactInfo
  paymentSettings?: PaymentSettings
  bookingSettings?: BookingSettings
  mainNavigation?: NavLink[]
  footerNavigation?: NavLink[]
  socialLinks?: SocialLink[]
  copyright?: string
  createdAt: string
  updatedAt: string
}