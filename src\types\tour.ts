import { SanityImageSource } from '@sanity/image-url/lib/types/types'
import type { Image } from 'sanity'

export type TourDifficulty = 'easy' | 'moderate' | 'challenging' | 'expert'
export type TourCategory = 'adventure' | 'cultural' | 'nature' | 'wildlife' | 'historical' | 'food_drink' | 'photography' | 'hiking' | 'water_sports' | 'city_tours'

export interface TourImage extends Image {
  alt?: string
}

export interface TourCoordinates {
  lat: number
  lng: number
}

export interface TourItineraryItem {
  time: string
  activity: string
  description: string
}

export interface TourAvailability {
  daysOfWeek: string[]
  times: string[]
  blackoutDates: string[]
}

export interface TourGuide {
  name: string
  bio?: string
  languages: string[]
  experience?: number
  photo?: TourImage
}

export interface TourSafety {
  requirements: string[]
  equipment: string[]
  restrictions?: string
  insurance?: string
}

export interface TourGroupDiscount {
  minGuests: number
  discount: number
}

export interface TourBookingSettings {
  advanceBooking: number
  instantBooking: boolean
  groupDiscounts: TourGroupDiscount[]
}

export interface TourSEO {
  metaTitle?: string
  metaDescription?: string
  keywords: string[]
}

export interface Tour {
  _id: string
  _type: 'tour'
  title: string
  slug: {
    _type: 'slug'
    current: string
  }
  description: string
  shortDescription?: string
  images: TourImage[]
  price: number
  duration: number
  maxGuests: number
  minGuests: number
  difficulty: TourDifficulty
  category: TourCategory
  location: string
  coordinates?: TourCoordinates
  includes: string[]
  excludes: string[]
  itinerary: TourItineraryItem[]
  meetingPoint: string
  cancellationPolicy?: string
  availability: TourAvailability
  guide?: TourGuide
  safety?: TourSafety
  booking?: TourBookingSettings
  seo?: TourSEO
  featured: boolean
  active: boolean
  createdAt: string
  updatedAt: string
}

export interface TourListItem {
  _id: string
  title: string
  slug: {
    current: string
  }
  shortDescription?: string
  images: TourImage[]
  price: number
  duration: number
  difficulty: TourDifficulty
  category: TourCategory
  location: string
  featured: boolean
}

export interface TourSearchFilters {
  category?: TourCategory[]
  difficulty?: TourDifficulty[]
  priceRange?: {
    min: number
    max: number
  }
  duration?: {
    min: number
    max: number
  }
  location?: string
  featured?: boolean
}

export interface TourBookingRequest {
  tourId: string
  date: string
  time: string
  guests: {
    adults: number
    children: number
    infants: number
  }
  specialRequests?: string
  contact: {
    firstName: string
    lastName: string
    email: string
    phone: string
    country: string
  }
}
